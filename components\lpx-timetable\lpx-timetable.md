## Timetable

#### props

| 参数    | 说明                             | 类型            | 默认值  | 可选值                       |
| ----------- | ------------------------------------ | ------------------- | ------- | :------------------------------- |
| timetableType | 定制表格左侧的序号和时间 | Array | 看源码... | -                                |
| week | 定制表格上方的周          | Array         | ['一', '二', '三', '四', '五', '六', '日'] | -                          |
| timetables | 课程内容       | Array              | []     | -                                |
| palette | 调色板, 会优先使用调色板的颜色     | Array              | []     | -                                |



#### event

| 事件名       | 说明               |
| ----------- | ------------------ |
| courseClick | 点击课程时触发, 参数course, 内容如下 |



#### course

```javascript
{
  index: 3,               // 这是第几节课
  length: 2,              // 这节课的课节数
  weekIndex: 4,           // 周数组的下标
  week: '五',             // 周五
  name: '数据结构与算法分析' // 课程名称
}
```



#### 使用

```vue
<template>
	<timetable :timetables="timetables"></timetable>
</template>

<script>
    import Timetable from '@/components/lpx-timetable/lpx-timetable'
	export default {
		data() {
			return {
				timetables: [
		          ['大学英语', '大学英语', '大学英语', '', '', '', '毛概', '毛概', '', '', '', '选修'],
                  ['', '', '信号与系统', '信号与系统', '模拟电子技术基础', '模拟电子技术基础', '', '', '', '', '', ''],
                  ['大学体育', '大学体育', '形势与政策', '形势与政策', '', '', '', '', '', '', '', ''],
                  ['', '', '', '', '电装实习', '电装实习', '', '', '', '大学体育', '大学体育', ''],
                  ['', '', '数据结构与算法分析', '数据结构与算法分析', '', '', '', '', '信号与系统', '信号与系统', '', '']
			    ]
			}
		}
	}
</script>
```
