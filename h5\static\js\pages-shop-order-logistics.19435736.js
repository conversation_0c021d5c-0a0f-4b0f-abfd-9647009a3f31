(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-shop-order-logistics"],{"0a37":function(t,n,e){"use strict";var a=e("969c"),i=e.n(a);i.a},"59da":function(t,n,e){"use strict";e.r(n);var a=e("82a3"),i=e.n(a);for(var r in a)"default"!==r&&function(t){e.d(n,t,(function(){return a[t]}))}(r);n["default"]=i.a},"82a3":function(t,n,e){"use strict";var a=e("4ea4");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,e("96cf");var i=a(e("3b8d")),r={data:function(){return{logistics:""}},onLoad:function(){var t=(0,i.default)(regeneratorRuntime.mark((function t(n){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n.id){t.next=6;break}return t.next=3,this.$api.info("orders",n.id);case 3:e=t.sent,this.logistics=e.data.logistics,console.log(this.logistics);case 6:case"end":return t.stop()}}),t,this)})));function n(n){return t.apply(this,arguments)}return n}(),onUnload:function(){},methods:{}};n.default=r},"969c":function(t,n,e){var a=e("ac23");"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=e("4f06").default;i("2ae64c45",a,!0,{sourceMap:!1,shadowMode:!1})},ab24:function(t,n,e){"use strict";e.r(n);var a=e("c5be"),i=e("59da");for(var r in i)"default"!==r&&function(t){e.d(n,t,(function(){return i[t]}))}(r);e("0a37");var o,c=e("f0c5"),u=Object(c["a"])(i["default"],a["b"],a["c"],!1,null,"11adf512",null,!1,a["a"],o);n["default"]=u.exports},ac23:function(t,n,e){var a=e("24fb");n=a(!1),n.push([t.i,"uni-page-body[data-v-11adf512]{padding-bottom:%?100?%}",""]),t.exports=n},c5be:function(t,n,e){"use strict";var a,i=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("v-uni-view",[e("v-uni-view",{staticClass:"cu-chat"},[e("v-uni-view",{key:t.index,domProps:{innerHTML:t._s(t.logistics)}})],1)],1)},r=[];e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return r})),e.d(n,"a",(function(){return a}))}}]);