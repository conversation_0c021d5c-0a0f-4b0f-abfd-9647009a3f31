<!DOCTYPE html>
<html lang=zh-CN>
	<head>
		<meta charset=utf-8>
		<meta http-equiv=X-UA-Compatible content="IE=edge">
		<title>app02</title>
		<!-- #ifdef H5 -->
		<script src="<%= BASE_URL %>static/js/image-resize.min.js"></script>
		<script src="<%= BASE_URL %>static/js/quill.min.js"></script>
		<!-- #endif -->
		<script>
			document.addEventListener('DOMContentLoaded', function() {
				document.documentElement.style.fontSize = document.documentElement.clientWidth / 20 + 'px'
			})
			var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS
				.supports('top: constant(a)'))
			document.write(
				'<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
				(coverSupport ? ', viewport-fit=cover' : '') + '" />')
		</script>
		<link rel="stylesheet" href="<%= BASE_URL %>static/index.css" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
	</head>
	<body><noscript><strong>Please enable JavaScript to continue.</strong></noscript>
		<div id=app></div>
	</body>
</html>