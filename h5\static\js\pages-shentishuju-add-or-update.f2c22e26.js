(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-shentishuju-add-or-update"],{"13e4":function(e,t,i){"use strict";i.r(t);var r=i("f1cb"),n=i("f7aa");for(var a in n)"default"!==a&&function(e){i.d(t,e,(function(){return n[e]}))}(a);i("a4df");var o,s=i("f0c5"),l=Object(s["a"])(n["default"],r["b"],r["c"],!1,null,"20dbd78b",null,!1,r["a"],o);t["default"]=l.exports},"93ff":function(e,t,i){"use strict";var r=i("4ea4");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a481"),i("f559"),i("ac6a"),i("28a5"),i("c5f6"),i("96cf");var n=r(i("3b8d")),a=r(i("e2b1")),o=r(i("064f")),s=r(i("bd56")),l={data:function(){return{cross:"",ruleForm:{yonghuzhanghao:"",xingming:"",xingbie:"",nianling:"",touxiang:"",tiwen:"",xinlv:"",xueya:"",shengao:"",tizhong:"",celiangriqi:""},user:{},ro:{yonghuzhanghao:!1,xingming:!1,xingbie:!1,nianling:!1,touxiang:!1,tiwen:!1,xinlv:!1,xueya:!1,shengao:!1,tizhong:!1,celiangriqi:!1}}},components:{wPicker:a.default,xiaEditor:o.default,multipleSelect:s.default},computed:{baseUrl:function(){return this.$base.url}},onLoad:function(){var e=(0,n.default)(regeneratorRuntime.mark((function e(t){var i,r,n,a,o=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.ruleForm.celiangriqi=this.$utils.getCurDate(),i=uni.getStorageSync("nowTable"),e.next=4,this.$api.session(i);case 4:if(r=e.sent,this.user=r.data,this.ruleForm.yonghuzhanghao=this.user.yonghuzhanghao,this.ro.yonghuzhanghao=!0,this.ruleForm.xingming=this.user.xingming,this.ro.xingming=!0,this.ruleForm.xingbie=this.user.xingbie,this.ro.xingbie=!0,this.ruleForm.nianling=this.user.nianling,this.ro.nianling=!0,this.ruleForm.touxiang=this.user.touxiang,this.ro.touxiang=!0,this.ruleForm.userid=uni.getStorageSync("appUserid"),t.refid&&(this.ruleForm.refid=Number(t.refid),this.ruleForm.nickname=uni.getStorageSync("nickname")),!t.id){e.next=24;break}return this.ruleForm.id=t.id,e.next=22,this.$api.info("shentishuju",this.ruleForm.id);case 22:r=e.sent,this.ruleForm=r.data;case 24:if(this.cross=t.cross,!t.cross){e.next=76;break}n=uni.getStorageSync("crossObj"),e.t0=regeneratorRuntime.keys(n);case 28:if((e.t1=e.t0()).done){e.next=76;break}if(a=e.t1.value,"yonghuzhanghao"!=a){e.next=34;break}return this.ruleForm.yonghuzhanghao=n[a],this.ro.yonghuzhanghao=!0,e.abrupt("continue",28);case 34:if("xingming"!=a){e.next=38;break}return this.ruleForm.xingming=n[a],this.ro.xingming=!0,e.abrupt("continue",28);case 38:if("xingbie"!=a){e.next=42;break}return this.ruleForm.xingbie=n[a],this.ro.xingbie=!0,e.abrupt("continue",28);case 42:if("nianling"!=a){e.next=46;break}return this.ruleForm.nianling=n[a],this.ro.nianling=!0,e.abrupt("continue",28);case 46:if("touxiang"!=a){e.next=50;break}return this.ruleForm.touxiang=n[a].split(",")[0],this.ro.touxiang=!0,e.abrupt("continue",28);case 50:if("tiwen"!=a){e.next=54;break}return this.ruleForm.tiwen=n[a],this.ro.tiwen=!0,e.abrupt("continue",28);case 54:if("xinlv"!=a){e.next=58;break}return this.ruleForm.xinlv=n[a],this.ro.xinlv=!0,e.abrupt("continue",28);case 58:if("xueya"!=a){e.next=62;break}return this.ruleForm.xueya=n[a],this.ro.xueya=!0,e.abrupt("continue",28);case 62:if("shengao"!=a){e.next=66;break}return this.ruleForm.shengao=n[a],this.ro.shengao=!0,e.abrupt("continue",28);case 66:if("tizhong"!=a){e.next=70;break}return this.ruleForm.tizhong=n[a],this.ro.tizhong=!0,e.abrupt("continue",28);case 70:if("celiangriqi"!=a){e.next=74;break}return this.ruleForm.celiangriqi=n[a],this.ro.celiangriqi=!0,e.abrupt("continue",28);case 74:e.next=28;break;case 76:this.styleChange(),this.$forceUpdate(),uni.getStorageSync("raffleType")&&null!=uni.getStorageSync("raffleType")&&(uni.removeStorageSync("raffleType"),setTimeout((function(){o.onSubmitTap()}),300));case 79:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),methods:{styleChange:function(){this.$nextTick((function(){}))},celiangriqiChange:function(e){this.ruleForm.celiangriqi=e.target.value,this.$forceUpdate()},touxiangTap:function(){var e=this;this.$api.upload((function(t){e.ruleForm.touxiang="upload/"+t.file,e.$forceUpdate(),e.$nextTick((function(){e.styleChange()}))}))},getUUID:function(){return(new Date).getTime()},onSubmitTap:function(){var e=(0,n.default)(regeneratorRuntime.mark((function e(){var t,i,r,n,a,o,s,l,u,d;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!this.ruleForm.nianling||this.$validate.isIntNumer(this.ruleForm.nianling)){e.next=3;break}return this.$utils.msg("年龄应输入整数"),e.abrupt("return");case 3:if(!this.cross){e.next=20;break}if(uni.setStorageSync("crossCleanType",!0),a=uni.getStorageSync("statusColumnName"),o=uni.getStorageSync("statusColumnValue"),""==a){e.next=20;break}if(t||(t=uni.getStorageSync("crossObj")),a.startsWith("[")){e.next=16;break}for(s in t)s==a&&(t[s]=o);return l=uni.getStorageSync("crossTable"),e.next=14,this.$api.update("".concat(l),t);case 14:e.next=20;break;case 16:i=Number(uni.getStorageSync("appUserid")),r=t["id"],n=uni.getStorageSync("statusColumnName"),n=n.replace(/\[/,"").replace(/\]/,"");case 20:if(!r||!i){e.next=43;break}return this.ruleForm.crossuserid=i,this.ruleForm.crossrefid=r,u={page:1,limit:10,crossuserid:i,crossrefid:r},e.next=26,this.$api.list("shentishuju",u);case 26:if(d=e.sent,!(d.data.total>=n)){e.next=33;break}return this.$utils.msg(uni.getStorageSync("tips")),uni.removeStorageSync("crossCleanType"),e.abrupt("return",!1);case 33:if(!this.ruleForm.id){e.next=38;break}return e.next=36,this.$api.update("shentishuju",this.ruleForm);case 36:e.next=40;break;case 38:return e.next=40,this.$api.add("shentishuju",this.ruleForm);case 40:this.$utils.msgBack("提交成功");case 41:e.next=51;break;case 43:if(!this.ruleForm.id){e.next=48;break}return e.next=46,this.$api.update("shentishuju",this.ruleForm);case 46:e.next=50;break;case 48:return e.next=50,this.$api.add("shentishuju",this.ruleForm);case 50:this.$utils.msgBack("提交成功");case 51:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),optionsChange:function(e){this.index=e.target.value},bindDateChange:function(e){this.date=e.target.value},getDate:function(e){var t=new Date,i=t.getFullYear(),r=t.getMonth()+1,n=t.getDate();return"start"===e?i-=60:"end"===e&&(i+=2),r=r>9?r:"0"+r,n=n>9?n:"0"+n,"".concat(i,"-").concat(r,"-").concat(n)},toggleTab:function(e){if(this.ro[e])return!1;this.$refs[e].show()}}};t.default=l},a4df:function(e,t,i){"use strict";var r=i("b2b9"),n=i.n(r);n.a},b2b9:function(e,t,i){var r=i("e6d9");"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var n=i("4f06").default;n("12e70524",r,!0,{sourceMap:!1,shadowMode:!1})},e6d9:function(e,t,i){var r=i("24fb");t=r(!1),t.push([e.i,'@charset "UTF-8";\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.content[data-v-20dbd78b]{min-height:calc(100vh - 44px);box-sizing:border-box}',""]),e.exports=t},f1cb:function(e,t,i){"use strict";var r,n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"content"},[i("v-uni-view",{style:{width:"100%",padding:"0",position:"relative",background:"#fff",height:"100%"}},[i("v-uni-form",{staticClass:"app-update-pv",style:{width:"100%",padding:"24rpx",background:"#fff",display:"block",height:"auto"}},[i("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("用户账号")]),i("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.yonghuzhanghao,placeholder:"用户账号",type:"text"},model:{value:e.ruleForm.yonghuzhanghao,callback:function(t){e.$set(e.ruleForm,"yonghuzhanghao",t)},expression:"ruleForm.yonghuzhanghao"}})],1),i("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("姓名")]),i("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.xingming,placeholder:"姓名",type:"text"},model:{value:e.ruleForm.xingming,callback:function(t){e.$set(e.ruleForm,"xingming",t)},expression:"ruleForm.xingming"}})],1),i("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("性别")]),i("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.xingbie,placeholder:"性别",type:"text"},model:{value:e.ruleForm.xingbie,callback:function(t){e.$set(e.ruleForm,"xingbie",t)},expression:"ruleForm.xingbie"}})],1),i("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("年龄")]),i("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.nianling,placeholder:"年龄",type:"number"},model:{value:e.ruleForm.nianling,callback:function(t){e.$set(e.ruleForm,"nianling",e._n(t))},expression:"ruleForm.nianling"}})],1),i("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.touxiangTap.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("头像")]),e.ruleForm.touxiang?i("v-uni-image",{staticClass:"avator",style:{width:"80rpx",borderRadius:"100%",objectFit:"cover",display:"block",height:"80rpx"},attrs:{src:e.baseUrl+e.ruleForm.touxiang.split(",")[0],mode:"aspectFill"}}):i("v-uni-image",{staticClass:"avator",style:{width:"80rpx",borderRadius:"100%",objectFit:"cover",display:"block",height:"80rpx"},attrs:{src:"../../static/gen/upload.png",mode:"aspectFill"}})],1),i("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("体温")]),i("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.tiwen,placeholder:"体温",type:"text"},model:{value:e.ruleForm.tiwen,callback:function(t){e.$set(e.ruleForm,"tiwen",t)},expression:"ruleForm.tiwen"}})],1),i("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("心率")]),i("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.xinlv,placeholder:"心率",type:"text"},model:{value:e.ruleForm.xinlv,callback:function(t){e.$set(e.ruleForm,"xinlv",t)},expression:"ruleForm.xinlv"}})],1),i("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("血压")]),i("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.xueya,placeholder:"血压",type:"text"},model:{value:e.ruleForm.xueya,callback:function(t){e.$set(e.ruleForm,"xueya",t)},expression:"ruleForm.xueya"}})],1),i("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("身高")]),i("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.shengao,placeholder:"身高",type:"text"},model:{value:e.ruleForm.shengao,callback:function(t){e.$set(e.ruleForm,"shengao",t)},expression:"ruleForm.shengao"}})],1),i("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("体重")]),i("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.tizhong,placeholder:"体重",type:"text"},model:{value:e.ruleForm.tizhong,callback:function(t){e.$set(e.ruleForm,"tizhong",t)},expression:"ruleForm.tizhong"}})],1),i("v-uni-view",{staticClass:" select",style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("测量日期")]),i("v-uni-picker",{style:{width:"100%",flex:"1",height:"auto"},attrs:{disabled:e.ro.celiangriqi,mode:"date",value:e.ruleForm.celiangriqi},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.celiangriqiChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"uni-input",style:{width:"100%",lineHeight:"80rpx",fontSize:"28rpx",color:"#D4CF5D"}},[e._v(e._s(e.ruleForm.celiangriqi?e.ruleForm.celiangriqi:"请选择测量日期"))])],1)],1),i("v-uni-view",{staticClass:"btn",style:{width:"100%",alignItems:"center",justifyContent:"center",display:"flex",height:"auto"}},[i("v-uni-button",{staticClass:"bg-red",style:{border:"0",padding:"0px",margin:"0",color:"rgb(255, 255, 255)",background:"#D4CF5D",width:"48%",lineHeight:"80rpx",fontSize:"28rpx",height:"80rpx"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onSubmitTap.apply(void 0,arguments)}}},[e._v("提交")])],1)],1)],1)],1)},a=[];i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return a})),i.d(t,"a",(function(){return r}))},f7aa:function(e,t,i){"use strict";i.r(t);var r=i("93ff"),n=i.n(r);for(var a in r)"default"!==a&&function(e){i.d(t,e,(function(){return r[e]}))}(a);t["default"]=n.a}}]);