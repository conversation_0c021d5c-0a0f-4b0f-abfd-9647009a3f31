(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-zuozhenyisheng-detail"],{"0753":function(t,e,i){"use strict";var n={"mescroll-uni":i("f05e").default,"uni-rate":i("f96a").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("mescroll-uni",{attrs:{up:t.upOption,down:t.downOption},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"content"},[i("v-uni-view",{staticClass:"container",style:{width:"100%",padding:"0",position:"relative",background:"#DFD8CC",height:"auto"}},[i("v-uni-swiper",{staticClass:"swiper",style:{width:"100%",background:"#fff",height:"400rpx",zIndex:"100"},attrs:{"indicator-dots":!1,autoplay:!1,circular:!1,"indicator-active-color":"#000000","indicator-color":"rgba(0, 0, 0, .3)",duration:500,interval:5e3,vertical:!1}},t._l(t.swiperList,(function(e,n){return i("v-uni-swiper-item",{key:n,style:{width:"100%",background:"#fff",height:"400rpx"}},["http"==e.substring(0,4)?i("v-uni-image",{style:{width:"100%",objectFit:"cover",display:"block",height:"400rpx"},attrs:{mode:"aspectFill",src:e}}):i("v-uni-image",{style:{width:"100%",objectFit:"cover",display:"block",height:"400rpx"},attrs:{mode:"aspectFill",src:t.baseUrl+e}})],1)})),1),i("v-uni-view",{staticClass:"detail-content",style:{padding:"20rpx 30rpx",margin:"-40rpx 0 0 0",overflow:"hidden",borderRadius:"40rpx 40rpx 0 0",flexWrap:"wrap",background:"#fff",display:"flex",width:"100%",height:"auto",zIndex:"1111"}},[i("v-uni-view",{staticClass:"detail-list-item price",style:{margin:"24rpx 0 24rpx 0",borderColor:"#ccc",flexWrap:"wrap",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",justifyContent:"space-between",height:"auto"}},[1==t.storeupFlag?i("v-uni-view",{style:{display:"flex"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.shoucang.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"icon iconfont icon-shoucang12",style:{margin:"0 4rpx 0 0",lineHeight:"80rpx",fontSize:"28rpx",color:"#D4CF5D"}}),i("v-uni-text",{style:{color:"#D4CF5D",lineHeight:"80rpx",fontSize:"28rpx"}},[t._v("已收藏")])],1):t._e(),0==t.storeupFlag?i("v-uni-view",{style:{display:"flex"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.shoucang.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"icon iconfont icon-shoucang11",style:{margin:"0 4rpx 0 0",lineHeight:"80rpx",fontSize:"28rpx",color:"#666"}}),i("v-uni-text",{style:{color:"#666",lineHeight:"80rpx",fontSize:"28rpx"}},[t._v("收藏")])],1):t._e()],1),i("v-uni-view",{staticClass:"detail-list-item title",style:{margin:"0 0 24rpx 0",borderColor:"#D8D8D8",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"lable",style:{width:"auto",padding:"0 10rpx 0 0",lineHeight:"48rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[t._v("医生姓名：")]),i("v-uni-view",{staticClass:"text",style:{padding:"0px",margin:"0px",lineHeight:"48rpx",fontSize:"28rpx",color:"rgb(0, 0, 0)",flex:"1"}},[t._v(t._s(t.detail.yishengxingming))])],1),i("v-uni-view",{staticClass:"detail-list-item",style:{margin:"0 0 24rpx 0",borderColor:"#D8D8D8",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"lable",style:{width:"auto",padding:"0 10rpx 0 0",lineHeight:"48rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[t._v("医生工号：")]),i("v-uni-view",{staticClass:"text",style:{padding:"0px",margin:"0px",lineHeight:"48rpx",fontSize:"28rpx",color:"rgb(0, 0, 0)",flex:"1"}},[t._v(t._s(t.detail.yishenggonghao))])],1),i("v-uni-view",{staticClass:"detail-list-item",style:{margin:"0 0 24rpx 0",borderColor:"#D8D8D8",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"lable",style:{width:"auto",padding:"0 10rpx 0 0",lineHeight:"48rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[t._v("性别：")]),i("v-uni-view",{staticClass:"text",style:{padding:"0px",margin:"0px",lineHeight:"48rpx",fontSize:"28rpx",color:"rgb(0, 0, 0)",flex:"1"}},[t._v(t._s(t.detail.xingbie))])],1),i("v-uni-view",{staticClass:"detail-list-item",style:{margin:"0 0 24rpx 0",borderColor:"#D8D8D8",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"lable",style:{width:"auto",padding:"0 10rpx 0 0",lineHeight:"48rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[t._v("科室：")]),i("v-uni-view",{staticClass:"text",style:{padding:"0px",margin:"0px",lineHeight:"48rpx",fontSize:"28rpx",color:"rgb(0, 0, 0)",flex:"1"}},[t._v(t._s(t.detail.keshi))])],1),i("v-uni-view",{staticClass:"detail-list-item",style:{margin:"0 0 24rpx 0",borderColor:"#D8D8D8",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"lable",style:{width:"auto",padding:"0 10rpx 0 0",lineHeight:"48rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[t._v("医龄：")]),i("v-uni-view",{staticClass:"text",style:{padding:"0px",margin:"0px",lineHeight:"48rpx",fontSize:"28rpx",color:"rgb(0, 0, 0)",flex:"1"}},[t._v(t._s(t.detail.yiling))])],1),i("v-uni-view",{staticClass:"detail-list-item",style:{margin:"0 0 24rpx 0",borderColor:"#D8D8D8",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"lable",style:{width:"auto",padding:"0 10rpx 0 0",lineHeight:"48rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[t._v("职称：")]),i("v-uni-view",{staticClass:"text",style:{padding:"0px",margin:"0px",lineHeight:"48rpx",fontSize:"28rpx",color:"rgb(0, 0, 0)",flex:"1"}},[t._v(t._s(t.detail.zhicheng))])],1),i("v-uni-view",{staticClass:"detail-list-item",style:{margin:"0 0 24rpx 0",borderColor:"#D8D8D8",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"lable",style:{width:"auto",padding:"0 10rpx 0 0",lineHeight:"48rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[t._v("擅长：")]),i("v-uni-view",{staticClass:"text",style:{padding:"0px",margin:"0px",lineHeight:"48rpx",fontSize:"28rpx",color:"rgb(0, 0, 0)",flex:"1"}},[t._v(t._s(t.detail.shanzhang))])],1),i("v-uni-view",{staticClass:"detail-list-item",style:{margin:"0 0 24rpx 0",borderColor:"#D8D8D8",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"lable",style:{width:"auto",padding:"0 10rpx 0 0",lineHeight:"48rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[t._v("联系方式：")]),i("v-uni-view",{staticClass:"text",staticStyle:{"text-decoration":"underline"},style:{padding:"0px",margin:"0px",lineHeight:"48rpx",fontSize:"28rpx",color:"rgb(0, 0, 0)",flex:"1"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.callClick(t.detail.lianxifangshi)}}},[t._v(t._s(t.detail.lianxifangshi))])],1),i("v-uni-view",{staticClass:"detail-list-item",style:{margin:"0 0 24rpx 0",borderColor:"#D8D8D8",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"lable",style:{width:"auto",padding:"0 10rpx 0 0",lineHeight:"48rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[t._v("坐诊时间：")]),i("v-uni-view",{staticClass:"text",style:{padding:"0px",margin:"0px",lineHeight:"48rpx",fontSize:"28rpx",color:"rgb(0, 0, 0)",flex:"1"}},[t._v(t._s(t.detail.zuozhenshijian))])],1),i("v-uni-view",{staticClass:"detail-list-item",style:{margin:"0 0 24rpx 0",borderColor:"#D8D8D8",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"lable",style:{width:"auto",padding:"0 10rpx 0 0",lineHeight:"48rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[t._v("评论数：")]),i("v-uni-view",{staticClass:"text",style:{padding:"0px",margin:"0px",lineHeight:"48rpx",fontSize:"28rpx",color:"rgb(0, 0, 0)",flex:"1"}},[t._v(t._s(t.detail.discussnum))])],1),i("v-uni-view",{staticClass:"detail-list-item",style:{margin:"0 0 24rpx 0",borderColor:"#D8D8D8",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"lable",style:{width:"auto",padding:"0 10rpx 0 0",lineHeight:"48rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[t._v("收藏数：")]),i("v-uni-view",{staticClass:"text",style:{padding:"0px",margin:"0px",lineHeight:"48rpx",fontSize:"28rpx",color:"rgb(0, 0, 0)",flex:"1"}},[t._v(t._s(t.detail.storeupnum))])],1),i("v-uni-view",{staticClass:"time-content",style:{width:"100%",margin:"0 0 24rpx 0",background:"",height:"auto"}},[i("v-uni-view",{staticClass:"comoment-header",style:{width:"100%",background:"#F6F6F4",justifyContent:"space-between",display:"flex",height:"auto"}},[i("v-uni-view",{style:{padding:"0 24rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333"}},[t._v("评论")]),i("v-uni-view",{staticClass:"btn-comment-content",staticStyle:{display:"flex","align-items":"center"},style:{padding:"0 20rpx",background:"#D4CF5D",display:"flex"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onCommentTap.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"cuIcon-add",style:{margin:"0 8rpx 0 0",lineHeight:"80rpx",fontSize:"28rpx",color:"#fff"}}),i("v-uni-view",{style:{color:"#fff",lineHeight:"80rpx",fontSize:"28rpx"}},[t._v("添加评论")])],1)],1),t._l(t.commentList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"cu-item comment-item",style:{width:"100%",padding:"24rpx",margin:"24rpx 0 0 0",background:"#F6F6F4",height:"auto"}},[i("v-uni-view",{style:{width:"100%",display:"flex",height:"auto"}},[e.avatarurl?i("v-uni-image",{style:{width:"60rpx",margin:"0 8rpx 0 0",borderRadius:"100%",display:"block",height:"60rpx"},attrs:{mode:"aspectFill",src:t.baseUrl+e.avatarurl}}):t._e(),i("v-uni-view",{staticClass:"text-grey",style:{color:"#333",lineHeight:"60rpx",fontSize:"28rpx"}},[t._v(t._s(e.nickname))])],1),i("v-uni-view",{staticClass:"text-gray text-content text-df",style:{margin:"8rpx 0",lineHeight:"1.5",fontSize:"28rpx",color:"#666"}},[i("v-uni-rich-text",{attrs:{nodes:e.content}})],1),i("v-uni-view",{staticClass:"text-gray text-content text-df",style:{margin:"8rpx 0",lineHeight:"1.5",fontSize:"28rpx",color:"#666"}},[i("uni-rate",{attrs:{disabled:!0,"disabled-color":"#ffca3e"},model:{value:e.score,callback:function(i){t.$set(e,"score",i)},expression:"item.score"}})],1),i("v-uni-view",{staticClass:"margin-top-sm text-gray text-df",style:{width:"100%",lineHeight:"24rpx",fontSize:"24rpx",color:"#999",textAlign:"right"}},[t._v(t._s(e.addtime))]),e.reply?i("v-uni-view",{staticClass:"text-gray text-content text-df",style:{margin:"8rpx 0",lineHeight:"1.5",fontSize:"28rpx",color:"#666"}},[t._v("回复:"),i("v-uni-rich-text",{attrs:{nodes:e.reply}})],1):t._e(),t.user&&t.user.id==e.userid?i("v-uni-view",{staticStyle:{display:"flex","justify-content":"flex-end",padding:"6rpx 0"}},[i("v-uni-view",{staticStyle:{color:"#999","font-size":"16rpx"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.delClick(e.id)}}},[t._v("删除")])],1):t._e()],1)}))],2),i("v-uni-view",{staticClass:"bottom-content bg-white tabbar border shop",style:{width:"100%",padding:"0",flexWrap:"wrap",background:"none",display:"flex",height:"auto"}},[t.userid&&t.isAuth("zuozhenyisheng","预约上门")?i("v-uni-button",{staticClass:"cu-btn bg-brown round shadow-blur",style:{border:"0",padding:"0 20rpx",margin:"0",color:"rgb(255, 255, 255)",background:"rgb(255, 170, 51)",width:"auto",fontSize:"28rpx",height:"80rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onAcrossTap("yuyueshangmenfuwu","","","","")}}},[t._v("预约上门")]):t._e(),!t.userid&&t.isAuthFront("zuozhenyisheng","预约上门")?i("v-uni-button",{staticClass:"cu-btn bg-brown round shadow-blur",style:{border:"0",padding:"0 20rpx",margin:"0",color:"rgb(255, 255, 255)",background:"rgb(255, 170, 51)",width:"auto",fontSize:"28rpx",height:"80rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onAcrossTap("yuyueshangmenfuwu","","","","")}}},[t._v("预约上门")]):t._e(),t.userid&&t.isAuth("zuozhenyisheng","在线问诊")?i("v-uni-button",{staticClass:"cu-btn bg-brown round shadow-blur",style:{border:"0",padding:"0 20rpx",margin:"0",color:"rgb(255, 255, 255)",background:"rgb(255, 170, 51)",width:"auto",fontSize:"28rpx",height:"80rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onAcrossTap("zaixianwenzhen","","","","")}}},[t._v("在线问诊")]):t._e(),!t.userid&&t.isAuthFront("zuozhenyisheng","在线问诊")?i("v-uni-button",{staticClass:"cu-btn bg-brown round shadow-blur",style:{border:"0",padding:"0 20rpx",margin:"0",color:"rgb(255, 255, 255)",background:"rgb(255, 170, 51)",width:"auto",fontSize:"28rpx",height:"80rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onAcrossTap("zaixianwenzhen","","","","")}}},[t._v("在线问诊")]):t._e(),t.userid&&t.isAuth("zuozhenyisheng","签约医生")?i("v-uni-button",{staticClass:"cu-btn bg-brown round shadow-blur",style:{border:"0",padding:"0 20rpx",margin:"0",color:"rgb(255, 255, 255)",background:"rgb(255, 170, 51)",width:"auto",fontSize:"28rpx",height:"80rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onAcrossTap("qianyueyisheng","","","","")}}},[t._v("签约医生")]):t._e(),!t.userid&&t.isAuthFront("zuozhenyisheng","签约医生")?i("v-uni-button",{staticClass:"cu-btn bg-brown round shadow-blur",style:{border:"0",padding:"0 20rpx",margin:"0",color:"rgb(255, 255, 255)",background:"rgb(255, 170, 51)",width:"auto",fontSize:"28rpx",height:"80rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onAcrossTap("qianyueyisheng","","","","")}}},[t._v("签约医生")]):t._e(),t.userid&&t.isAuth("zuozhenyisheng","私聊")?i("v-uni-button",{style:{border:"0",padding:"0 20rpx",margin:"0",color:"rgb(255, 255, 255)",background:"rgb(255, 170, 51)",width:"auto",fontSize:"28rpx",height:"80rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.chatClick.apply(void 0,arguments)}}},[t._v("联系TA")]):t._e(),!t.userid&&t.isAuthFront("zuozhenyisheng","私聊")?i("v-uni-button",{style:{border:"0",padding:"0 20rpx",margin:"0",color:"rgb(255, 255, 255)",background:"rgb(255, 170, 51)",width:"auto",fontSize:"28rpx",height:"80rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.chatClick.apply(void 0,arguments)}}},[t._v("联系TA")]):t._e()],1)],1)],1)],1)],1)],1)},a=[];i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}))},"0cfc":function(t,e,i){"use strict";i.r(e);var n=i("65d5"),r=i.n(n);for(var a in n)"default"!==a&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},"1de5":function(t,e,i){"use strict";t.exports=function(t,e){return e||(e={}),t=t&&t.__esModule?t.default:t,"string"!==typeof t?t:(/^['"].*['"]$/.test(t)&&(t=t.slice(1,-1)),e.hash&&(t+=e.hash),/["'() \t\n]/.test(t)||e.needQuotes?'"'.concat(t.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):t)}},"44cb":function(t,e,i){"use strict";var n=i("57d7"),r=i.n(n);r.a},"57d7":function(t,e,i){var n=i("ac96");"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("4f06").default;r("e2ee3954",n,!0,{sourceMap:!1,shadowMode:!1})},"65d5":function(t,e,i){"use strict";var n=i("4ea4");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a481"),i("c5f6"),i("28a5"),i("ac6a"),i("f559"),i("55dd"),i("96cf");var r=n(i("3b8d")),a=n(i("f96a")),s={data:function(){return{btnColor:["#409eff","#67c23a","#909399","#e6a23c","#f56c6c","#356c6c","#351c6c","#f093a9","#a7c23a","#104eff","#10441f","#a21233","#503319"],id:"",userid:"",detail:{},swiperList:[],commentList:[],mescroll:null,downOption:{auto:!1},upOption:{noMoreSize:3,textNoMore:"~ 没有更多了 ~"},hasNext:!0,user:{},storeupFlag:0,count:0,timer:null,title:""}},components:{uniRate:a.default},computed:{baseUrl:function(){return this.$base.url}},onLoad:function(){var t=(0,r.default)(regeneratorRuntime.mark((function t(e){var i,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return i=uni.getStorageSync("nowTable"),t.next=3,this.$api.session(i);case 3:n=t.sent,this.user=n.data,this.id=e.id,e.userid&&(this.userid=e.userid),this.init(),this.hasNext=!0,this.mescroll&&this.mescroll.resetUpScroll();case 10:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),onUnload:function(){this.timer&&clearInterval(this.timer)},onShow:function(){var t=(0,r.default)(regeneratorRuntime.mark((function t(e){var i,n,r,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return i=uni.getStorageSync("nowTable"),t.next=3,this.$api.session(i);case 3:if(n=t.sent,this.user=n.data,this.btnColor=this.btnColor.sort((function(){return.5-Math.random()})),this.getStoreup(),r=uni.getStorageSync("discusszuozhenyishengCleanType"),r&&(uni.removeStorageSync("discusszuozhenyishengCleanType"),this.mescroll.num=1,this.upCallback(this.mescroll),this.init(2)),a=uni.getStorageSync("crossCleanType"),!a){t.next=17;break}return uni.removeStorageSync("crossCleanType"),t.next=14,this.$api.info("zuozhenyisheng",this.id);case 14:n=t.sent,this.detail=n.data,this.title=this.detail.yishengxingming;case 17:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),destroyed:function(){},methods:{callClick:function(t){uni.makePhoneCall({phoneNumber:t})},onPayTap:function(){var t=this;if(!this.user)return this.$utils.msg("请先登录"),setTimeout((function(){t.$utils.jump("../login/login")}),1500),!1;uni.setStorageSync("paytable","zuozhenyisheng"),uni.setStorageSync("payObject",this.detail),this.$utils.jump("../pay-confirm/pay-confirm?type=1")},onDetailTap:function(t){uni.setStorageSync("useridTag",this.userid),this.$utils.jump("./detail?id=".concat(t.id,"&userid=")+this.userid)},getStoreup:function(){var t=(0,r.default)(regeneratorRuntime.mark((function t(){var e,i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.user){t.next=2;break}return t.abrupt("return",!1);case 2:return e={page:1,limit:1,refid:this.id,tablename:"zuozhenyisheng",userid:this.user.id,type:1},t.next=5,this.$api.list("storeup",e);case 5:i=t.sent,this.storeupFlag=i.data.list.length;case 7:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),shoucang:function(){var t=(0,r.default)(regeneratorRuntime.mark((function t(){var e,i,n,a,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=this,this.user){t.next=5;break}return this.$utils.msg("请先登录"),setTimeout((function(){e.$utils.jump("../login/login")}),1500),t.abrupt("return",!1);case 5:return i=this,n={page:1,limit:1,refid:i.detail.id,tablename:"zuozhenyisheng",userid:i.user.id,type:1},t.next=9,i.$api.list("storeup",n);case 9:if(a=t.sent,1!=a.data.list.length){t.next=14;break}return s=a.data.list[0].id,uni.showModal({title:"提示",content:"是否取消",success:function(){var t=(0,r.default)(regeneratorRuntime.mark((function t(e){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.confirm){t.next=8;break}return i.detail.storeupnum--,t.next=4,i.$api.update("zuozhenyisheng",i.detail);case 4:return t.next=6,i.$api.del("storeup",JSON.stringify([s]));case 6:i.$utils.msg("取消成功"),i.getStoreup();case 8:case"end":return t.stop()}}),t)})));function e(e){return t.apply(this,arguments)}return e}()}),t.abrupt("return");case 14:uni.showModal({title:"提示",content:"是否收藏",success:function(){var t=(0,r.default)(regeneratorRuntime.mark((function t(e){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.confirm){t.next=8;break}return t.next=3,i.$api.add("storeup",{userid:i.user.id,name:i.detail.yishengxingming,picture:i.swiperList[0],refid:i.detail.id,tablename:"zuozhenyisheng",type:1});case 3:return i.detail.storeupnum++,t.next=6,i.$api.update("zuozhenyisheng",i.detail);case 6:i.$utils.msg("收藏成功"),i.getStoreup();case 8:case"end":return t.stop()}}),t)})));function e(e){return t.apply(this,arguments)}return e}()});case 15:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),onAcrossTap:function(){var t=(0,r.default)(regeneratorRuntime.mark((function t(e,i,n,r,a,s){var o,l,u,c=arguments;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(c.length>6&&void 0!==c[6]?c[6]:1,o=this,this.user){t.next=6;break}return this.$utils.msg("请先登录"),setTimeout((function(){o.$utils.jump("../login/login")}),1500),t.abrupt("return",!1);case 6:if(uni.setStorageSync("crossTable","zuozhenyisheng"),uni.setStorageSync("crossObj",this.detail),uni.setStorageSync("statusColumnName",r),uni.setStorageSync("statusColumnValue",s),uni.setStorageSync("tips",a),""==r||r.startsWith("[")){t.next=21;break}l=uni.getStorageSync("crossObj"),t.t0=regeneratorRuntime.keys(l);case 14:if((t.t1=t.t0()).done){t.next=21;break}if(u=t.t1.value,u!=r||l[u]!=s){t.next=19;break}return this.$utils.msg(a),t.abrupt("return");case 19:t.next=14;break;case 21:this.$utils.jump("../".concat(e,"/add-or-update?cross=true"));case 22:case"end":return t.stop()}}),t,this)})));function e(e,i,n,r,a,s){return t.apply(this,arguments)}return e}(),init:function(){var t=(0,r.default)(regeneratorRuntime.mark((function t(){var e,i,n=arguments;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e=n.length>0&&void 0!==n[0]?n[0]:1,this.timer&&clearInterval(this.timer),t.next=4,this.$api.info("zuozhenyisheng",this.id);case 4:if(i=t.sent,this.detail=i.data,this.title=this.detail.yishengxingming,this.swiperList=this.detail.zhaopian?this.detail.zhaopian.split(","):[],2!=e){t.next=12;break}return this.detail.discussnum++,t.next=12,this.$api.update("zuozhenyisheng",this.detail);case 12:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),mescrollInit:function(t){this.mescroll=t},downCallback:function(t){this.hasNext=!0,t.resetUpScroll()},upCallback:function(){var t=(0,r.default)(regeneratorRuntime.mark((function t(e){var i,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!uni.getStorageSync("appUserid")){t.next=8;break}return t.next=3,this.$api.list("discusszuozhenyisheng",{page:e.num,limit:10,refid:Number(this.id)});case 3:for(n in i=t.sent,1==e.num&&(this.commentList=[]),i.data.list)i.data.list[n].content&&(i.data.list[n].content=i.data.list[n].content.replace(/img src/gi,'img style="width:100%;" src'));this.commentList=this.commentList.concat(i.data.list),0==i.data.list.length&&(this.hasNext=!1);case 8:e.endSuccess(e.size,this.hasNext);case 9:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),onChatTap:function(){this.$utils.jump("../chat/chat")},download:function(t){var e=this;t=e.$base.url+t,uni.downloadFile({url:t,success:function(i){200===i.statusCode&&(e.$utils.msg("下载成功"),window.open(t))}})},onCartTabTap:function(){this.$utils.tab("../shop-cart/shop-cart")},onCommentTap:function(){var t=(0,r.default)(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=this,this.user){t.next=5;break}return this.$utils.msg("请先登录"),setTimeout((function(){e.$utils.jump("../login/login")}),1500),t.abrupt("return",!1);case 5:this.$utils.jump("../discusszuozhenyisheng/add-or-update?refid=".concat(this.id));case 6:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),delClick:function(t){var e=this;uni.showModal({title:"提示",content:"是否删除此评论？",success:function(){var i=(0,r.default)(regeneratorRuntime.mark((function i(n){return regeneratorRuntime.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!n.confirm){i.next=8;break}return i.next=3,e.$api.del("discusszuozhenyisheng",JSON.stringify([t]));case 3:return e.$utils.msg("删除成功"),e.detail.discussnum--,i.next=7,e.$api.update("zuozhenyisheng",e.detail);case 7:setTimeout((function(){e.upCallback(e.mescroll)}),1500);case 8:case"end":return i.stop()}}),i)})));function n(t){return i.apply(this,arguments)}return n}()})}}};e.default=s},ac96:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */uni-page-body[data-v-5880d77f]{--animate-duration:1s;--animate-delay:1s;--animate-repeat:1}.content[data-v-5880d77f]{min-height:calc(100vh - 44px);box-sizing:border-box}.seat-list[data-v-5880d77f]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-flex-wrap:wrap;flex-wrap:wrap;background:#fff;margin:%?20?%;border-radius:%?20?%;padding:%?20?%;font-size:%?30?%}.seat-list .seat-item[data-v-5880d77f]{width:33.33%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column;margin-bottom:%?20?%}.seat-list .seat-item .seat-icon[data-v-5880d77f]{width:%?50?%;height:%?50?%;margin-bottom:%?10?%}uni-audio[data-v-5880d77f]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column}.audio[data-v-5880d77f] .uni-audio-default{width:inherit}',""]),t.exports=e},b09c:function(t,e,i){"use strict";i.r(e);var n=i("0753"),r=i("0cfc");for(var a in r)"default"!==a&&function(t){i.d(e,t,(function(){return r[t]}))}(a);i("44cb");var s,o=i("f0c5"),l=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"5880d77f",null,!1,n["a"],s);e["default"]=l.exports}}]);