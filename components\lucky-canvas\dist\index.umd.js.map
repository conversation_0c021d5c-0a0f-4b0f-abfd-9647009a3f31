{"version": 3, "file": "index.umd.js", "sources": ["../node_modules/tslib/tslib.es6.js", "../src/utils/polyfill.js", "../src/utils/index.ts", "../src/observer/dep.ts", "../src/observer/utils.ts", "../src/observer/array.ts", "../src/observer/index.ts", "../src/observer/watcher.ts", "../src/lib/lucky.ts", "../src/utils/math.ts", "../src/utils/tween.ts", "../src/lib/wheel.ts", "../src/lib/grid.ts", "../src/lib/slot.ts", "../src/utils/image.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n", "/**\n * 由于部分低版本下的某些 app 可能会缺少某些原型方法, 这里增加兼容\n */\n\n// ie11 不兼容 includes 方法\nif (!Array.prototype.includes) {\n  Object.defineProperty(Array.prototype, 'includes', {\n    value: function(valueToFind, fromIndex) {\n\n      if (this == null) {\n        throw new TypeError('\"this\" is null or not defined');\n      }\n\n      // 1. Let O be ? ToObject(this value).\n      var o = Object(this);\n\n      // 2. Let len be ? ToLength(? Get(O, \"length\")).\n      var len = o.length >>> 0;\n\n      // 3. If len is 0, return false.\n      if (len === 0) {\n        return false;\n      }\n\n      // 4. Let n be ? ToInteger(fromIndex).\n      //    (If fromIndex is undefined, this step produces the value 0.)\n      var n = fromIndex | 0;\n\n      // 5. If n ≥ 0, then\n      //  a. Let k be n.\n      // 6. Else n < 0,\n      //  a. Let k be len + n.\n      //  b. If k < 0, let k be 0.\n      var k = Math.max(n >= 0 ? n : len - Math.abs(n), 0);\n\n      function sameValueZero(x, y) {\n        return x === y || (typeof x === 'number' && typeof y === 'number' && isNaN(x) && isNaN(y));\n      }\n\n      // 7. Repeat, while k < len\n      while (k < len) {\n        // a. Let elementK be the result of ? Get(O, ! ToString(k)).\n        // b. If SameValueZero(valueToFind, elementK) is true, return true.\n        if (sameValueZero(o[k], valueToFind)) {\n          return true;\n        }\n        // c. Increase k by 1.\n        k++;\n      }\n\n      // 8. Return false\n      return false;\n    }\n  });\n}\n\n// vivo x7 下网易云游戏 app 缺少 includes 方法\nif (!String.prototype.includes) {\n  String.prototype.includes = function(search, start) {\n    'use strict';\n    if (typeof start !== 'number') {\n      start = 0;\n    }\n    if (start + search.length > this.length) {\n      return false;\n    } else {\n      return this.indexOf(search, start) !== -1;\n    }\n  };\n}\n\n// vivo x7 下网易云游戏 app 缺少 find 方法\nif (!Array.prototype.find) {\n  Object.defineProperty(Array.prototype, 'find', {\n    value: function(predicate) {\n     // 1. Let O be ? ToObject(this value).\n      if (this == null) {\n        throw new TypeError('\"this\" is null or not defined');\n      }\n      var o = Object(this);\n      // 2. Let len be ? ToLength(? Get(O, \"length\")).\n      var len = o.length >>> 0;\n      // 3. If IsCallable(predicate) is false, throw a TypeError exception.\n      if (typeof predicate !== 'function') {\n        throw new TypeError('predicate must be a function');\n      }\n      // 4. If thisArg was supplied, let T be thisArg; else let T be undefined.\n      var thisArg = arguments[1];\n      // 5. Let k be 0.\n      var k = 0;\n      // 6. Repeat, while k < len\n      while (k < len) {\n        // a. Let Pk be ! ToString(k).\n        // b. Let kValue be ? Get(O, Pk).\n        // c. Let testResult be ToBoolean(? Call(predicate, T, « kValue, k, O »)).\n        // d. If testResult is true, return kValue.\n        var kValue = o[k];\n        if (predicate.call(thisArg, kValue, k, o)) {\n          return kValue;\n        }\n        // e. Increase k by 1.\n        k++;\n      }\n      // 7. Return undefined.\n      return void 0;\n    }\n  });\n}\n", "/**\n * 判断是否是期望的类型\n * @param { unknown } param 将要判断的变量\n * @param { ...string } types 期望的类型\n * @return { boolean } 返回期望是否正确\n */\nexport const isExpectType = (param: unknown, ...types: string[]): boolean => {\n  return types.some(type => Object.prototype.toString.call(param).slice(8, -1).toLowerCase() === type)\n}\n\nexport const get = (data: object, strKeys: string) => {\n  const keys = strKeys.split('.')\n  for (let key of keys) {\n    const res = data[key]\n    if (!isExpectType(res, 'object', 'array')) return res\n    data = res\n  }\n  return data\n}\n\nexport const has = (data: object, key: string | number): boolean => {\n  return Object.prototype.hasOwnProperty.call(data, key)\n}\n\n/**\n * 移除\\n\n * @param { string } str 将要处理的字符串\n * @return { string } 返回新的字符串\n */\nexport const removeEnter = (str: string): string => {\n  return [].filter.call(str, s => s !== '\\n').join('')\n}\n\n/**\n * 把任何数据类型转成数字\n * @param num \n */\nexport const getNumber = (num: unknown): number => {\n  if (num === null) return 0\n  if (typeof num === 'object') return NaN\n  if (typeof num === 'number') return num\n  if (typeof num === 'string') {\n    if (num[num.length - 1] === '%') {\n      return Number(num.slice(0, -1)) / 100\n    }\n    return Number(num)\n  }\n  return NaN\n}\n\n/**\n * 判断颜色是否有效 (透明色 === 无效)\n * @param color 颜色\n */\nexport const hasBackground = (color: string | undefined | null): boolean => {\n  if (typeof color !== 'string') return false\n  color = color.toLocaleLowerCase().trim()\n  if (color === 'transparent') return false\n  if (/^rgba/.test(color)) {\n    const alpha = /([^\\s,]+)\\)$/.exec(color)\n    if (getNumber(alpha) === 0) return false\n  }\n  return true\n}\n\n/**\n * 通过padding计算\n * @return { object } block 边框信息\n */\nexport const computePadding = (\n  block: { padding?: string },\n  getLength: Function\n): [number, number, number, number] => {\n  let padding = block.padding?.split(' ').map(n => getLength(n)) || [0],\n    paddingTop = 0,\n    paddingBottom = 0,\n    paddingLeft = 0,\n    paddingRight = 0\n  switch (padding.length) {\n    case 1:\n      paddingTop = paddingBottom = paddingLeft = paddingRight = padding[0]\n      break\n    case 2:\n      paddingTop = paddingBottom = padding[0]\n      paddingLeft = paddingRight = padding[1]\n      break\n    case 3:\n      paddingTop = padding[0]\n      paddingLeft = paddingRight = padding[1]\n      paddingBottom = padding[2]\n      break\n    default:\n      paddingTop = padding[0]\n      paddingBottom = padding[1]\n      paddingLeft = padding[2]\n      paddingRight = padding[3]\n  }\n  // 检查是否单独传入值, 并且不是0\n  const res = { paddingTop, paddingBottom, paddingLeft, paddingRight }\n  for (let key in res) {\n    // 是否含有这个属性, 并且是数字或字符串\n    res[key] = has(block, key) && isExpectType(block[key], 'string', 'number')\n      ? getLength(block[key])\n      : res[key]\n  }\n  return [paddingTop, paddingBottom, paddingLeft, paddingRight]\n}\n\n/**\n * 节流函数\n * @param fn 将要处理的函数\n * @param wait 时间, 单位为毫秒\n * @returns 包装好的节流函数\n */\nexport const throttle = (fn: Function, wait = 300) => {\n  let timeId = null as any\n  return function (this: any, ...args: any[]) {\n    if (timeId) return\n    timeId = setTimeout(() => {\n      fn.apply(this, args)\n      clearTimeout(timeId)\n      timeId = null\n    }, wait)\n  }\n}\n\n/**\n * 通过概率计算出一个奖品索引\n * @param { Array<number | undefined> } rangeArr 概率\n * @returns { number } 中奖索引\n */\nexport const computeRange = (rangeArr: Array<number | undefined>): number => {\n  const ascendingArr: number[] = []\n  // 额外增加 map 来优化 ts 的类型推断\n  const sum = rangeArr.map(num => Number(num)).reduce((prev, curr) => {\n    if (curr > 0) { // 大于0\n      const res = prev + curr\n      ascendingArr.push(res)\n      return res\n    } else { // 小于等于0或NaN\n      ascendingArr.push(NaN)\n      return prev\n    }\n  }, 0)\n  const random = Math.random() * sum\n  return ascendingArr.findIndex(num => random <= num)\n}\n\n/**\n * 根据宽度分割字符串, 来达到换行的效果\n * @param text \n * @param maxWidth \n * @returns \n */\nexport const splitText = (\n  ctx: CanvasRenderingContext2D,\n  text: string,\n  getWidth: (lines: string[]) => number,\n  lineClamp: number = Infinity\n): string[] => {\n  // 如果 lineClamp 设置不正确, 则忽略该属性\n  if (lineClamp <= 0) lineClamp = Infinity\n  let str = ''\n  const lines = []\n  const EndWidth = ctx.measureText('...').width\n  for (let i = 0; i < text.length; i++) {\n    str += text[i]\n    let currWidth = ctx.measureText(str).width\n    const maxWidth = getWidth(lines)\n    // 如果正在计算最后一行, 则加上三个小点的宽度\n    if (lineClamp === lines.length + 1) currWidth += EndWidth\n    // 如果已经没有宽度了, 就没有必要再计算了\n    if (maxWidth < 0) return lines\n    // 如果当前一行的宽度不够了, 则处理下一行\n    if (currWidth > maxWidth) {\n      lines.push(str.slice(0, -1))\n      str = text[i]\n    }\n    // 如果现在是最后一行, 则加上三个小点并跳出\n    if (lineClamp === lines.length) {\n      lines[lines.length - 1] += '...'\n      return lines\n    }\n  }\n  if (str) lines.push(str)\n  if (!lines.length) lines.push(text)\n  return lines\n}\n\n// 获取一个重新排序的数组\nexport const getSortedArrayByIndex = <T>(arr: T[], order: number[]): T[] => {\n  const map: { [key: number]: T } = {}, res = []\n  for (let i = 0; i < arr.length; i++) {\n    map[i] = arr[i]\n  }\n  for (let i = 0; i < order.length; i++) {\n    const curr = map[order[i]]\n    if (curr) (res[i] = curr)\n  }\n  return res\n}\n", "import Watcher from './watcher'\n\nexport default class Dep {\n  static target: Watcher | null\n  private subs: Array<Watcher>\n\n  /**\n   * 订阅中心构造器\n   */\n  constructor () {\n    this.subs = []\n  }\n\n  /**\n   * 收集依赖\n   * @param {*} sub \n   */\n  public addSub (sub: Watcher) {\n    // 此处临时使用includes防重复添加\n    if (!this.subs.includes(sub)) {\n      this.subs.push(sub)\n    }\n  }\n\n  /**\n   * 派发更新\n   */\n  public notify () {\n    this.subs.forEach(sub => {\n      sub.update()\n    })\n  }\n}\n", "\nimport { isExpectType } from '../utils'\n\nexport const hasProto = '__proto__' in {}\n\nexport function def (obj: object, key: string | number, val: any, enumerable?: boolean) {\n  Object.defineProperty(obj, key, {\n    value: val,\n    enumerable: !!enumerable,\n    writable: true,\n    configurable: true\n  })\n}\n\nexport function parsePath (path: string) {\n  path += '.'\n  let segments: string[] = [], segment = ''\n  for (let i = 0; i < path.length; i++) {\n    let curr = path[i]\n    if (/\\[|\\./.test(curr)) {\n      segments.push(segment)\n      segment = ''\n    } else if (/\\W/.test(curr)) {\n      continue\n    } else {\n      segment += curr\n    }\n  }\n  return function (data: object | any[]) {\n    return segments.reduce((data, key) => {\n      return data[key]\n    }, data)\n  }\n}\n\nexport function traverse (value: any) {\n  // const seenObjects = new Set()\n  const dfs = (data: any) => {\n    if (!isExpectType(data, 'array', 'object')) return\n    Object.keys(data).forEach(key => {\n      const value = data[key]\n      dfs(value)\n    })\n  }\n  dfs(value)\n  // seenObjects.clear()\n}", "/**\n * 重写数组的原型方法\n */\nconst oldArrayProto = Array.prototype\nconst newArrayProto = Object.create(oldArrayProto)\nconst methods = ['push', 'pop', 'shift', 'unshift', 'sort', 'splice', 'reverse']\nmethods.forEach(method => {\n  newArrayProto[method] = function (...args: any[]) {\n    const res = oldArrayProto[method].apply(this, args)\n    const luckyOb = this['__luckyOb__']\n    if (['push', 'unshift', 'splice'].includes(method)) luckyOb.walk(this)\n    luckyOb.dep.notify()\n    return res\n  }\n})\n\nexport { newArrayProto }\n", "import Dep from './dep'\nimport { hasProto, def } from './utils'\nimport { newArrayProto } from './array'\n\nexport default class Observer {\n  value: any\n  dep: Dep\n\n  /**\n   * 观察者构造器\n   * @param value \n   */\n  constructor (value: any) {\n    // this.value = value\n    this.dep = new Dep()\n    // 将响应式对象代理到当前value上面, 并且将当前的enumerable设置为false\n    def(value, '__luckyOb__', this)\n    if (Array.isArray(value)) { // 如果是数组, 则重写原型方法\n      if (hasProto) {\n        value['__proto__'] = newArrayProto\n      } else {\n        Object.getOwnPropertyNames(newArrayProto).forEach(key => {\n          def(value, key, newArrayProto[key])\n        })\n      }\n    }\n    this.walk(value)\n  }\n\n  walk (data: object | any[]) {\n    Object.keys(data).forEach(key => {\n      defineReactive(data, key, data[key])\n    })\n  }\n}\n\n/**\n * 处理响应式\n * @param { Object | Array } data\n */\nexport function observe (data: any): Observer | void {\n  if (!data || typeof data !== 'object') return\n  let luckyOb: Observer | void\n  if ('__luckyOb__' in data) {\n    luckyOb = data['__luckyOb__']\n  } else {\n    luckyOb = new Observer(data)\n  }\n  return luckyOb\n}\n\n/**\n * 重写 setter / getter\n * @param {*} data \n * @param {*} key \n * @param {*} val \n */\nexport function defineReactive (data: any, key: string | number, val: any) {\n  const dep = new Dep()\n  const property = Object.getOwnPropertyDescriptor(data, key)\n  if (property && property.configurable === false) {\n    return\n  }\n  const getter = property && property.get\n  const setter = property && property.set\n  if ((!getter || setter) && arguments.length === 2) {\n    val = data[key]\n  }\n  let childOb = observe(val)\n  Object.defineProperty(data, key, {\n    get: () => {\n      const value = getter ? getter.call(data) : val\n      if (Dep.target) {\n        dep.addSub(Dep.target)\n        if (childOb) {\n          childOb.dep.addSub(Dep.target)\n        }\n      }\n      return value\n    },\n    set: (newVal) => {\n      if (newVal === val) return\n      val = newVal\n      if (getter && !setter) return\n      if (setter) {\n        setter.call(data, newVal)\n      } else {\n        val = newVal\n      }\n      childOb = observe(newVal)\n      dep.notify()\n    }\n  })\n}\n", "import Lucky from '../lib/lucky'\nimport Dep from './dep'\nimport { parsePath, traverse } from './utils'\n\nexport interface WatchOptType {\n  handler?: () => Function\n  immediate?: boolean\n  deep?: boolean\n}\n\nlet uid = 0\nexport default class Watcher {\n  id: number\n  $lucky: Lucky\n  expr: string | Function\n  cb: Function\n  deep: boolean\n  getter: Function\n  value: any\n\n  /**\n   * 观察者构造器\n   * @param {*} $lucky \n   * @param {*} expr \n   * @param {*} cb \n   */\n  constructor ($lucky: Lucky, expr: string | Function, cb: Function, options: WatchOptType = {}) {\n    this.id = uid++\n    this.$lucky = $lucky\n    this.expr = expr\n    this.deep = !!options.deep\n    if (typeof expr === 'function') {\n      this.getter = expr\n    } else {\n      this.getter = parsePath(expr)\n    }\n    this.cb = cb\n    this.value = this.get()\n  }\n\n  /**\n   * 根据表达式获取新值\n   */\n  get () {\n    Dep.target = this\n    const value = this.getter.call(this.$lucky, this.$lucky)\n    // 处理深度监听\n    if (this.deep) {\n      traverse(value)\n    }\n    Dep.target = null\n    return value\n  }\n\n  /**\n   * 触发 watcher 更新\n   */\n  update () {\n    // get获取新值\n    const newVal = this.get()\n    // 读取之前存储的旧值\n    const oldVal = this.value\n    this.value = newVal\n    // 触发 watch 回调\n    this.cb.call(this.$lucky, newVal, oldVal)\n  }\n}\n", "import '../utils/polyfill'\nimport { has, isExpectType, throttle } from '../utils/index'\nimport { name, version } from '../../package.json'\nimport { ConfigType, UserConfigType, ImgItemType, ImgType, Tuple } from '../types/index'\nimport { defineReactive } from '../observer'\nimport Watcher, { WatchOptType } from '../observer/watcher'\n\nexport default class Lucky {\n  static version: string = version\n  protected readonly version: string = version\n  protected readonly config: ConfigType\n  protected readonly ctx: CanvasRenderingContext2D\n  protected htmlFontSize: number = 16\n  protected rAF: Function = function () {}\n  protected boxWidth: number = 0\n  protected boxHeight: number = 0\n  protected data: {\n    width: string | number,\n    height: string | number\n  }\n\n  /**\n   * 公共构造器\n   * @param config\n   */\n  constructor (\n    config: string | HTMLDivElement | UserConfigType,\n    data: {\n      width: string | number,\n      height: string | number\n    }\n  ) {\n    // 兼容代码开始: 为了处理 v1.0.6 版本在这里传入了一个 dom\n    if (typeof config === 'string') config = { el: config } as UserConfigType\n    else if (config.nodeType === 1) config = { el: '', divElement: config } as UserConfigType\n    // 这里先野蛮的处理, 等待后续优化, 对外暴露的类型是UserConfigType, 但内部期望是ConfigType\n    config = config as UserConfigType\n    this.config = config as ConfigType\n    this.data = data\n    // 开始初始化\n    if (!config.flag) config.flag = 'WEB'\n    if (config.el) config.divElement = document.querySelector(config.el) as HTMLDivElement\n    // 如果存在父盒子, 就创建canvas标签\n    if (config.divElement) {\n      // 无论盒子内有没有canvas都执行覆盖逻辑\n      config.canvasElement = document.createElement('canvas')\n      config.divElement.appendChild(config.canvasElement)\n    }\n    // 获取 canvas 上下文\n    if (config.canvasElement) {\n      config.ctx = config.canvasElement.getContext('2d')!\n      // 添加版本信息到标签上, 方便定位版本问题\n      config.canvasElement.setAttribute('package', `${name}@${version}`)\n      config.canvasElement.addEventListener('click', e => this.handleClick(e))\n    }\n    this.ctx = config.ctx as CanvasRenderingContext2D\n    // 初始化 window 方法\n    this.initWindowFunction()\n    // 如果最后得不到 canvas 上下文那就无法进行绘制\n    if (!this.config.ctx) {\n      console.error('无法获取到 CanvasContext2D')\n    }\n    // 监听 window 触发 resize 时重置\n    if (window && typeof window.addEventListener === 'function') {\n      window.addEventListener('resize', throttle(() => this.resize(), 300))\n    }\n    // 监听异步设置 html 的 fontSize 并重新绘制\n    if (window && typeof window.MutationObserver === 'function') {\n      new window.MutationObserver(() => {\n        this.resize()\n      }).observe(document.documentElement, { attributes: true })\n    }\n  }\n\n  /**\n   * 初始化组件大小/单位\n   */\n  protected resize(): void {\n    this.config.beforeResize?.()\n    // 先初始化 fontSize 以防后面有 rem 单位\n    this.setHTMLFontSize()\n    // 拿到 config 即可设置 dpr\n    this.setDpr()\n    // 初始化宽高\n    this.resetWidthAndHeight()\n    // 根据 dpr 来缩放 canvas\n    this.zoomCanvas()\n  }\n\n  /**\n   * 初始化方法\n   */\n  protected initLucky () {\n    this.resize()\n    if (!this.boxWidth || !this.boxHeight) {\n      return console.error('无法获取到宽度或高度')\n    }\n  }\n\n  /**\n   * 鼠标点击事件\n   * @param e 事件参数\n   */\n  protected handleClick (e: MouseEvent): void {}\n\n  /**\n   * 根标签的字体大小\n   */\n  protected setHTMLFontSize (): void {\n    if (!window) return\n    this.htmlFontSize = +window.getComputedStyle(document.documentElement).fontSize.slice(0, -2)\n  }\n\n  // 清空画布\n  public clearCanvas (): void {\n    const [width, height] = [this.boxWidth, this.boxHeight]\n    this.ctx.clearRect(-width, -height, width * 2, height * 2)\n  }\n\n  /**\n   * 设备像素比\n   * window 环境下自动获取, 其余环境手动传入\n   */\n  protected setDpr (): void {\n    const { config } = this\n    if (config.dpr) {\n      // 优先使用 config 传入的 dpr\n    } else if (window) {\n      window['dpr'] = config.dpr = window.devicePixelRatio || 1\n    } else if (!config.dpr) {\n      console.error(config, '未传入 dpr 可能会导致绘制异常')\n    }\n  }\n\n  /**\n   * 重置盒子和canvas的宽高\n   */\n  private resetWidthAndHeight (): void {\n    const { config, data } = this\n    // 如果是浏览器环境并且存在盒子\n    let boxWidth = 0, boxHeight = 0\n    if (config.divElement) {\n      boxWidth = config.divElement.offsetWidth\n      boxHeight = config.divElement.offsetHeight\n    }\n    // 先从 data 里取宽高, 如果 config 上面没有, 就从 style 上面取\n    this.boxWidth = this.getLength(data.width || config['width']) || boxWidth\n    this.boxHeight = this.getLength(data.height || config['height']) || boxHeight\n    // 重新把宽高赋给盒子\n    if (config.divElement) {\n      config.divElement.style.overflow = 'hidden'\n      config.divElement.style.width = this.boxWidth + 'px'\n      config.divElement.style.height = this.boxHeight + 'px'\n    }\n  }\n\n  /**\n   * 根据 dpr 缩放 canvas 并处理位移\n   */\n  protected zoomCanvas (): void {\n    const { config, ctx } = this\n    const { canvasElement, dpr } = config\n    const [width, height] = [this.boxWidth * dpr, this.boxHeight * dpr]\n    if (!canvasElement) return\n    canvasElement.width = width\n    canvasElement.height = height\n    canvasElement.style.width = `${width}px`\n    canvasElement.style.height = `${height}px`\n    canvasElement.style['transform-origin'] = 'left top'\n    canvasElement.style.transform = `scale(${1 / dpr})`\n    ctx.scale(dpr, dpr)\n  }\n\n  /**\n   * 从 window 对象上获取一些方法\n   */\n  private initWindowFunction (): void {\n    const { config } = this\n    if (window) {\n      this.rAF = window.requestAnimationFrame ||\n        window['webkitRequestAnimationFrame'] ||\n        window['mozRequestAnimationFrame'] ||\n        function (callback: Function) {\n          window.setTimeout(callback, 1000 / 60)\n        }\n      config.setTimeout = window.setTimeout\n      config.setInterval = window.setInterval\n      config.clearTimeout = window.clearTimeout\n      config.clearInterval = window.clearInterval\n      return\n    }\n    if (config.rAF) {\n      // 优先使用帧动画\n      this.rAF = config.rAF\n    } else if (config.setTimeout) {\n      // 其次使用定时器\n      const timeout = config.setTimeout\n      this.rAF = (callback: Function): number => timeout(callback, 16.7)\n    } else {\n      // 如果config里面没有提供, 那就假设全局方法存在setTimeout\n      this.rAF = (callback: Function): number => setTimeout(callback, 16.7)\n    }\n  }\n\n  public isWeb () {\n    return ['WEB', 'UNI-H5', 'TARO-H5'].includes(this.config.flag)\n  }\n\n  /**\n   * 异步加载图片并返回图片的几何信息\n   * @param src 图片路径\n   * @param info 图片信息\n   */\n  protected loadImg (\n    src: string,\n    info: ImgItemType,\n    resolveName = '$resolve'\n  ): Promise<ImgType> {\n    return new Promise((resolve, reject) => {\n      if (!src) reject(`=> '${info.src}' 不能为空或不合法`)\n      if (this.config.flag === 'WEB') {\n        let imgObj = new Image()\n        imgObj['crossorigin'] = 'anonymous'\n        imgObj.onload = () => resolve(imgObj)\n        imgObj.onerror = () => reject(`=> '${info.src}' 图片加载失败`)\n        imgObj.src = src\n      } else {\n        // 其余平台向外暴露, 交给外部自行处理\n        info[resolveName] = resolve\n        info['$reject'] = reject\n        return\n      }\n    })\n  }\n\n  /**\n   * 公共绘制图片的方法\n   * @param imgObj 图片对象\n   * @param rectInfo: [x轴位置, y轴位置, 渲染宽度, 渲染高度] \n   */\n  protected drawImage(\n    ctx: CanvasRenderingContext2D,\n    imgObj: ImgType,\n    ...rectInfo: [...Tuple<number, 4>, ...Partial<Tuple<number, 4>>]\n  ): void {\n    let drawImg\n    const { flag, dpr } = this.config\n    if (['WEB', 'MP-WX'].includes(flag)) {\n      // 浏览器和新版小程序中直接绘制即可\n      drawImg = imgObj\n    } else if (['UNI-H5', 'UNI-MP', 'TARO-H5', 'TARO-MP'].includes(flag)) {\n      // 旧版本的小程序需要绘制 path, 这里特殊处理一下\n      type OldImageType = ImgType & { path: CanvasImageSource }\n      drawImg = (imgObj as OldImageType).path\n    } else {\n      // 如果传入了未知的标识\n      return console.error('意料之外的 flag, 该平台尚未兼容!')\n    }\n    const miniProgramOffCtx = (drawImg['canvas'] || drawImg).getContext?.('2d')\n    if (miniProgramOffCtx && !this.isWeb()) {\n      rectInfo = rectInfo.map(val => val! * dpr) as Tuple<number, 8>\n      const temp = miniProgramOffCtx.getImageData(...rectInfo.slice(0, 4))\n      ctx.putImageData(temp, ...(rectInfo.slice(4, 6) as Tuple<number, 2>))\n    } else {\n      if (rectInfo.length === 8) {\n        rectInfo = rectInfo.map((val, index) => index < 4 ? val! * dpr : val) as Tuple<number, 8>\n      }\n      // 尝试捕获错误\n      try {\n        ctx.drawImage(drawImg, ...rectInfo as Tuple<number, 8>)\n      } catch (err) {\n        /**\n         * TODO: safari浏览器下, init() 会出现奇怪的报错\n         * IndexSizeError: The index is not in the allowed range\n         * 但是这个报错并不影响实际的绘制, 目前先放一放, 等待有缘人\n         */\n        // console.log(err)\n      }\n    }\n  }\n\n  /**\n   * 计算图片的渲染宽高\n   * @param imgObj 图片标签元素\n   * @param imgInfo 图片信息\n   * @param maxWidth 最大宽度\n   * @param maxHeight 最大高度\n   * @return [渲染宽度, 渲染高度]\n   */\n  protected computedWidthAndHeight (\n    imgObj: ImgType,\n    imgInfo: ImgItemType,\n    maxWidth: number,\n    maxHeight: number\n  ): [number, number] {\n    // 根据配置的样式计算图片的真实宽高\n    if (!imgInfo.width && !imgInfo.height) {\n      // 如果没有配置宽高, 则使用图片本身的宽高\n      return [imgObj.width, imgObj.height]\n    } else if (imgInfo.width && !imgInfo.height) {\n      // 如果只填写了宽度, 没填写高度\n      let trueWidth = this.getLength(imgInfo.width, maxWidth)\n      // 那高度就随着宽度进行等比缩放\n      return [trueWidth, imgObj.height * (trueWidth / imgObj.width)]\n    } else if (!imgInfo.width && imgInfo.height) {\n      // 如果只填写了宽度, 没填写高度\n      let trueHeight = this.getLength(imgInfo.height, maxHeight)\n      // 那宽度就随着高度进行等比缩放\n      return [imgObj.width * (trueHeight / imgObj.height), trueHeight]\n    }\n    // 如果宽度和高度都填写了, 就如实计算\n    return [\n      this.getLength(imgInfo.width, maxWidth),\n      this.getLength(imgInfo.height, maxHeight)\n    ]\n  }\n\n  /**\n   * 转换单位\n   * @param { string } value 将要转换的值\n   * @param { number } denominator 分子\n   * @return { number } 返回新的字符串\n   */\n  protected changeUnits (value: string, denominator = 1): number {\n    const { config } = this\n    return Number(value.replace(/^([-]*[0-9.]*)([a-z%]*)$/, (val, num, unit) => {\n      const handleCssUnit = {\n        '%': (n: number) => n * (denominator / 100),\n        'px': (n: number) => n * 1,\n        'rem': (n: number) => n * this.htmlFontSize,\n        'vw': (n: number) => n / 100 * window.innerWidth,\n      }[unit]\n      if (handleCssUnit) return handleCssUnit(num)\n      // 如果找不到默认单位, 就交给外面处理\n      const otherHandleCssUnit = config.handleCssUnit || config['unitFunc']\n      return otherHandleCssUnit ? otherHandleCssUnit(num, unit) : num\n    }))\n  }\n\n  /**\n   * 获取长度\n   * @param length 将要转换的长度\n   * @param maxLength 最大长度\n   * @return 返回长度\n   */\n  protected getLength (length: string | number | undefined, maxLength?: number): number {\n    if (isExpectType(length, 'number')) return length as number\n    if (isExpectType(length, 'string')) return this.changeUnits(length as string, maxLength)\n    return 0\n  }\n\n  /**\n   * 获取相对(居中)X坐标\n   * @param width\n   * @param col\n   */\n  protected getOffsetX (width: number, maxWidth: number = 0): number {\n    return (maxWidth - width) / 2\n  }\n\n  protected getOffscreenCanvas (width: number, height: number): {\n    _offscreenCanvas: HTMLCanvasElement,\n    _ctx: CanvasRenderingContext2D\n  } | void {\n    if (!has(this, '_offscreenCanvas')) {\n      if (window && window.document && this.config.flag === 'WEB') {\n        this['_offscreenCanvas'] = document.createElement('canvas')\n      } else {\n        this['_offscreenCanvas'] = this.config['offscreenCanvas']\n      }\n      if (!this['_offscreenCanvas']) return console.error('离屏 Canvas 无法渲染!')\n    }\n    const dpr = this.config.dpr\n    const _offscreenCanvas = this['_offscreenCanvas'] as HTMLCanvasElement\n    _offscreenCanvas.width = (width || 300) * dpr\n    _offscreenCanvas.height = (height || 150) * dpr\n    const _ctx = _offscreenCanvas.getContext('2d')!\n    _ctx.clearRect(0, 0, width, height)\n    _ctx.scale(dpr, dpr)\n    _ctx['dpr'] = dpr\n    return { _offscreenCanvas, _ctx }\n  }\n\n  /**\n   * 添加一个新的响应式数据 (临时)\n   * @param data 数据\n   * @param key 属性\n   * @param value 新值\n   */\n  public $set (data: object, key: string | number, value: any) {\n    if (!data || typeof data !== 'object') return\n    defineReactive(data, key, value)\n  }\n\n  /**\n   * 添加一个属性计算 (临时)\n   * @param data 源数据\n   * @param key 属性名\n   * @param callback 回调函数\n   */\n  protected $computed (data: object, key: string, callback: Function) {\n    Object.defineProperty(data, key, {\n      get: () => {\n        return callback.call(this)\n      }\n    })\n  }\n\n  /**\n   * 添加一个观察者 create user watcher\n   * @param expr 表达式\n   * @param handler 回调函数\n   * @param watchOpt 配置参数\n   * @return 卸载当前观察者的函数 (暂未返回)\n   */\n  protected $watch (\n    expr: string | Function,\n    handler: Function | WatchOptType,\n    watchOpt: WatchOptType = {}\n  ): Function {\n    if (typeof handler === 'object') {\n      watchOpt = handler\n      handler = watchOpt.handler!\n    }\n    // 创建 user watcher\n    const watcher = new Watcher(this, expr, handler, watchOpt)\n    // 判断是否需要初始化时触发回调\n    if (watchOpt.immediate) {\n      handler.call(this, watcher.value)\n    }\n    // 返回一个卸载当前观察者的函数\n    return function unWatchFn () {}\n  }\n}\n", "/**\n * 转换为运算角度\n * @param { number } deg 数学角度\n * @return { number } 运算角度\n */\nexport const getAngle = (deg: number): number => {\n  return Math.PI / 180 * deg\n}\n\n/**\n * 根据角度计算圆上的点\n * @param { number } deg 运算角度\n * @param { number } r 半径\n * @return { Array<number> } 坐标[x, y]\n */\nexport const getArcPointerByDeg = (deg: number, r: number): [number, number] => {\n  return [+(Math.cos(deg) * r).toFixed(8), +(Math.sin(deg) * r).toFixed(8)]\n}\n\n/**\n * 根据点计算切线方程\n * @param { number } x 横坐标\n * @param { number } y 纵坐标\n * @return { Array<number> } [斜率, 常数]\n */\nexport const getTangentByPointer = (x: number, y: number): Array<number> => {\n  let k = - x / y\n  let b = -k * x + y\n  return [k, b]\n}\n\n// 使用 arc 绘制扇形\nexport const fanShapedByArc = (\n  ctx: CanvasRenderingContext2D,\n  minRadius: number,\n  maxRadius: number,\n  start: number,\n  end: number,\n  gutter: number,\n): void => {\n  ctx.beginPath()\n  let maxGutter = getAngle(90 / Math.PI / maxRadius * gutter)\n  let minGutter = getAngle(90 / Math.PI / minRadius * gutter)\n  let maxStart = start + maxGutter\n  let maxEnd = end - maxGutter\n  let minStart = start + minGutter\n  let minEnd = end - minGutter\n  ctx.arc(0, 0, maxRadius, maxStart, maxEnd, false)\n  // 如果 getter 比按钮短就绘制圆弧, 反之计算新的坐标点\n  // if (minEnd > minStart) {\n  //   ctx.arc(0, 0, minRadius, minEnd, minStart, true)\n  // } else {\n    ctx.lineTo(\n      ...getArcPointerByDeg(\n        (start + end) / 2,\n        gutter / 2 / Math.abs(Math.sin((start - end) / 2))\n      )\n    )\n  // }\n  ctx.closePath()\n}\n\n// 使用 arc 绘制圆角矩形\nexport const roundRectByArc = (\n  ctx: CanvasRenderingContext2D,\n  ...[x, y, w, h, r]: number[]\n) => {\n  const min = Math.min(w, h), PI = Math.PI\n  if (r > min / 2) r = min / 2\n  ctx.beginPath()\n  ctx.moveTo(x + r, y)\n  ctx.lineTo(x + r, y)\n  ctx.lineTo(x + w - r, y)\n  ctx.arc(x + w - r, y + r, r, -PI / 2, 0)\n  ctx.lineTo(x + w, y + h - r)\n  ctx.arc(x + w - r, y + h - r, r, 0, PI / 2)\n  ctx.lineTo(x + r, y + h)\n  ctx.arc(x + r, y + h - r, r, PI / 2, PI)\n  ctx.lineTo(x, y + r)\n  ctx.arc(x + r, y + r, r, PI, -PI / 2)\n  ctx.closePath()\n}\n\n/**\n * 创建线性渐变色\n */\nexport const getLinearGradient = (\n  ctx: CanvasRenderingContext2D,\n  x: number,\n  y: number,\n  w: number,\n  h: number,\n  background: string\n) => {\n  const context = (/linear-gradient\\((.+)\\)/.exec(background) as Array<any>)[1]\n    .split(',') // 根据逗号分割\n    .map((text: string) => text.trim()) // 去除两边空格\n  let deg = context.shift(), direction: [number, number, number, number] = [0, 0, 0, 0]\n  // 通过起始点和角度计算渐变终点的坐标点, 这里感谢泽宇大神提醒我使用勾股定理....\n  if (deg.includes('deg')) {\n    deg = deg.slice(0, -3) % 360\n    // 根据4个象限定义起点坐标, 根据45度划分8个区域计算终点坐标\n    const getLenOfTanDeg = (deg: number) => Math.tan(deg / 180 * Math.PI)\n    if (deg >= 0 && deg < 45) direction = [x, y + h, x + w, y + h - w * getLenOfTanDeg(deg - 0)]\n    else if (deg >= 45 && deg < 90) direction = [x, y + h, (x + w) - h * getLenOfTanDeg(deg - 45), y]\n    else if (deg >= 90 && deg < 135) direction = [x + w, y + h, (x + w) - h * getLenOfTanDeg(deg - 90), y]\n    else if (deg >= 135 && deg < 180) direction = [x + w, y + h, x, y + w * getLenOfTanDeg(deg - 135)]\n    else if (deg >= 180 && deg < 225) direction = [x + w, y, x, y + w * getLenOfTanDeg(deg - 180)]\n    else if (deg >= 225 && deg < 270) direction = [x + w, y, x + h * getLenOfTanDeg(deg - 225), y + h]\n    else if (deg >= 270 && deg < 315) direction = [x, y, x + h * getLenOfTanDeg(deg - 270), y + h]\n    else if (deg >= 315 && deg < 360) direction = [x, y, x + w, y + h - w * getLenOfTanDeg(deg - 315)]\n  }\n  // 创建四个简单的方向坐标\n  else if (deg.includes('top')) direction = [x, y + h, x, y]\n  else if (deg.includes('bottom')) direction = [x, y, x, y + h]\n  else if (deg.includes('left')) direction = [x + w, y, x, y]\n  else if (deg.includes('right')) direction = [x, y, x + w, y]\n  // 创建线性渐变必须使用整数坐标\n  const gradient = ctx.createLinearGradient(...(direction.map(n => n >> 0) as typeof direction))\n  // 这里后期重构, 先用any代替\n  return context.reduce((gradient: any, item: any, index: any) => {\n    const info = item.split(' ')\n    if (info.length === 1) gradient.addColorStop(index, info[0])\n    else if (info.length === 2) gradient.addColorStop(...info)\n    return gradient\n  }, gradient)\n}\n\n// // 根据三点画圆弧\n// export const drawRadian = (\n//   ctx: CanvasRenderingContext2D,\n//   r: number,\n//   start: number,\n//   end: number,\n//   direction: boolean = true\n// ) => {\n//   // 如果角度大于等于180度, 则分两次绘制, 因为 arcTo 无法绘制180度的圆弧\n//   if (Math.abs(end - start).toFixed(8) >= getAngle(180).toFixed(8)) {\n//     let middle = (end + start) / 2\n//     if (direction) {\n//       drawRadian(ctx, r, start, middle, direction)\n//       drawRadian(ctx, r, middle, end, direction)\n//     } else {\n//       drawRadian(ctx, r, middle, end, direction)\n//       drawRadian(ctx, r, start, middle, direction)\n//     }\n//     return false\n//   }\n//   // 如果方法相反, 则交换起点和终点\n//   if (!direction) [start, end] = [end, start]\n//   const [x1, y1] = getArcPointerByDeg(start, r)\n//   const [x2, y2] = getArcPointerByDeg(end, r)\n//   const [k1, b1] = getTangentByPointer(x1, y1)\n//   const [k2, b2] = getTangentByPointer(x2, y2)\n//   // 计算两条切线的交点\n//   let x0 = (b2 - b1) / (k1 - k2)\n//   let y0 = (k2 * b1 - k1 * b2) / (k2 - k1)\n//   // 如果有任何一条切线垂直于x轴, 则斜率不存在\n//   if (isNaN(x0)) {\n//     Math.abs(x1) === +r.toFixed(8) && (x0 = x1)\n//     Math.abs(x2) === +r.toFixed(8) && (x0 = x2)\n//   }\n//   if (k1 === Infinity || k1 === -Infinity) {\n//     y0 = k2 * x0 + b2\n//   }\n//   else if (k2 === Infinity || k2 === -Infinity) {\n//     y0 = k1 * x0 + b1\n//   }\n//   ctx.lineTo(x1, y1)\n//   // 微信小程序下 arcTo 在安卓真机下绘制有 bug\n//   ctx.arcTo(x0, y0, x2, y2, r)\n// }\n\n// // 使用 arcTo 绘制扇形 (弃用)\n// export const drawSectorByArcTo = (\n//   ctx: CanvasRenderingContext2D,\n//   minRadius: number,\n//   maxRadius: number,\n//   start: number,\n//   end: number,\n//   gutter: number,\n// ) => {\n//   if (!minRadius) minRadius = gutter\n//   // 内外圆弧分别进行等边缩放\n//   let maxGutter = getAngle(90 / Math.PI / maxRadius * gutter)\n//   let minGutter = getAngle(90 / Math.PI / minRadius * gutter)\n//   let maxStart = start + maxGutter\n//   let maxEnd = end - maxGutter\n//   let minStart = start + minGutter\n//   let minEnd = end - minGutter\n//   ctx.beginPath()\n//   ctx.moveTo(...getArcPointerByDeg(maxStart, maxRadius))\n//   drawRadian(ctx, maxRadius, maxStart, maxEnd, true)\n//   // 如果 getter 比按钮短就绘制圆弧, 反之计算新的坐标点\n//   if (minEnd > minStart) {\n//     drawRadian(ctx, minRadius, minStart, minEnd, false)\n//   } else {\n//     ctx.lineTo(\n//       ...getArcPointerByDeg(\n//         (start + end) / 2,\n//         gutter / 2 / Math.abs(Math.sin((start - end) / 2))\n//       )\n//     )\n//   }\n//   ctx.closePath()\n// }\n\n// // 使用 arcTo 绘制圆角矩形 (弃用)\n// export const roundRectByArcTo = (\n//   ctx: CanvasRenderingContext2D,\n//   ...[x, y, w, h, r]: number[]\n// ) => {\n//   let min = Math.min(w, h)\n//   if (r > min / 2) r = min / 2\n//   ctx.beginPath()\n//   ctx.moveTo(x + r, y)\n//   ctx.lineTo(x + r, y)\n//   ctx.lineTo(x + w - r, y)\n//   ctx.arcTo(x + w, y, x + w, y + r, r)\n//   ctx.lineTo(x + w, y + h - r)\n//   ctx.arcTo(x + w, y + h, x + w - r, y + h, r)\n//   ctx.lineTo(x + r, y + h)\n//   ctx.arcTo(x, y + h, x, y + h - r, r)\n//   ctx.lineTo(x, y + r)\n//   ctx.arcTo(x, y, x + r, y, r)\n// }\n", "/**\n * 缓动函数\n * t: current time（当前时间）\n * b: beginning value（初始值）\n * c: change in value（变化量）\n * d: duration（持续时间）\n * \n * 感谢张鑫旭大佬 https://github.com/zhangxinxu/Tween\n */\n\ninterface SpeedType {\n  easeIn: (...arr: number[]) => number\n  easeOut: (...arr: number[]) => number\n}\n\n// 二次方的缓动\nexport const quad: SpeedType = {\n  easeIn: function (t, b, c, d) {\n    if (t >= d) t = d\n    return c * (t /= d) * t + b\n  },\n  easeOut: function (t, b, c, d) {\n    if (t >= d) t = d\n    return -c * (t /= d) * (t - 2) + b\n  }\n}\n\n// 三次方的缓动\nexport const cubic: SpeedType = {\n  easeIn: function (t, b, c, d) {\n    if (t >= d) t = d\n    return c * (t /= d) * t * t + b\n  },\n  easeOut: function (t, b, c, d) {\n    if (t >= d) t = d\n    return c * ((t = t / d - 1) * t * t + 1) + b\n  }\n}\n\n// 四次方的缓动\nexport const quart: SpeedType = {\n  easeIn: function (t, b, c, d) {\n    if (t >= d) t = d\n    return c * (t /= d) * t * t * t + b\n  },\n  easeOut: function (t, b, c, d) {\n    if (t >= d) t = d\n    return -c * ((t = t / d - 1) * t * t * t - 1) + b\n  }\n}\n\n// 五次方的缓动\nexport const quint: SpeedType = {\n  easeIn: function (t, b, c, d) {\n    if (t >= d) t = d\n    return c * (t /= d) * t * t * t * t + b\n  },\n  easeOut: function (t, b, c, d) {\n    if (t >= d) t = d\n    return c * ((t = t / d - 1) * t * t * t * t + 1) + b\n  }\n}\n\n// 正弦曲线的缓动\nexport const sine: SpeedType = {\n  easeIn: function (t, b, c, d) {\n    if (t >= d) t = d\n    return -c * Math.cos(t / d * (Math.PI / 2)) + c + b\n  },\n  easeOut: function (t, b, c, d) {\n    if (t >= d) t = d\n    return c * Math.sin(t / d * (Math.PI / 2)) + b\n  }\n}\n\n// 指数曲线的缓动\nexport const expo: SpeedType = {\n  easeIn: function (t, b, c, d) {\n    if (t >= d) t = d\n    return (t == 0) ? b : c * Math.pow(2, 10 * (t / d - 1)) + b\n  },\n  easeOut: function (t, b, c, d) {\n    if (t >= d) t = d\n    return (t == d) ? b + c : c * (-Math.pow(2, -10 * t / d) + 1) + b\n  }\n}\n\n// 圆形曲线的缓动\nexport const circ: SpeedType = {\n  easeIn: function (t, b, c, d) {\n    if (t >= d) t = d\n    return -c * (Math.sqrt(1 - (t /= d) * t) - 1) + b\n  },\n  easeOut: function (t, b, c, d) {\n    if (t >= d) t = d\n    return c * Math.sqrt(1 - (t = t / d - 1) * t) + b\n  }\n}\n", "import Lucky from './lucky'\nimport { UserConfigType, FontItemType, ImgType } from '../types/index'\nimport LuckyWheelConfig, {\n  BlockType,\n  PrizeType,\n  ButtonType,\n  DefaultConfigType,\n  DefaultStyleType,\n  StartCallbackType,\n  EndCallbackType\n} from '../types/wheel'\nimport {\n  removeEnter,\n  hasBackground,\n  computeRange,\n  splitText,\n  has,\n} from '../utils/index'\nimport { getAngle, fanShapedByArc } from '../utils/math'\nimport { quad } from '../utils/tween'\n\nexport default class LuckyWheel extends Lucky {\n  private blocks: Array<BlockType> = []\n  private prizes: Array<PrizeType> = []\n  private buttons: Array<ButtonType> = []\n  private defaultConfig: DefaultConfigType = {}\n  private defaultStyle: DefaultStyleType = {}\n  private _defaultConfig: Required<DefaultConfigType> = {} as Required<DefaultConfigType>\n  private _defaultStyle: Required<DefaultStyleType> = {} as Required<DefaultStyleType>\n  private startCallback?: StartCallbackType\n  private endCallback?: EndCallbackType\n  private Radius = 0                    // 大转盘半径\n  private prizeRadius = 0               // 奖品区域半径\n  private prizeDeg = 0                  // 奖品数学角度\n  private prizeAng = 0               // 奖品运算角度\n  private rotateDeg = 0                 // 转盘旋转角度\n  private maxBtnRadius = 0              // 最大按钮半径\n  private startTime = 0                 // 开始时间戳\n  private endTime = 0                   // 停止时间戳\n  private stopDeg = 0                   // 刻舟求剑\n  private endDeg = 0                    // 停止角度\n  private FPS = 16.6                    // 屏幕刷新率\n  /**\n   * 游戏当前的阶段\n   * step = 0 时, 游戏尚未开始\n   * step = 1 时, 此时处于加速阶段\n   * step = 2 时, 此时处于匀速阶段\n   * step = 3 时, 此时处于减速阶段\n   */\n  private step: 0 | 1 | 2 | 3 = 0\n  /**\n   * 中奖索引\n   * prizeFlag = undefined 时, 处于开始抽奖阶段, 正常旋转\n   * prizeFlag >= 0 时, 说明stop方法被调用, 并且传入了中奖索引\n   * prizeFlag === -1 时, 说明stop方法被调用, 并且传入了负值, 本次抽奖无效\n   */\n  private prizeFlag: number | undefined\n  private ImageCache = new Map()\n\n  /**\n   * 大转盘构造器\n   * @param config 配置项\n   * @param data 抽奖数据\n   */\n  constructor (config: UserConfigType, data: LuckyWheelConfig) {\n    super(config, {\n      width: data.width,\n      height: data.height\n    })\n    this.initData(data)\n    this.initWatch()\n    this.initComputed()\n    // 创建前回调函数\n    config.beforeCreate?.call(this)\n    // 首次初始化\n    this.init()\n  }\n\n  protected resize(): void {\n    super.resize()\n    this.Radius = Math.min(this.boxWidth, this.boxHeight) / 2\n    this.ctx.translate(this.Radius, this.Radius)\n    this.draw()\n    this.config.afterResize?.()\n  }\n\n  protected initLucky (): void {\n    this.Radius = 0\n    this.prizeRadius = 0\n    this.prizeDeg = 0\n    this.prizeAng = 0\n    this.rotateDeg = 0\n    this.maxBtnRadius = 0\n    this.startTime = 0\n    this.endTime = 0\n    this.stopDeg = 0\n    this.endDeg = 0\n    this.FPS = 16.6\n    this.prizeFlag = -1\n    this.step = 0\n    super.initLucky()\n  }\n\n  /**\n   * 初始化数据\n   * @param data\n   */\n  private initData (data: LuckyWheelConfig): void {\n    this.$set(this, 'width', data.width)\n    this.$set(this, 'height', data.height)\n    this.$set(this, 'blocks', data.blocks || [])\n    this.$set(this, 'prizes', data.prizes || [])\n    this.$set(this, 'buttons', data.buttons || [])\n    this.$set(this, 'defaultConfig', data.defaultConfig || {})\n    this.$set(this, 'defaultStyle', data.defaultStyle || {})\n    this.$set(this, 'startCallback', data.start)\n    this.$set(this, 'endCallback', data.end)\n  }\n\n  /**\n   * 初始化属性计算\n   */\n  private initComputed () {\n    // 默认配置\n    this.$computed(this, '_defaultConfig', () => {\n      const config = {\n        gutter: '0px',\n        offsetDegree: 0,\n        speed: 20,\n        speedFunction: 'quad',\n        accelerationTime: 2500,\n        decelerationTime: 2500,\n        stopRange: 0,\n        ...this.defaultConfig\n      }\n      return config\n    })\n    // 默认样式\n    this.$computed(this, '_defaultStyle', () => {\n      const style = {\n        fontSize: '18px',\n        fontColor: '#000',\n        fontStyle: 'sans-serif',\n        fontWeight: '400',\n        background: 'rgba(0,0,0,0)',\n        wordWrap: true,\n        lengthLimit: '90%',\n        ...this.defaultStyle\n      }\n      return style\n    })\n  }\n\n  /**\n   * 初始化观察者\n   */\n  private initWatch () {\n    // 重置宽度\n    this.$watch('width', (newVal: string | number) => {\n      this.data.width = newVal\n      this.resize()\n    })\n    // 重置高度\n    this.$watch('height', (newVal: string | number) => {\n      this.data.height = newVal\n      this.resize()\n    })\n    // 观察 blocks 变化收集图片\n    this.$watch('blocks', (newData: Array<BlockType>) => {\n      this.initImageCache()\n    }, { deep: true })\n    // 观察 prizes 变化收集图片\n    this.$watch('prizes', (newData: Array<PrizeType>) => {\n      this.initImageCache()\n    }, { deep: true })\n    // 观察 buttons 变化收集图片\n    this.$watch('buttons', (newData: Array<ButtonType>) => {\n      this.initImageCache()\n    }, { deep: true })\n    this.$watch('defaultConfig', () => this.draw(), { deep: true })\n    this.$watch('defaultStyle', () => this.draw(), { deep: true })\n    this.$watch('startCallback', () => this.init())\n    this.$watch('endCallback', () => this.init())\n  }\n\n  /**\n   * 初始化 canvas 抽奖\n   */\n  public async init (): Promise<void> {\n    this.initLucky()\n    const { config } = this\n    // 初始化前回调函数\n    config.beforeInit?.call(this)\n    this.draw() // 先画一次, 防止闪烁\n    this.draw() // 再画一次, 拿到正确的按钮轮廓\n    // 异步加载图片\n    await this.initImageCache()\n    // 初始化后回调函数\n    config.afterInit?.call(this)\n  }\n\n  private initImageCache (): Promise<void> {\n    return new Promise((resolve) => {\n      const willUpdateImgs = {\n        blocks: this.blocks.map(block => block.imgs),\n        prizes: this.prizes.map(prize => prize.imgs),\n        buttons: this.buttons.map(btn => btn.imgs),\n      }\n      ;(<(keyof typeof willUpdateImgs)[]>Object.keys(willUpdateImgs)).forEach(imgName => {\n        const willUpdate = willUpdateImgs[imgName]\n        // 循环遍历所有图片\n        const allPromise: Promise<void>[] = []\n        willUpdate && willUpdate.forEach((imgs, cellIndex) => {\n          imgs && imgs.forEach((imgInfo, imgIndex) => {\n            allPromise.push(this.loadAndCacheImg(imgName, cellIndex, imgIndex))\n          })\n        })\n        Promise.all(allPromise).then(() => {\n          this.draw()\n          resolve()\n        })\n      })\n    })\n  }\n\n  /**\n   * canvas点击事件\n   * @param e 事件参数\n   */\n  protected handleClick (e: MouseEvent): void {\n    const { ctx } = this\n    ctx.beginPath()\n    ctx.arc(0, 0, this.maxBtnRadius, 0, Math.PI * 2, false)\n    if (!ctx.isPointInPath(e.offsetX, e.offsetY)) return\n    if (this.step !== 0) return\n    this.startCallback?.(e)\n  }\n\n  /**\n   * 根据索引单独加载指定图片并缓存\n   * @param cellName 模块名称\n   * @param cellIndex 模块索引\n   * @param imgName 模块对应的图片缓存\n   * @param imgIndex 图片索引\n   */\n  private async loadAndCacheImg (\n    cellName: 'blocks' | 'prizes' | 'buttons',\n    cellIndex: number,\n    imgIndex: number,\n  ): Promise<void> {\n    return new Promise((resolve, reject) => {\n      // 获取图片信息\n      const cell: BlockType | PrizeType | ButtonType = this[cellName][cellIndex]\n      if (!cell || !cell.imgs) return\n      const imgInfo = cell.imgs[imgIndex]\n      if (!imgInfo) return\n      // 异步加载图片\n      this.loadImg(imgInfo.src, imgInfo).then(async currImg => {\n        if (typeof imgInfo.formatter === 'function') {\n          currImg = await Promise.resolve(imgInfo.formatter.call(this, currImg))\n        }\n        this.ImageCache.set(imgInfo['src'], currImg)\n        resolve()\n      }).catch(err => {\n        console.error(`${cellName}[${cellIndex}].imgs[${imgIndex}] ${err}`)\n        reject()\n      })\n    })\n  }\n\n  private drawBlock (radius: number, block: BlockType, blockIndex: number): void {\n    const { ctx } = this\n    if (hasBackground(block.background)) {\n      ctx.beginPath()\n      ctx.fillStyle = block.background!\n      ctx.arc(0, 0, radius, 0, Math.PI * 2, false)\n      ctx.fill()\n    }\n    block.imgs && block.imgs.forEach((imgInfo, imgIndex) => {\n      const blockImg = this.ImageCache.get(imgInfo.src)\n      if (!blockImg) return\n      // 绘制图片\n      const [trueWidth, trueHeight] = this.computedWidthAndHeight(blockImg, imgInfo, radius * 2, radius * 2)\n      const [xAxis, yAxis] = [this.getOffsetX(trueWidth) + this.getLength(imgInfo.left, radius * 2), this.getLength(imgInfo.top, radius * 2) - radius]\n      ctx.save()\n      imgInfo.rotate && ctx.rotate(getAngle(this.rotateDeg))\n      this.drawImage(ctx, blockImg, xAxis, yAxis, trueWidth, trueHeight)\n      ctx.restore()\n    })\n  }\n\n  /**\n   * 开始绘制\n   */\n  protected draw (): void {\n    const { config, ctx, _defaultConfig, _defaultStyle } = this\n    // 触发绘制前回调\n    config.beforeDraw?.call(this, ctx)\n    // 清空画布\n    ctx.clearRect(-this.Radius, -this.Radius, this.Radius * 2, this.Radius * 2)\n    // 计算 padding 并绘制 blocks 边框\n    this.prizeRadius = this.blocks.reduce((radius, block, blockIndex) => {\n      this.drawBlock(radius, block, blockIndex)\n      return radius - this.getLength(block.padding && block.padding.split(' ')[0])\n    }, this.Radius)\n    // 计算起始弧度\n    this.prizeDeg = 360 / this.prizes.length\n    this.prizeAng = getAngle(this.prizeDeg)\n    const shortSide = this.prizeRadius * Math.sin(this.prizeAng / 2) * 2\n    // 起始角度调整到正上方, 并且减去半个扇形角度\n    let start = getAngle(this.rotateDeg - 90 + this.prizeDeg / 2 + _defaultConfig.offsetDegree)\n    // 计算文字横坐标\n    const getFontX = (font: FontItemType, line: string) => {\n      return this.getOffsetX(ctx.measureText(line).width) + this.getLength(font.left, shortSide)\n    }\n    // 计算文字纵坐标\n    const getFontY = (font: FontItemType, height: number, lineIndex: number) => {\n      // 优先使用字体行高, 要么使用默认行高, 其次使用字体大小, 否则使用默认字体大小\n      const lineHeight = font.lineHeight || _defaultStyle.lineHeight || font.fontSize || _defaultStyle.fontSize\n      return this.getLength(font.top, height) + (lineIndex + 1) * this.getLength(lineHeight)\n    }\n    ctx.save()\n    // 绘制prizes奖品区域\n    this.prizes.forEach((prize, prizeIndex) => {\n      // 计算当前奖品区域中间坐标点\n      let currMiddleDeg = start + prizeIndex * this.prizeAng\n      // 奖品区域可见高度\n      let prizeHeight = this.prizeRadius - this.maxBtnRadius\n      // 绘制背景\n      const background = prize.background || _defaultStyle.background\n      if (hasBackground(background)) {\n        ctx.fillStyle = background\n        fanShapedByArc(\n          ctx, this.maxBtnRadius, this.prizeRadius,\n          currMiddleDeg - this.prizeAng / 2,\n          currMiddleDeg + this.prizeAng / 2,\n          this.getLength(_defaultConfig.gutter),\n        )\n        ctx.fill()\n      }\n      // 计算临时坐标并旋转文字\n      let x = Math.cos(currMiddleDeg) * this.prizeRadius\n      let y = Math.sin(currMiddleDeg) * this.prizeRadius\n      ctx.translate(x, y)\n      ctx.rotate(currMiddleDeg + getAngle(90))\n      // 绘制图片\n      prize.imgs && prize.imgs.forEach((imgInfo, imgIndex) => {\n        const prizeImg = this.ImageCache.get(imgInfo.src)\n        if (!prizeImg) return\n        const [trueWidth, trueHeight] = this.computedWidthAndHeight(\n          prizeImg,\n          imgInfo,\n          this.prizeAng * this.prizeRadius,\n          prizeHeight\n        )\n        const [xAxis, yAxis] = [\n          this.getOffsetX(trueWidth) + this.getLength(imgInfo.left, shortSide),\n          this.getLength(imgInfo.top, prizeHeight)\n        ]\n        this.drawImage(ctx, prizeImg, xAxis, yAxis, trueWidth, trueHeight)\n      })\n      // 逐行绘制文字\n      prize.fonts && prize.fonts.forEach(font => {\n        const fontColor = font.fontColor || _defaultStyle.fontColor\n        const fontWeight = font.fontWeight || _defaultStyle.fontWeight\n        const fontSize = this.getLength(font.fontSize || _defaultStyle.fontSize)\n        const fontStyle = font.fontStyle || _defaultStyle.fontStyle\n        const wordWrap = has(font, 'wordWrap') ? font.wordWrap : _defaultStyle.wordWrap\n        const lengthLimit = font.lengthLimit || _defaultStyle.lengthLimit\n        const lineClamp = font.lineClamp || _defaultStyle.lineClamp\n        ctx.fillStyle = fontColor\n        ctx.font = `${fontWeight} ${fontSize >> 0}px ${fontStyle}`\n        let lines = [], text = String(font.text)\n        if (wordWrap) {\n          lines = splitText(ctx, removeEnter(text), (lines) => {\n            // 三角形临边\n            const adjacentSide = this.prizeRadius - getFontY(font, prizeHeight, lines.length)\n            // 三角形短边\n            const shortSide = adjacentSide * Math.tan(this.prizeAng / 2)\n            // 最大宽度\n            let maxWidth = shortSide * 2 - this.getLength(_defaultConfig.gutter)\n            return this.getLength(lengthLimit, maxWidth)\n          }, lineClamp)\n        } else {\n          lines = text.split('\\n')\n        }\n        lines.filter(line => !!line).forEach((line, lineIndex) => {\n          ctx.fillText(line, getFontX(font, line), getFontY(font, prizeHeight, lineIndex))\n        })\n      })\n      // 修正旋转角度和原点坐标\n      ctx.rotate(getAngle(360) - currMiddleDeg - getAngle(90))\n      ctx.translate(-x, -y)\n    })\n    ctx.restore()\n    // 绘制按钮\n    this.buttons.forEach((btn, btnIndex) => {\n      let radius = this.getLength(btn.radius, this.prizeRadius)\n      // 绘制背景颜色\n      this.maxBtnRadius = Math.max(this.maxBtnRadius, radius)\n      if (hasBackground(btn.background)) {\n        ctx.beginPath()\n        ctx.fillStyle = btn.background as string\n        ctx.arc(0, 0, radius, 0, Math.PI * 2, false)\n        ctx.fill()\n      }\n      // 绘制指针\n      if (btn.pointer && hasBackground(btn.background)) {\n        ctx.beginPath()\n        ctx.fillStyle = btn.background as string\n        ctx.moveTo(-radius, 0)\n        ctx.lineTo(radius, 0)\n        ctx.lineTo(0, -radius * 2)\n        ctx.closePath()\n        ctx.fill()\n      }\n      // 绘制按钮图片\n      btn.imgs && btn.imgs.forEach((imgInfo, imgIndex) => {\n        const btnImg = this.ImageCache.get(imgInfo.src)\n        if (!btnImg) return\n        const [trueWidth, trueHeight] = this.computedWidthAndHeight(btnImg, imgInfo, radius * 2, radius * 2)\n        const [xAxis, yAxis] = [this.getOffsetX(trueWidth) + this.getLength(imgInfo.left, radius), this.getLength(imgInfo.top, radius)]\n        this.drawImage(ctx, btnImg, xAxis, yAxis, trueWidth, trueHeight)\n      })\n      // 绘制按钮文字\n      btn.fonts && btn.fonts.forEach(font => {\n        let fontColor = font.fontColor || _defaultStyle.fontColor\n        let fontWeight = font.fontWeight || _defaultStyle.fontWeight\n        let fontSize = this.getLength(font.fontSize || _defaultStyle.fontSize)\n        let fontStyle = font.fontStyle || _defaultStyle.fontStyle\n        ctx.fillStyle = fontColor\n        ctx.font = `${fontWeight} ${fontSize >> 0}px ${fontStyle}`\n        String(font.text).split('\\n').forEach((line, lineIndex) => {\n          ctx.fillText(line, getFontX(font, line), getFontY(font, radius, lineIndex))\n        })\n      })\n    })\n    // 触发绘制后回调\n    config.afterDraw?.call(this, ctx)\n  }\n\n  /**\n   * 刻舟求剑\n   */\n  private carveOnGunwaleOfAMovingBoat (): void {\n    const { _defaultConfig, prizeFlag, prizeDeg, rotateDeg } = this\n    this.endTime = Date.now()\n    const stopDeg = this.stopDeg = rotateDeg\n    const speed = _defaultConfig.speed\n    const stopRange = (Math.random() * prizeDeg - prizeDeg / 2) * this.getLength(_defaultConfig.stopRange)\n    let i = 0, prevSpeed = 0, prevDeg = 0\n    while (++i) {\n      const endDeg = 360 * i - prizeFlag! * prizeDeg - rotateDeg - _defaultConfig.offsetDegree + stopRange - prizeDeg / 2\n      let currSpeed = quad.easeOut(this.FPS, stopDeg, endDeg, _defaultConfig.decelerationTime) - stopDeg\n      if (currSpeed > speed) {\n        this.endDeg = (speed - prevSpeed > currSpeed - speed) ? endDeg : prevDeg\n        break\n      }\n      prevDeg = endDeg\n      prevSpeed = currSpeed\n    }\n  }\n\n  /**\n   * 对外暴露: 开始抽奖方法\n   */\n  public play (): void {\n    if (this.step !== 0) return\n    // 记录游戏开始时间\n    this.startTime = Date.now()\n    // 重置中奖索引\n    this.prizeFlag = void 0\n    // 加速阶段\n    this.step = 1\n    // 触发回调\n    this.config.afterStart?.()\n    // 开始游戏\n    this.run()\n  }\n\n  /**\n   * 对外暴露: 缓慢停止方法\n   * @param index 中奖索引\n   */\n  public stop (index?: number): void {\n    if (this.step === 0 || this.step === 3) return\n    // 如果没有传递中奖索引, 则通过range属性计算一个\n    if (!index && index !== 0) {\n      const rangeArr = this.prizes.map(item => item.range)\n      index = computeRange(rangeArr)\n    }\n    // 如果index是负数则停止游戏, 反之则传递中奖索引\n    if (index < 0) {\n      this.step = 0\n      this.prizeFlag = -1\n    } else {\n      this.step = 2\n      this.prizeFlag = index % this.prizes.length\n    }\n  }\n\n  /**\n   * 实际开始执行方法\n   * @param num 记录帧动画执行多少次\n   */\n  private run (num: number = 0): void {\n    const { rAF, step, prizeFlag, _defaultConfig } = this\n    const { accelerationTime, decelerationTime, speed } = _defaultConfig\n    // 游戏结束\n    if (step === 0) {\n      this.endCallback?.(this.prizes.find((prize, index) => index === prizeFlag) || {})\n      return\n    }\n    // 如果等于 -1 就直接停止游戏\n    if (prizeFlag === -1) return\n    // 计算结束位置\n    if (step === 3 && !this.endDeg) this.carveOnGunwaleOfAMovingBoat()\n    // 计算时间间隔\n    const startInterval = Date.now() - this.startTime\n    const endInterval = Date.now() - this.endTime\n    let rotateDeg = this.rotateDeg\n    // \n    if (step === 1 || startInterval < accelerationTime) { // 加速阶段\n      // 记录帧率\n      this.FPS = startInterval / num\n      const currSpeed = quad.easeIn(startInterval, 0, speed, accelerationTime)\n      // 加速到峰值后, 进入匀速阶段\n      if (currSpeed === speed) {\n        this.step = 2\n      }\n      rotateDeg = rotateDeg + currSpeed % 360\n    } else if (step === 2) { // 匀速阶段\n      // 速度保持不变\n      rotateDeg = rotateDeg + speed % 360\n      // 如果 prizeFlag 有值, 则进入减速阶段\n      if (prizeFlag !== void 0 && prizeFlag >= 0) {\n        this.step = 3\n        // 清空上一次的位置信息\n        this.stopDeg = 0\n        this.endDeg = 0\n      }\n    } else if (step === 3) { // 减速阶段\n      // 开始缓慢停止\n      rotateDeg = quad.easeOut(endInterval, this.stopDeg, this.endDeg, decelerationTime)\n      if (endInterval >= decelerationTime) {\n        this.step = 0\n      }\n    } else {\n      // 出现异常\n      this.stop(-1)\n    }\n    this.rotateDeg = rotateDeg\n    this.draw()\n    rAF(this.run.bind(this, num + 1))\n  }\n\n  /**\n   * 换算渲染坐标\n   * @param x\n   * @param y\n   */\n  protected conversionAxis (x: number, y: number): [number, number] {\n    const { config } = this\n    return [x / config.dpr - this.Radius, y / config.dpr - this.Radius]\n  }\n}\n", "import Lucky from './lucky'\nimport { UserConfigType, ImgType } from '../types/index'\nimport LuckyGridConfig, {\n  BlockType,\n  PrizeType,\n  ButtonType,\n  CellFontType,\n  CellImgType,\n  RowsType,\n  ColsType,\n  CellType,\n  DefaultConfigType,\n  DefaultStyleType,\n  ActiveStyleType,\n  StartCallbackType,\n  EndCallbackType,\n} from '../types/grid'\nimport {\n  has,\n  isExpectType,\n  removeEnter,\n  computePadding,\n  hasBackground,\n  computeRange,\n  splitText\n} from '../utils/index'\nimport { roundRectByArc, getLinearGradient } from '../utils/math'\nimport { quad } from '../utils/tween'\n\nexport default class LuckyGrid extends Lucky {\n  private rows: RowsType = 3\n  private cols: ColsType = 3\n  private blocks: Array<BlockType> = []\n  private prizes: Array<PrizeType> = []\n  private buttons: Array<ButtonType> = []\n  private button?: ButtonType\n  private defaultConfig: DefaultConfigType = {}\n  private defaultStyle: DefaultStyleType = {}\n  private activeStyle: ActiveStyleType = {}\n  private _defaultConfig: Required<DefaultConfigType> = {} as Required<DefaultConfigType>\n  private _defaultStyle: Required<DefaultStyleType> = {} as Required<DefaultStyleType>\n  private _activeStyle: Required<ActiveStyleType> = {} as Required<ActiveStyleType>\n  private startCallback?: StartCallbackType\n  private endCallback?: EndCallbackType\n  private cellWidth = 0                 // 格子宽度\n  private cellHeight = 0                // 格子高度\n  private startTime = 0                 // 开始时间戳\n  private endTime = 0                   // 结束时间戳\n  private currIndex = 0                 // 当前index累加\n  private stopIndex = 0                 // 刻舟求剑\n  private endIndex = 0                  // 停止索引\n  private demo = false                  // 是否自动游走\n  private timer = 0                     // 游走定时器\n  private FPS = 16.6                    // 屏幕刷新率\n  /**\n   * 游戏当前的阶段\n   * step = 0 时, 游戏尚未开始\n   * step = 1 时, 此时处于加速阶段\n   * step = 2 时, 此时处于匀速阶段\n   * step = 3 时, 此时处于减速阶段\n   */\n  private step: 0 | 1 | 2 | 3 = 0\n  /**\n   * 中奖索引\n   * prizeFlag = undefined 时, 处于开始抽奖阶段, 正常旋转\n   * prizeFlag >= 0 时, 说明stop方法被调用, 并且传入了中奖索引\n   * prizeFlag === -1 时, 说明stop方法被调用, 并且传入了负值, 本次抽奖无效\n   */\n  private prizeFlag: number | undefined = -1\n  // 所有格子\n  private cells: CellType<CellFontType, CellImgType>[] = []\n  // 奖品区域几何信息\n  private prizeArea: { x: number, y: number, w: number, h: number } | undefined\n  // 图片缓存\n  private ImageCache = new Map()\n\n  /**\n   * 九宫格构造器\n   * @param config 配置项\n   * @param data 抽奖数据\n   */\n  constructor (config: UserConfigType, data: LuckyGridConfig) {\n    super(config, {\n      width: data.width,\n      height: data.height\n    })\n    this.initData(data)\n    this.initWatch()\n    this.initComputed()\n    // 创建前回调函数\n    config.beforeCreate?.call(this)\n    // 首次初始化\n    this.init()\n  }\n\n  protected resize(): void {\n    super.resize()\n    this.draw()\n    this.config.afterResize?.()\n  }\n\n  protected initLucky (): void {\n    this.cellWidth = 0\n    this.cellHeight = 0\n    this.startTime = 0\n    this.endTime = 0\n    this.currIndex = 0\n    this.stopIndex = 0\n    this.endIndex = 0\n    this.demo = false\n    this.timer = 0\n    this.FPS = 16.6\n    this.prizeFlag = -1\n    this.step = 0\n    super.initLucky()\n  }\n\n  /**\n   * 初始化数据\n   * @param data\n   */\n  private initData (data: LuckyGridConfig): void {\n    this.$set(this, 'width', data.width)\n    this.$set(this, 'height', data.height)\n    this.$set(this, 'rows', Number(data.rows) || 3)\n    this.$set(this, 'cols', Number(data.cols) || 3)\n    this.$set(this, 'blocks', data.blocks || [])\n    this.$set(this, 'prizes', data.prizes || [])\n    this.$set(this, 'buttons', data.buttons || [])\n    // 临时过渡代码, 升级到2.x即可删除\n    this.$set(this, 'button', data.button)\n    this.$set(this, 'defaultConfig', data.defaultConfig || {})\n    this.$set(this, 'defaultStyle', data.defaultStyle || {})\n    this.$set(this, 'activeStyle', data.activeStyle || {})\n    this.$set(this, 'startCallback', data.start)\n    this.$set(this, 'endCallback', data.end)\n  }\n\n  /**\n   * 初始化属性计算\n   */\n  private initComputed (): void {\n    // 默认配置\n    this.$computed(this, '_defaultConfig', () => {\n      const config = {\n        gutter: 5,\n        speed: 20,\n        accelerationTime: 2500,\n        decelerationTime: 2500,\n        ...this.defaultConfig\n      }\n      config.gutter = this.getLength(config.gutter)\n      config.speed = config.speed / 40\n      return config\n    })\n    // 默认样式\n    this.$computed(this, '_defaultStyle', () => {\n      return {\n        borderRadius: 20,\n        fontColor: '#000',\n        fontSize: '18px',\n        fontStyle: 'sans-serif',\n        fontWeight: '400',\n        background: 'rgba(0,0,0,0)',\n        shadow: '',\n        wordWrap: true,\n        lengthLimit: '90%',\n        ...this.defaultStyle\n      }\n    })\n    // 中奖样式\n    this.$computed(this, '_activeStyle', () => {\n      return {\n        background: '#ffce98',\n        shadow: '',\n        ...this.activeStyle\n      }\n    })\n  }\n\n  /**\n   * 初始化观察者\n   */\n  private initWatch (): void {\n    // 重置宽度\n    this.$watch('width', (newVal: string | number) => {\n      this.data.width = newVal\n      this.resize()\n    })\n    // 重置高度\n    this.$watch('height', (newVal: string | number) => {\n      this.data.height = newVal\n      this.resize()\n    })\n    // 监听 blocks 数据的变化\n    this.$watch('blocks', (newData: Array<BlockType>) => {\n      this.initImageCache()\n    }, { deep: true })\n    // 监听 prizes 数据的变化\n    this.$watch('prizes', (newData: Array<PrizeType>) => {\n      this.initImageCache()\n    }, { deep: true })\n    // 监听 button 数据的变化\n    this.$watch('buttons', (newData: Array<ButtonType>) => {\n      this.initImageCache()\n    }, { deep: true })\n    this.$watch('rows', () => this.init())\n    this.$watch('cols', () => this.init())\n    this.$watch('defaultConfig', () => this.draw(), { deep: true })\n    this.$watch('defaultStyle', () => this.draw(), { deep: true })\n    this.$watch('activeStyle', () => this.draw(), { deep: true })\n    this.$watch('startCallback', () => this.init())\n    this.$watch('endCallback', () => this.init())\n  }\n\n  /**\n   * 初始化 canvas 抽奖\n   */\n  public async init (): Promise<void> {\n    this.initLucky()\n    const { config } = this\n    // 初始化前回调函数\n    config.beforeInit?.call(this)\n    // 先画一次防止闪烁\n    this.draw()\n    // 异步加载图片\n    await this.initImageCache()\n    // 初始化后回调函数\n    config.afterInit?.call(this)\n  }\n\n  private initImageCache (): Promise<void> {\n    return new Promise((resolve) => {\n      const btnImgs = this.buttons.map(btn => btn.imgs)\n      if (this.button) btnImgs.push(this.button.imgs)\n      const willUpdateImgs = {\n        blocks: this.blocks.map(block => block.imgs),\n        prizes: this.prizes.map(prize => prize.imgs),\n        buttons: btnImgs,\n      }\n      ;(<(keyof typeof willUpdateImgs)[]>Object.keys(willUpdateImgs)).forEach(imgName => {\n        const willUpdate = willUpdateImgs[imgName]\n        // 循环遍历所有图片\n        const allPromise: Promise<void>[] = []\n        willUpdate && willUpdate.forEach((imgs, cellIndex) => {\n          imgs && imgs.forEach((imgInfo, imgIndex) => {\n            allPromise.push(this.loadAndCacheImg(imgName, cellIndex, imgIndex))\n          })\n        })\n        Promise.all(allPromise).then(() => {\n          this.draw()\n          resolve()\n        })\n      })\n    })\n  }\n\n  /**\n   * canvas点击事件\n   * @param e 事件参数\n   */\n  protected handleClick (e: MouseEvent): void {\n    const { ctx } = this\n    ;[\n      ...this.buttons,\n      this.button\n    ].forEach(btn => {\n      if (!btn) return\n      const [x, y, width, height] = this.getGeometricProperty([\n        btn.x, btn.y, btn.col || 1, btn.row || 1\n      ])\n      ctx.beginPath()\n      ctx.rect(x, y, width, height)\n      if (!ctx.isPointInPath(e.offsetX, e.offsetY)) return\n      if (this.step !== 0) return\n      // 如果btn里有单独的回调方法, 优先触发\n      if (typeof btn.callback === 'function') btn.callback.call(this, btn)\n      // 最后触发公共回调\n      this.startCallback?.(e, btn)\n    })\n  }\n\n  /**\n   * 根据索引单独加载指定图片并缓存\n   * @param cellName 模块名称\n   * @param cellIndex 模块索引\n   * @param imgName 模块对应的图片缓存\n   * @param imgIndex 图片索引\n   */\n  private async loadAndCacheImg (\n    cellName: 'blocks' | 'prizes' | 'buttons',\n    cellIndex: number,\n    imgIndex: number\n  ): Promise<void> {\n    return new Promise((resolve, reject) => {\n      let cell: BlockType | PrizeType | ButtonType = this[cellName][cellIndex]\n      // 临时过渡代码, 升级到2.x即可删除\n      if (cellName === 'buttons' && !this.buttons.length && this.button) {\n        cell = this.button\n      }\n      if (!cell || !cell.imgs) return\n      const imgInfo = cell.imgs[imgIndex]\n      if (!imgInfo) return\n      // 异步加载图片\n      const request = [\n        this.loadImg(imgInfo.src, imgInfo),\n        imgInfo['activeSrc'] && this.loadImg(imgInfo['activeSrc'], imgInfo, '$activeResolve')\n      ]\n      Promise.all(request).then(async ([defaultImg, activeImg]) => {\n        const formatter = imgInfo.formatter\n        // 对图片进行处理\n        if (typeof formatter === 'function') {\n          defaultImg = await Promise.resolve(formatter.call(this, defaultImg))\n          if (activeImg) {\n            activeImg = await Promise.resolve(formatter.call(this, activeImg))\n          }\n        }\n        this.ImageCache.set(imgInfo['src'], defaultImg)\n        activeImg && this.ImageCache.set(imgInfo['activeSrc'], activeImg)\n        resolve()\n      }).catch(err => {\n        console.error(`${cellName}[${cellIndex}].imgs[${imgIndex}] ${err}`)\n        reject()\n      })\n    })\n  }\n\n  /**\n   * 绘制九宫格抽奖\n   */\n  protected draw (): void {\n    const { config, ctx, _defaultConfig, _defaultStyle, _activeStyle } = this\n    // 触发绘制前回调\n    config.beforeDraw?.call(this, ctx)\n    // 清空画布\n    ctx.clearRect(0, 0, this.boxWidth, this.boxHeight)\n    // 合并奖品和按钮\n    this.cells = [\n      ...this.prizes,\n      ...this.buttons\n    ]\n    if (this.button) this.cells.push(this.button)\n    this.cells.forEach(cell => {\n      cell.col = cell.col || 1\n      cell.row = cell.row || 1\n    })\n    // 计算获取奖品区域的几何信息\n    this.prizeArea = this.blocks.reduce(({x, y, w, h}, block, blockIndex) => {\n      const [paddingTop, paddingBottom, paddingLeft, paddingRight] = computePadding(block, this.getLength.bind(this))\n      const r = block.borderRadius ? this.getLength(block.borderRadius) : 0\n      // 绘制边框\n      const background = block.background\n      if (hasBackground(background)) {\n        ctx.fillStyle = this.handleBackground(x, y, w, h, background!)\n        roundRectByArc(ctx, x, y, w, h, r)\n        ctx.fill()\n      }\n      // 绘制图片\n      block.imgs && block.imgs.forEach((imgInfo, imgIndex) => {\n        const blockImg = this.ImageCache.get(imgInfo.src)\n        if (!blockImg) return\n        // 绘制图片\n        const [trueWidth, trueHeight] = this.computedWidthAndHeight(blockImg, imgInfo, w, h)\n        const [xAxis, yAxis] = [\n          this.getOffsetX(trueWidth, w) + this.getLength(imgInfo.left, w),\n          this.getLength(imgInfo.top, h)\n        ]\n        this.drawImage(ctx, blockImg, x + xAxis, y + yAxis, trueWidth, trueHeight)\n      })\n      return {\n        x: x + paddingLeft,\n        y: y + paddingTop,\n        w: w - paddingLeft - paddingRight,\n        h: h - paddingTop - paddingBottom\n      }\n    }, { x: 0, y: 0, w: this.boxWidth, h: this.boxHeight })\n    // 计算单一奖品格子的宽度和高度\n    this.cellWidth = (this.prizeArea.w - _defaultConfig.gutter * (this.cols - 1)) / this.cols\n    this.cellHeight = (this.prizeArea.h - _defaultConfig.gutter * (this.rows - 1)) / this.rows\n    // 绘制所有格子\n    this.cells.forEach((cell, cellIndex) => {\n      let [x, y, width, height] = this.getGeometricProperty([cell.x, cell.y, cell.col!, cell.row!])\n      // 默认不显示中奖标识\n      let isActive = false\n      // 只要 prizeFlag 不是负数, 就显示中奖标识\n      if (this.prizeFlag === void 0 || this.prizeFlag > -1) {\n        isActive = cellIndex === this.currIndex % this.prizes.length >> 0\n      }\n      // 绘制背景色\n      const background = isActive ? _activeStyle.background : (cell.background || _defaultStyle.background)\n      if (hasBackground(background)) {\n        // 处理阴影 (暂时先用any, 这里后续要优化)\n        const shadow: any = (\n          isActive ? _activeStyle.shadow : (cell.shadow || _defaultStyle.shadow)\n        )\n          .replace(/px/g, '') // 清空px字符串\n          .split(',')[0].split(' ') // 防止有人声明多个阴影, 截取第一个阴影\n          .map((n, i) => i < 3 ? Number(n) : n) // 把数组的前三个值*像素比\n        // 绘制阴影\n        if (shadow.length === 4) {\n          ctx.shadowColor = shadow[3]\n          ctx.shadowOffsetX = shadow[0] * config.dpr\n          ctx.shadowOffsetY = shadow[1] * config.dpr\n          ctx.shadowBlur = shadow[2]\n          // 修正(格子+阴影)的位置, 这里使用逗号运算符\n          shadow[0] > 0 ? (width -= shadow[0]) : (width += shadow[0], x -= shadow[0])\n          shadow[1] > 0 ? (height -= shadow[1]) : (height += shadow[1], y -= shadow[1])\n        }\n        // 绘制背景\n        ctx.fillStyle = this.handleBackground(x, y, width, height, background)\n        const borderRadius = this.getLength(cell.borderRadius ? cell.borderRadius : _defaultStyle.borderRadius)\n        roundRectByArc(ctx, x, y, width, height, borderRadius)\n        ctx.fill()\n        // 清空阴影\n        ctx.shadowColor = 'rgba(0, 0, 0, 0)'\n        ctx.shadowOffsetX = 0\n        ctx.shadowOffsetY = 0\n        ctx.shadowBlur = 0\n      }\n      // 修正图片缓存\n      let cellName = 'prizes'\n      if (cellIndex >= this.prizes.length) {\n        cellName = 'buttons'\n        cellIndex -= this.prizes.length\n      }\n      // 绘制图片\n      cell.imgs && cell.imgs.forEach((imgInfo, imgIndex) => {\n        const cellImg = this.ImageCache.get(imgInfo.src)\n        const activeImg = this.ImageCache.get(imgInfo['activeSrc'])\n        if (!cellImg) return\n        const renderImg = (isActive && activeImg) || cellImg\n        if (!renderImg) return\n        const [trueWidth, trueHeight] = this.computedWidthAndHeight(renderImg, imgInfo, width, height)\n        const [xAxis, yAxis] = [\n          x + this.getOffsetX(trueWidth, width) + this.getLength(imgInfo.left, width),\n          y + this.getLength(imgInfo.top, height)\n        ]\n        this.drawImage(ctx, renderImg, xAxis, yAxis, trueWidth, trueHeight)\n      })\n      // 绘制文字\n      cell.fonts && cell.fonts.forEach(font => {\n        // 字体样式\n        const style = isActive && _activeStyle.fontStyle\n          ? _activeStyle.fontStyle\n          : (font.fontStyle || _defaultStyle.fontStyle)\n        // 字体加粗\n        const fontWeight = isActive && _activeStyle.fontWeight\n          ? _activeStyle.fontWeight\n          : (font.fontWeight || _defaultStyle.fontWeight)\n        // 字体大小\n        const size = isActive && _activeStyle.fontSize\n          ? this.getLength(_activeStyle.fontSize)\n          : this.getLength(font.fontSize || _defaultStyle.fontSize)\n        // 字体行高\n        const lineHeight = isActive && _activeStyle.lineHeight\n          ? _activeStyle.lineHeight\n          : font.lineHeight || _defaultStyle.lineHeight || font.fontSize || _defaultStyle.fontSize\n        const wordWrap = has(font, 'wordWrap') ? font.wordWrap : _defaultStyle.wordWrap\n        const lengthLimit = font.lengthLimit || _defaultStyle.lengthLimit\n        const lineClamp = font.lineClamp || _defaultStyle.lineClamp\n        ctx.font = `${fontWeight} ${size >> 0}px ${style}`\n        ctx.fillStyle = (isActive && _activeStyle.fontColor) ? _activeStyle.fontColor : (font.fontColor || _defaultStyle.fontColor)\n        let lines = [], text = String(font.text)\n        // 计算文字换行\n        if (wordWrap) {\n          // 最大宽度\n          let maxWidth = this.getLength(lengthLimit, width)\n          lines = splitText(ctx, removeEnter(text), () => maxWidth, lineClamp)\n        } else {\n          lines = text.split('\\n')\n        }\n        lines.forEach((line, lineIndex) => {\n          ctx.fillText(\n            line,\n            x + this.getOffsetX(ctx.measureText(line).width, width) + this.getLength(font.left, width),\n            y + this.getLength(font.top, height) + (lineIndex + 1) * this.getLength(lineHeight)\n          )\n        })\n      })\n    })\n    // 触发绘制后回调\n    config.afterDraw?.call(this, ctx)\n  }\n\n  /**\n   * 处理背景色\n   * @param x\n   * @param y\n   * @param width\n   * @param height\n   * @param background\n   * @param isActive\n   */\n  private handleBackground (\n    x: number,\n    y: number,\n    width: number,\n    height: number,\n    background: string,\n  ) {\n    const { ctx } = this\n    // 处理线性渐变\n    if (background.includes('linear-gradient')) {\n      background = getLinearGradient(ctx, x, y, width, height, background)\n    }\n    return background\n  }\n\n  /**\n   * 刻舟求剑\n   */\n  private carveOnGunwaleOfAMovingBoat (): void {\n    const { _defaultConfig, prizeFlag, currIndex } = this\n    this.endTime = Date.now()\n    const stopIndex = this.stopIndex = currIndex\n    const speed = _defaultConfig.speed\n    let i = 0, prevSpeed = 0, prevIndex = 0\n    while (++i) {\n      const endIndex = this.prizes.length * i + prizeFlag! - (stopIndex)\n      const currSpeed = quad.easeOut(this.FPS, stopIndex, endIndex, _defaultConfig.decelerationTime) - stopIndex\n      if (currSpeed > speed) {\n        this.endIndex = (speed - prevSpeed > currSpeed - speed) ? endIndex : prevIndex\n        break\n      }\n      prevIndex = endIndex\n      prevSpeed = currSpeed\n    }\n  }\n\n  /**\n   * 对外暴露: 开始抽奖方法\n   */\n  public play (): void {\n    if (this.step !== 0) return\n    // 记录游戏开始的时间\n    this.startTime = Date.now()\n    // 重置中奖索引\n    this.prizeFlag = void 0\n    // 开始加速\n    this.step = 1\n    // 触发回调\n    this.config.afterStart?.()\n    // 开始运行\n    this.run()\n  }\n\n  /**\n   * 对外暴露: 缓慢停止方法\n   * @param index 中奖索引\n   */\n  public stop (index?: number): void {\n    if (this.step === 0 || this.step === 3) return\n    // 如果没有传递中奖索引, 则通过range属性计算一个\n    if (!index && index !== 0) {\n      const rangeArr = this.prizes.map(item => item.range)\n      index = computeRange(rangeArr)\n    }\n    // 如果index是负数则停止游戏, 反之则传递中奖索引\n    if (index < 0) {\n      this.step = 0\n      this.prizeFlag = -1\n    } else {\n      this.step = 2\n      this.prizeFlag = index % this.prizes.length\n    }\n  }\n\n  /**\n   * 实际开始执行方法\n   * @param num 记录帧动画执行多少次\n   */\n  private run (num: number = 0): void {\n    const { rAF, step, prizes, prizeFlag, _defaultConfig } = this\n    const { accelerationTime, decelerationTime, speed } = _defaultConfig\n    // 结束游戏\n    if (step === 0) {\n      this.endCallback?.(this.prizes.find((prize, index) => index === prizeFlag) || {})\n      return\n    }\n    // 如果等于 -1 就直接停止游戏\n    if (prizeFlag === -1) return\n    // 计算结束位置\n    if (step === 3 && !this.endIndex) this.carveOnGunwaleOfAMovingBoat()\n    // 计算时间间隔\n    const startInterval = Date.now() - this.startTime\n    const endInterval = Date.now() - this.endTime\n    let currIndex = this.currIndex\n    // \n    if (step === 1 || startInterval < accelerationTime) { // 加速阶段\n      // 记录帧率\n      this.FPS = startInterval / num\n      const currSpeed = quad.easeIn(startInterval, 0.1, speed - 0.1, accelerationTime)\n      // 加速到峰值后, 进入匀速阶段\n      if (currSpeed === speed) {\n        this.step = 2\n      }\n      currIndex = currIndex + currSpeed % prizes.length\n    } else if (step === 2) { // 匀速阶段\n      // 速度保持不变\n      currIndex = currIndex + speed % prizes.length\n      // 如果 prizeFlag 有值, 则进入减速阶段\n      if (prizeFlag !== void 0 && prizeFlag >= 0) {\n        this.step = 3\n        // 清空上一次的位置信息\n        this.stopIndex = 0\n        this.endIndex = 0\n      }\n    } else if (step === 3) { // 减速阶段\n      // 开始缓慢停止\n      currIndex = quad.easeOut(endInterval, this.stopIndex, this.endIndex, decelerationTime)\n      if (endInterval >= decelerationTime) {\n        this.step = 0\n      }\n    } else {\n      // 出现异常\n      this.stop(-1)\n    }\n    this.currIndex = currIndex\n    this.draw()\n    rAF(this.run.bind(this, num + 1))\n  }\n\n  /**\n   * 计算奖品格子的几何属性\n   * @param { array } [...矩阵坐标, col, row]\n   * @return { array } [...真实坐标, width, height]\n   */\n  private getGeometricProperty ([x, y, col = 1, row = 1]: number[]) {\n    const { cellWidth, cellHeight } = this\n    const gutter = this._defaultConfig.gutter\n    let res = [\n      this.prizeArea!.x + (cellWidth + gutter) * x,\n      this.prizeArea!.y + (cellHeight + gutter) * y\n    ]\n    col && row && res.push(\n      cellWidth * col + gutter * (col - 1),\n      cellHeight * row + gutter * (row - 1),\n    )\n    return res\n  }\n\n  /**\n   * 换算渲染坐标\n   * @param x\n   * @param y\n   */\n  protected conversionAxis (x: number, y: number): [number, number] {\n    const { config } = this\n    return [x / config.dpr, y / config.dpr]\n  }\n}\n", "import Lucky from './lucky'\nimport { UserConfigType, ImgType, ImgItemType, Tuple } from '../types/index'\nimport SlotMachineConfig, {\n  BlockType,\n  PrizeType,\n  SlotType,\n  DefaultConfigType,\n  DefaultStyleType,\n  EndCallbackType,\n} from '../types/slot'\nimport {\n  get, has,\n  isExpectType,\n  removeEnter,\n  computePadding,\n  hasBackground,\n  computeRange,\n  splitText,\n  getSortedArrayByIndex\n} from '../utils/index'\nimport { roundRectByArc } from '../utils/math'\nimport { quad } from '../utils/tween'\n\nexport default class SlotMachine extends Lucky {\n  // 背景\n  private blocks: Array<BlockType> = []\n  // 奖品列表\n  private prizes: Array<PrizeType> = []\n  // 插槽列表\n  private slots: Array<SlotType> = []\n  // 默认配置\n  private defaultConfig: DefaultConfigType = {}\n  private _defaultConfig: Required<DefaultConfigType> = {} as Required<DefaultConfigType>\n  // 默认样式\n  private defaultStyle: DefaultStyleType = {}\n  private _defaultStyle: Required<DefaultStyleType> = {} as Required<DefaultStyleType>\n  private endCallback: EndCallbackType = () => {}\n  // 离屏canvas\n  private _offscreenCanvas?: HTMLCanvasElement\n  private cellWidth = 0             // 格子宽度\n  private cellHeight = 0            // 格子高度\n  private cellAndSpacing = 0        // 格子+间距\n  private widthAndSpacing = 0       // 格子宽度+列间距\n  private heightAndSpacing = 0      // 格子高度+行间距\n  private FPS = 16.6                // 屏幕刷新率\n  private scroll: number[] = []     // 滚动的长度\n  private stopScroll: number[] = [] // 刻舟求剑\n  private endScroll: number[] = []  // 最终停止的长度\n  private startTime = 0             // 开始游戏的时间\n  private endTime = 0               // 开始停止的时间\n  /**\n   * 游戏当前的阶段\n   * step = 0 时, 游戏尚未开始\n   * step = 1 时, 此时处于加速阶段\n   * step = 2 时, 此时处于匀速阶段\n   * step = 3 时, 此时处于减速阶段\n   */\n  private step: 0 | 1 | 2 | 3 = 0\n  /**\n   * 中奖索引\n   * prizeFlag = undefined 时, 处于开始抽奖阶段, 正常旋转\n   * prizeFlag >= 0 时, 说明stop方法被调用, 并且传入了中奖索引\n   * prizeFlag === -1 时, 说明stop方法被调用, 并且传入了负值, 本次抽奖无效\n   */\n  private prizeFlag: number[] | undefined = void 0\n  // 奖品区域几何信息\n  private prizeArea?: { x: number, y: number, w: number, h: number }\n  // 图片缓存\n  private ImageCache = new Map()\n\n  /**\n   * 老虎机构造器\n   * @param config 配置项\n   * @param data 抽奖数据\n   */\n   constructor (config: UserConfigType, data: SlotMachineConfig) {\n    super(config, {\n      width: data.width,\n      height: data.height\n    })\n    this.initData(data)\n    this.initWatch()\n    this.initComputed()\n    // 创建前回调函数\n    config.beforeCreate?.call(this)\n    // 首次初始化\n    this.init()\n  }\n\n  protected resize(): void {\n    super.resize()\n    this.draw()\n    this.config.afterResize?.()\n  }\n\n  protected initLucky (): void {\n    this.cellWidth = 0\n    this.cellHeight = 0\n    this.cellAndSpacing = 0\n    this.widthAndSpacing = 0\n    this.heightAndSpacing = 0\n    this.FPS = 16.6\n    this.scroll = []\n    this.stopScroll = []\n    this.endScroll = []\n    this.startTime = 0\n    this.endTime = 0\n    this.prizeFlag = void 0\n    this.step = 0\n    super.initLucky()\n  }\n\n  /**\n   * 初始化数据\n   * @param data\n   */\n  private initData (data: SlotMachineConfig): void {\n    this.$set(this, 'width', data.width)\n    this.$set(this, 'height', data.height)\n    this.$set(this, 'blocks', data.blocks || [])\n    this.$set(this, 'prizes', data.prizes || [])\n    this.$set(this, 'slots', data.slots || [])\n    this.$set(this, 'defaultConfig', data.defaultConfig || {})\n    this.$set(this, 'defaultStyle', data.defaultStyle || {})\n    this.$set(this, 'endCallback', data.end)\n  }\n\n  /**\n   * 初始化属性计算\n   */\n  private initComputed (): void {\n    // 默认配置\n    this.$computed(this, '_defaultConfig', () => {\n      const config = {\n        mode: 'vertical',\n        rowSpacing: 0,\n        colSpacing: 5,\n        speed: 20,\n        direction: 1,\n        accelerationTime: 2500,\n        decelerationTime: 2500,\n        ...this.defaultConfig\n      }\n      config.rowSpacing = this.getLength(config.rowSpacing)\n      config.colSpacing = this.getLength(config.colSpacing)\n      return config\n    })\n    // 默认样式\n    this.$computed(this, '_defaultStyle', () => {\n      return {\n        borderRadius: 0,\n        fontColor: '#000',\n        fontSize: '18px',\n        fontStyle: 'sans-serif',\n        fontWeight: '400',\n        background: 'rgba(0,0,0,0)',\n        wordWrap: true,\n        lengthLimit: '90%',\n        ...this.defaultStyle\n      }\n    })\n  }\n\n  /**\n   * 初始化观察者\n   */\n  private initWatch (): void {\n    // 重置宽度\n    this.$watch('width', (newVal: string | number) => {\n      this.data.width = newVal\n      this.resize()\n    })\n    // 重置高度\n    this.$watch('height', (newVal: string | number) => {\n      this.data.height = newVal\n      this.resize()\n    })\n    // 监听 blocks 数据的变化\n    this.$watch('blocks', (newData: Array<BlockType>) => {\n      this.initImageCache()\n    }, { deep: true })\n    // 监听 prizes 数据的变化\n    this.$watch('prizes', (newData: Array<PrizeType>) => {\n      this.initImageCache()\n    }, { deep: true })\n    // 监听 prizes 数据的变化\n    this.$watch('slots', (newData: Array<PrizeType>) => {\n      this.drawOffscreenCanvas()\n      this.draw()\n    }, { deep: true })\n    this.$watch('defaultConfig', () => this.draw(), { deep: true })\n    this.$watch('defaultStyle', () => this.draw(), { deep: true })\n    this.$watch('endCallback', () => this.init())\n  }\n\n  /**\n   * 初始化 canvas 抽奖\n   */\n  public async init (): Promise<void> {\n    this.initLucky()\n    const { config } = this\n    // 初始化前回调函数\n    config.beforeInit?.call(this)\n    // 先绘制一次\n    this.drawOffscreenCanvas()\n    this.draw()\n    // 异步加载图片\n    await this.initImageCache()\n    // 初始化后回调函数\n    config.afterInit?.call(this)\n  }\n\n  private initImageCache (): Promise<void> {\n    return new Promise((resolve) => {\n      const willUpdateImgs = {\n        blocks: this.blocks.map(block => block.imgs),\n        prizes: this.prizes.map(prize => prize.imgs),\n      }\n      ;(<(keyof typeof willUpdateImgs)[]>Object.keys(willUpdateImgs)).forEach(imgName => {\n        const willUpdate = willUpdateImgs[imgName]\n        // 循环遍历所有图片\n        const allPromise: Promise<void>[] = []\n        willUpdate && willUpdate.forEach((imgs, cellIndex) => {\n          imgs && imgs.forEach((imgInfo, imgIndex) => {\n            allPromise.push(this.loadAndCacheImg(imgName, cellIndex, imgIndex))\n          })\n        })\n        Promise.all(allPromise).then(() => {\n          this.drawOffscreenCanvas()\n          this.draw()\n          resolve()\n        })\n      })\n    })\n  }\n\n  /**\n   * 根据索引单独加载指定图片并缓存\n   * @param cellName 模块名称\n   * @param cellIndex 模块索引\n   * @param imgName 模块对应的图片缓存\n   * @param imgIndex 图片索引\n   */\n  private async loadAndCacheImg (\n    cellName: 'blocks' | 'prizes',\n    cellIndex: number,\n    imgIndex: number\n  ): Promise<void> {\n    return new Promise((resolve, reject) => {\n      let cell: BlockType | PrizeType = this[cellName][cellIndex]\n      if (!cell || !cell.imgs) return\n      const imgInfo = cell.imgs[imgIndex]\n      if (!imgInfo) return\n      // 异步加载图片\n      this.loadImg(imgInfo.src, imgInfo).then(async currImg => {\n        if (typeof imgInfo.formatter === 'function') {\n          currImg = await Promise.resolve(imgInfo.formatter.call(this, currImg))\n        }\n        this.ImageCache.set(imgInfo['src'], currImg)\n        resolve()\n      }).catch(err => {\n        console.error(`${cellName}[${cellIndex}].imgs[${imgIndex}] ${err}`)\n        reject()\n      })\n    })\n  }\n\n  /**\n   * 绘制离屏canvas\n   */\n  protected drawOffscreenCanvas (): void {\n    const { _defaultConfig, _defaultStyle } = this\n    const { w, h } = this.drawBlocks()!\n    // 计算单一奖品格子的宽度和高度\n    const prizesLen = this.prizes.length\n    const { cellWidth, cellHeight, widthAndSpacing, heightAndSpacing } = this.displacementWidthOrHeight()\n    const defaultOrder = new Array(prizesLen).fill(void 0).map((v, i) => i)\n    let maxOrderLen = 0, maxOffWidth = 0, maxOffHeight = 0\n    this.slots.forEach((slot, slotIndex) => {\n      // 初始化 scroll 的值\n      if (this.scroll[slotIndex] === void 0) this.scroll[slotIndex] = 0\n      // 如果没有order属性, 就填充prizes\n      slot.order = slot.order || defaultOrder\n      // 计算最大值\n      const currLen = slot.order.length\n      maxOrderLen = Math.max(maxOrderLen, currLen)\n      maxOffWidth = Math.max(maxOffWidth, w + widthAndSpacing * currLen)\n      maxOffHeight = Math.max(maxOffHeight, h + heightAndSpacing * currLen)\n    })\n    // 创建一个离屏Canvas来存储画布的内容\n    const { _offscreenCanvas, _ctx } = this.getOffscreenCanvas(maxOffWidth, maxOffHeight)!\n    this._offscreenCanvas = _offscreenCanvas\n    // 绘制插槽\n    this.slots.forEach((slot, slotIndex) => {\n      const cellX = cellWidth * slotIndex\n      const cellY = cellHeight * slotIndex\n      let lengthOfCopy = 0\n      // 绘制奖品\n      const newPrizes = getSortedArrayByIndex(this.prizes, slot.order!)\n      // 如果没有奖品则打断逻辑\n      if (!newPrizes.length) return\n      newPrizes.forEach((cell, cellIndex) => {\n        if (!cell) return\n        const orderIndex = slot.order![cellIndex]\n        const prizesX = widthAndSpacing * cellIndex + _defaultConfig.colSpacing / 2\n        const prizesY = heightAndSpacing * cellIndex + _defaultConfig.rowSpacing / 2\n        const [_x, _y, spacing] = this.displacement(\n          [cellX, prizesY, heightAndSpacing],\n          [prizesX, cellY, widthAndSpacing]\n        )\n        lengthOfCopy += spacing\n        // 绘制背景\n        const background = cell.background || _defaultStyle.background\n        if (hasBackground(background)) {\n          const borderRadius = this.getLength(has(cell, 'borderRadius') ? cell.borderRadius : _defaultStyle.borderRadius)\n          roundRectByArc(_ctx, _x, _y, cellWidth, cellWidth, borderRadius)\n          _ctx.fillStyle = background\n          _ctx.fill()\n        }\n        // 绘制图片\n        cell.imgs && cell.imgs.forEach((imgInfo, imgIndex) => {\n          const cellImg = this.ImageCache.get(imgInfo.src)\n          if (!cellImg) return\n          const [trueWidth, trueHeight] = this.computedWidthAndHeight(cellImg, imgInfo, cellWidth, cellHeight)\n          const [xAxis, yAxis] = [\n            _x + this.getOffsetX(trueWidth, cellWidth) + this.getLength(imgInfo.left, cellWidth),\n            _y + this.getLength(imgInfo.top, cellHeight)\n          ]\n          this.drawImage(_ctx, cellImg, xAxis, yAxis, trueWidth, trueHeight)\n        })\n        // 绘制文字\n        cell.fonts && cell.fonts.forEach(font => {\n          // 字体样式\n          const style = font.fontStyle || _defaultStyle.fontStyle\n          // 字体加粗\n          const fontWeight = font.fontWeight || _defaultStyle.fontWeight\n          // 字体大小\n          const size = this.getLength(font.fontSize || _defaultStyle.fontSize)\n          // 字体行高\n          const lineHeight = font.lineHeight || _defaultStyle.lineHeight || font.fontSize || _defaultStyle.fontSize\n          const wordWrap = has(font, 'wordWrap') ? font.wordWrap : _defaultStyle.wordWrap\n          const lengthLimit = font.lengthLimit || _defaultStyle.lengthLimit\n          const lineClamp = font.lineClamp || _defaultStyle.lineClamp\n          _ctx.font = `${fontWeight} ${size >> 0}px ${style}`\n          _ctx.fillStyle = font.fontColor || _defaultStyle.fontColor\n          let lines = [], text = String(font.text)\n          // 计算文字换行\n          if (wordWrap) {\n            // 最大宽度\n            let maxWidth = this.getLength(lengthLimit, cellWidth)\n            lines = splitText(_ctx, removeEnter(text), () => maxWidth, lineClamp)\n          } else {\n            lines = text.split('\\n')\n          }\n          lines.forEach((line, lineIndex) => {\n            _ctx.fillText(\n              line,\n              _x + this.getOffsetX(_ctx.measureText(line).width, cellWidth) + this.getLength(font.left, cellWidth),\n              _y + this.getLength(font.top, cellHeight) + (lineIndex + 1) * this.getLength(lineHeight)\n            )\n          })\n        })\n      })\n      const [_x, _y, _w, _h] = this.displacement(\n        [cellX, 0, cellWidth, lengthOfCopy],\n        [0, cellY, lengthOfCopy, cellHeight]\n      )\n      let drawLen = lengthOfCopy\n      while (drawLen < maxOffHeight + lengthOfCopy) {\n        const [drawX, drawY] = this.displacement([_x, drawLen], [drawLen, _y])\n        this.drawImage(\n          _ctx, _offscreenCanvas,\n          _x, _y, _w, _h,\n          drawX, drawY, _w, _h\n        )\n        drawLen += lengthOfCopy\n      }\n    })\n  }\n\n  /**\n   * 绘制背景区域\n   */\n  protected drawBlocks (): SlotMachine['prizeArea'] {\n    const { config, ctx, _defaultConfig, _defaultStyle } = this\n    // 绘制背景区域, 并计算奖品区域\n    return this.prizeArea = this.blocks.reduce(({x, y, w, h}, block, blockIndex) => {\n      const [paddingTop, paddingBottom, paddingLeft, paddingRight] = computePadding(block, this.getLength.bind(this))\n      const r = block.borderRadius ? this.getLength(block.borderRadius) : 0\n      // 绘制边框\n      const background = block.background || _defaultStyle.background\n      if (hasBackground(background)) {\n        roundRectByArc(ctx, x, y, w, h, r)\n        ctx.fillStyle = background\n        ctx.fill()\n      }\n      // 绘制图片\n      block.imgs && block.imgs.forEach((imgInfo, imgIndex) => {\n        const blockImg = this.ImageCache.get(imgInfo.src)\n        if (!blockImg) return\n        // 绘制图片\n        const [trueWidth, trueHeight] = this.computedWidthAndHeight(blockImg, imgInfo, w, h)\n        const [xAxis, yAxis] = [this.getOffsetX(trueWidth, w) + this.getLength(imgInfo.left, w), this.getLength(imgInfo.top, h)]\n        this.drawImage(ctx, blockImg, x + xAxis, y + yAxis, trueWidth, trueHeight)\n      })\n      return {\n        x: x + paddingLeft,\n        y: y + paddingTop,\n        w: w - paddingLeft - paddingRight,\n        h: h - paddingTop - paddingBottom\n      }\n    }, { x: 0, y: 0, w: this.boxWidth, h: this.boxHeight })\n  }\n\n  /**\n   * 绘制老虎机抽奖\n   */\n  protected draw (): void {\n    const { config, ctx, _defaultConfig, _defaultStyle } = this\n    // 触发绘制前回调\n    config.beforeDraw?.call(this, ctx)\n    // 清空画布\n    ctx.clearRect(0, 0, this.boxWidth, this.boxHeight)\n    // 绘制背景\n    const { x, y, w, h } = this.drawBlocks()!\n    // 绘制插槽\n    if (!this._offscreenCanvas) return\n    const { cellWidth, cellHeight, cellAndSpacing, widthAndSpacing, heightAndSpacing } = this\n    this.slots.forEach((slot, slotIndex) => {\n      // 绘制临界点\n      const _p = cellAndSpacing * slot.order!.length\n      // 调整奖品垂直居中\n      const start = this.displacement(-(h - heightAndSpacing) / 2, -(w - widthAndSpacing) / 2)\n      let scroll = this.scroll[slotIndex] + start\n      // scroll 会无限累加 1 / -1\n      if (scroll < 0) {\n        scroll = scroll % _p + _p\n      }\n      if (scroll > _p) {\n        scroll = scroll % _p\n      }\n      const [sx, sy, sw, sh] = this.displacement(\n        [cellWidth * slotIndex, scroll, cellWidth, h],\n        [scroll, cellHeight * slotIndex, w, cellHeight]\n      )\n      const [dx, dy, dw, dh] = this.displacement(\n        [x + widthAndSpacing * slotIndex, y, cellWidth, h],\n        [x, y + heightAndSpacing * slotIndex, w, cellHeight]\n      )\n      this.drawImage(ctx, this._offscreenCanvas!, sx, sy, sw, sh, dx, dy, dw, dh)\n    })\n  }\n\n  /**\n   * 刻舟求剑\n   */\n  private carveOnGunwaleOfAMovingBoat (): void {\n    const { _defaultConfig, prizeFlag, cellAndSpacing } = this\n    // 记录开始停止的时间戳\n    this.endTime = Date.now()\n    this.slots.forEach((slot, slotIndex) => {\n      const order = slot.order!\n      if (!order.length) return\n      const speed = slot.speed || _defaultConfig.speed\n      const direction = slot.direction || _defaultConfig.direction\n      const orderIndex = order.findIndex(prizeIndex => prizeIndex === prizeFlag![slotIndex])\n      const _p = cellAndSpacing * order.length\n      const stopScroll = this.stopScroll[slotIndex] = this.scroll[slotIndex]\n      let i = 0\n      while (++i) {\n        const endScroll = cellAndSpacing * orderIndex + (_p * i * direction) - stopScroll\n        const currSpeed = quad.easeOut(this.FPS, stopScroll, endScroll, _defaultConfig.decelerationTime) - stopScroll\n        if (Math.abs(currSpeed) > speed) {\n          this.endScroll[slotIndex] = endScroll\n          break\n        }\n      }\n    })\n  }\n\n  /**\n   * 对外暴露: 开始抽奖方法\n   */\n   public play (): void {\n    if (this.step !== 0) return\n    // 记录开始游戏的时间\n    this.startTime = Date.now()\n    // 清空中奖索引\n    this.prizeFlag = void 0\n    // 开始加速\n    this.step = 1\n    // 触发回调\n    this.config.afterStart?.()\n    // 开始渲染\n    this.run()\n  }\n\n  public stop (index: number | number[]): void {\n    if (this.step === 0 || this.step === 3) return\n    // 设置中奖索引\n    if (typeof index === 'number') {\n      this.prizeFlag = new Array(this.slots.length).fill(index)\n    } else if (isExpectType(index, 'array')) {\n      if (index.length === this.slots.length) {\n        this.prizeFlag = index\n      } else {\n        this.stop(-1)\n        return console.error(`stop([${index}]) 参数长度的不正确`)\n      }\n    } else {\n      this.stop(-1)\n      return console.error(`stop() 无法识别的参数类型 ${typeof index}`)\n    }\n    // 如果包含负数则停止游戏, 反之则继续游戏\n    if (this.prizeFlag?.includes(-1)) {\n      this.prizeFlag = []\n      // 停止游戏\n      this.step = 0\n    } else {\n      // 进入匀速\n      this.step = 2\n    }\n  }\n\n  /**\n   * 让游戏动起来\n   * @param num 记录帧动画执行多少次\n   */\n  private run (num: number = 0): void {\n    const { rAF, step, prizeFlag, _defaultConfig, cellAndSpacing, slots } = this\n    const { accelerationTime, decelerationTime } = _defaultConfig\n    // 游戏结束\n    if (this.step === 0 && prizeFlag?.length === slots.length) {\n      let flag = prizeFlag[0]\n      for (let i = 0; i < slots.length; i++) {\n        const slot = slots[i], currFlag = prizeFlag[i]\n        if (!slot.order?.includes(currFlag) || flag !== currFlag) {\n          flag = -1\n          break\n        }\n      }\n      this.endCallback?.(this.prizes.find((prize, index) => index === flag) || void 0)\n      return\n    }\n    // 如果长度为 0 就直接停止游戏\n    if (prizeFlag !== void 0 && !prizeFlag.length) return\n    // 计算最终停止的位置\n    if (this.step === 3 && !this.endScroll.length) this.carveOnGunwaleOfAMovingBoat()\n    // 计算时间间隔\n    const startInterval = Date.now() - this.startTime\n    const endInterval = Date.now() - this.endTime\n    // 分别计算对应插槽的加速度\n    slots.forEach((slot, slotIndex) => {\n      const order = slot.order\n      if (!order || !order.length) return\n      const _p = cellAndSpacing * order.length\n      const speed = Math.abs(slot.speed || _defaultConfig.speed)\n      const direction = slot.direction || _defaultConfig.direction\n      let scroll = 0, prevScroll = this.scroll[slotIndex]\n      if (step === 1 || startInterval < accelerationTime) { // 加速阶段\n        // 记录帧率\n        this.FPS = startInterval / num\n        const currSpeed = quad.easeIn(startInterval, 0, speed, accelerationTime)\n        // 加速到最大速度后, 即可进入匀速阶段\n        if (currSpeed === speed) {\n          this.step = 2\n        }\n        scroll = (prevScroll + (currSpeed * direction)) % _p\n      } else if (step === 2) { // 匀速阶段\n        // 速度保持不变\n        scroll = (prevScroll + (speed * direction)) % _p\n        // 如果有 prizeFlag 有值, 则进入减速阶段\n        if (prizeFlag?.length === slots.length) {\n          this.step = 3\n          // 清空上一轮的位置信息\n          this.stopScroll = []\n          this.endScroll = []\n        }\n      } else if (step === 3 && endInterval) { // 减速阶段\n        // 开始缓慢停止\n        const stopScroll = this.stopScroll[slotIndex]\n        const endScroll = this.endScroll[slotIndex]\n        scroll = quad.easeOut(endInterval, stopScroll, endScroll, decelerationTime)\n        if (endInterval >= decelerationTime) {\n          this.step = 0\n        }\n      }\n      this.scroll[slotIndex] = scroll\n    })\n    this.draw()\n    rAF(this.run.bind(this, num + 1))\n  }\n\n  // 根据mode置换数值\n  private displacement<T> (a: T, b: T): T {\n    return this._defaultConfig.mode === 'horizontal' ? b : a\n  }\n\n  // 根据mode计算宽高\n  private displacementWidthOrHeight () {\n    const mode = this._defaultConfig.mode\n    const slotsLen = this.slots.length\n    const { colSpacing, rowSpacing } = this._defaultConfig\n    const { x, y, w, h } = this.prizeArea || this.drawBlocks()!\n    let cellWidth = 0, cellHeight = 0, widthAndSpacing = 0, heightAndSpacing = 0\n    if (mode === 'horizontal') {\n      cellHeight = this.cellHeight = (h - rowSpacing * (slotsLen - 1)) / slotsLen\n      cellWidth = this.cellWidth = cellHeight\n    } else {\n      cellWidth = this.cellWidth = (w - colSpacing * (slotsLen - 1)) / slotsLen\n      cellHeight = this.cellHeight = cellWidth\n    }\n    widthAndSpacing = this.widthAndSpacing = this.cellWidth + colSpacing\n    heightAndSpacing = this.heightAndSpacing = this.cellHeight + rowSpacing\n    if (mode === 'horizontal') {\n      this.cellAndSpacing = widthAndSpacing\n    } else {\n      this.cellAndSpacing = heightAndSpacing\n    }\n    return {\n      cellWidth,\n      cellHeight,\n      widthAndSpacing,\n      heightAndSpacing,\n    }\n  }\n}\n", "import { ImgType } from '../types/index'\nimport { roundRectByArc } from './math'\n\n/**\n * 根据路径获取图片对象\n * @param { string } src 图片路径\n * @returns { Promise<HTMLImageElement> } 图片标签\n */\nexport const getImage = (src: string): Promise<ImgType> => {\n  return new Promise((resolve, reject) => {\n    const img = new Image()\n    img.onload = () => resolve(img)\n    img.onerror = err => reject(err)\n    img.src = src\n  })\n}\n\n/**\n * 切割圆角\n * @param img 将要裁剪的图片对象\n * @param radius 裁剪的圆角半径\n * @returns 返回一个离屏 canvas 用于渲染\n */\nexport const cutRound = (img: ImgType, radius: number): ImgType => {\n  const canvas = document.createElement('canvas')\n  const ctx = canvas.getContext('2d')!\n  const { width, height } = img\n  canvas.width = width\n  canvas.height = height\n  roundRectByArc(ctx, 0, 0, width, height, radius)\n  ctx.clip()\n  ctx.drawImage(img, 0, 0, width, height)\n  return canvas\n}\n\n/**\n * 透明度\n * @param img 将要处理的图片对象\n * @param opacity 透明度\n * @returns 返回一个离屏 canvas 用于渲染\n */\nexport const opacity = (\n  img: ImgType,\n  opacity: number\n): ImgType => {\n  const canvas = document.createElement('canvas')\n  const ctx = canvas.getContext('2d')!\n  const { width, height } = img\n  canvas.width = width\n  canvas.height = height\n  // 绘制图片, 部分浏览器不支持 filter 属性, 需要处理兼容\n  if (typeof ctx.filter === 'string') {\n    ctx.filter = `opacity(${opacity * 100}%)`\n    ctx.drawImage(img, 0, 0, width, height)\n  } else {\n    ctx.drawImage(img, 0, 0, width, height)\n    const imageData = ctx.getImageData(0, 0, width, height)\n    const { data } = imageData\n    const len = data.length\n    for (let i = 0; i < len; i += 4) {\n      const alpha = data[i + 3]\n      if (alpha !== 0) data[i + 3] = alpha * opacity\n    }\n    ctx.putImageData(imageData, 0, 0)\n  }\n  return canvas\n}\n\n/**\n * 权重矩阵\n * @param radius 模糊半径\n * @param sigma \n * @returns 返回一个权重和为1的矩阵\n */\nconst getMatrix = (radius: number, sigma?: number): number[] => {\n  sigma = sigma || radius / 3\n  const r = Math.ceil(radius)\n  const sigma_2 = sigma * sigma\n  const sigma2_2 = 2 * sigma_2\n  const denominator = 1 / (2 * Math.PI * sigma_2)\n  const matrix = []\n  let total = 0\n  // 计算权重矩阵\n  for (let x = -r; x <= r; x++) {\n    for (let y = -r; y <= r; y++) {\n      // 套用二维高斯函数得到每个点的权重\n      const res = denominator * Math.exp(-(x * x + y * y) / sigma2_2)\n      matrix.push(res)\n      total += res\n    }\n  }\n  // 让矩阵中所有权重的和等于1\n  for (let i = 0; i < matrix.length; i++) {\n    matrix[i] /= total\n  }\n  return matrix\n}\n\n/**\n * 高斯模糊\n * @param img 将要处理的图片对象\n * @param radius 模糊半径\n * @returns 返回一个离屏 canvas 用于渲染\n */\nexport const blur = (\n  img: ImgType,\n  radius: number\n): ImgType => {\n  const canvas = document.createElement('canvas')\n  const ctx = canvas.getContext('2d')!\n  const { width, height } = img\n  // 设置图片宽高\n  canvas.width = width\n  canvas.height = height\n  ctx.drawImage(img, 0, 0, width, height)\n  const ImageData = ctx.getImageData(0, 0, width, height)\n  const { data } = ImageData\n  const matrix = getMatrix(radius)\n  const r = Math.ceil(radius)\n  const w = width * 4\n  const cols = r * 2 + 1\n  const len = data.length, matrixLen = matrix.length\n  for (let i = 0; i < len; i += 4) {\n    // 处理\n  }\n  console.log(ImageData)\n  ctx.putImageData(ImageData, 0, 0)\n  return canvas\n}\n\nexport const getBase64Image = () => {\n\n}\n"], "names": ["Array", "prototype", "includes", "Object", "defineProperty", "value", "valueToFind", "fromIndex", "TypeError", "o", "len", "length", "n", "k", "Math", "max", "abs", "sameValueZero", "x", "y", "isNaN", "String", "search", "start", "indexOf", "find", "predicate", "thisArg", "arguments", "kValue", "call"], "mappings": ";;;;;;;;IAAA;IACA;AACA;IACA;IACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;IACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;IACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;IACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC1G,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC;AACF;IACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAChC,IAAI,IAAI,OAAO,CAAC,KAAK,UAAU,IAAI,CAAC,KAAK,IAAI;IAC7C,QAAQ,MAAM,IAAI,SAAS,CAAC,sBAAsB,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,+BAA+B,CAAC,CAAC;IAClG,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IACzF,CAAC;AACD;IACO,IAAI,QAAQ,GAAG,WAAW;IACjC,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,EAAE;IACrD,QAAQ,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC7D,YAAY,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;IAC7B,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACzF,SAAS;IACT,QAAQ,OAAO,CAAC,CAAC;IACjB,MAAK;IACL,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,EAAC;AA4BD;IACO,SAAS,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;IAC7D,IAAI,SAAS,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAChH,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,OAAO,EAAE,MAAM,EAAE;IAC/D,QAAQ,SAAS,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACnG,QAAQ,SAAS,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACtG,QAAQ,SAAS,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE;IACtH,QAAQ,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAC9E,KAAK,CAAC,CAAC;IACP,CAAC;AACD;IACO,SAAS,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE;IAC3C,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrH,IAAI,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,UAAU,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7J,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACtE,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;IACtB,QAAQ,IAAI,CAAC,EAAE,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC;IACtE,QAAQ,OAAO,CAAC,EAAE,IAAI;IACtB,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACzK,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;IACpD,YAAY,QAAQ,EAAE,CAAC,CAAC,CAAC;IACzB,gBAAgB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM;IAC9C,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACxE,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;IACjE,gBAAgB,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IACjE,gBAAgB;IAChB,oBAAoB,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE;IAChI,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;IAC1G,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE;IACzF,oBAAoB,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE;IACvF,oBAAoB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;IAC1C,oBAAoB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;IAC3C,aAAa;IACb,YAAY,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACvC,SAAS,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;IAClE,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IACzF,KAAK;IACL,CAAC;AA0DD;IACO,SAAS,aAAa,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;IAC9C,IAAI,IAAI,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IACzF,QAAQ,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE;IAChC,YAAY,IAAI,CAAC,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACjE,YAAY,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAC5B,SAAS;IACT,KAAK;IACL,IAAI,OAAO,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7D;;IC5KA;IACA;IACA;IAEA;IACA,IAAI,CAACA,KAAK,CAACC,SAAN,CAAgBC,QAArB,EAA+B;IAC7BC,EAAAA,MAAM,CAACC,cAAP,CAAsBJ,KAAK,CAACC,SAA5B,EAAuC,UAAvC,EAAmD;IACjDI,IAAAA,KAAK,EAAE,eAASC,WAAT,EAAsBC,SAAtB,EAAiC;IAEtC,UAAI,QAAQ,IAAZ,EAAkB;IAChB,cAAM,IAAIC,SAAJ,CAAc,+BAAd,CAAN;IACD,OAJqC;;;IAOtC,UAAIC,CAAC,GAAGN,MAAM,CAAC,IAAD,CAAd,CAPsC;;IAUtC,UAAIO,GAAG,GAAGD,CAAC,CAACE,MAAF,KAAa,CAAvB,CAVsC;;IAatC,UAAID,GAAG,KAAK,CAAZ,EAAe;IACb,eAAO,KAAP;IACD,OAfqC;IAkBtC;;;IACA,UAAIE,CAAC,GAAGL,SAAS,GAAG,CAApB,CAnBsC;IAsBtC;IACA;IACA;IACA;;IACA,UAAIM,CAAC,GAAGC,IAAI,CAACC,GAAL,CAASH,CAAC,IAAI,CAAL,GAASA,CAAT,GAAaF,GAAG,GAAGI,IAAI,CAACE,GAAL,CAASJ,CAAT,CAA5B,EAAyC,CAAzC,CAAR;;IAEA,eAASK,aAAT,CAAuBC,CAAvB,EAA0BC,CAA1B,EAA6B;IAC3B,eAAOD,CAAC,KAAKC,CAAN,IAAY,OAAOD,CAAP,KAAa,QAAb,IAAyB,OAAOC,CAAP,KAAa,QAAtC,IAAkDC,KAAK,CAACF,CAAD,CAAvD,IAA8DE,KAAK,CAACD,CAAD,CAAtF;IACD,OA9BqC;;;IAiCtC,aAAON,CAAC,GAAGH,GAAX,EAAgB;IACd;IACA;IACA,YAAIO,aAAa,CAACR,CAAC,CAACI,CAAD,CAAF,EAAOP,WAAP,CAAjB,EAAsC;IACpC,iBAAO,IAAP;IACD,SALa;;;IAOdO,QAAAA,CAAC;IACF,OAzCqC;;;IA4CtC,aAAO,KAAP;IACD;IA9CgD,GAAnD;IAgDD;;;IAGD,IAAI,CAACQ,MAAM,CAACpB,SAAP,CAAiBC,QAAtB,EAAgC;IAC9BmB,EAAAA,MAAM,CAACpB,SAAP,CAAiBC,QAAjB,GAA4B,UAASoB,MAAT,EAAiBC,KAAjB,EAAwB;;IAElD,QAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;IAC7BA,MAAAA,KAAK,GAAG,CAAR;IACD;;IACD,QAAIA,KAAK,GAAGD,MAAM,CAACX,MAAf,GAAwB,KAAKA,MAAjC,EAAyC;IACvC,aAAO,KAAP;IACD,KAFD,MAEO;IACL,aAAO,KAAKa,OAAL,CAAaF,MAAb,EAAqBC,KAArB,MAAgC,CAAC,CAAxC;IACD;IACF,GAVD;IAWD;;;IAGD,IAAI,CAACvB,KAAK,CAACC,SAAN,CAAgBwB,IAArB,EAA2B;IACzBtB,EAAAA,MAAM,CAACC,cAAP,CAAsBJ,KAAK,CAACC,SAA5B,EAAuC,MAAvC,EAA+C;IAC7CI,IAAAA,KAAK,EAAE,eAASqB,SAAT,EAAoB;IAC1B;IACC,UAAI,QAAQ,IAAZ,EAAkB;IAChB,cAAM,IAAIlB,SAAJ,CAAc,+BAAd,CAAN;IACD;;IACD,UAAIC,CAAC,GAAGN,MAAM,CAAC,IAAD,CAAd,CALyB;;IAOzB,UAAIO,GAAG,GAAGD,CAAC,CAACE,MAAF,KAAa,CAAvB,CAPyB;;IASzB,UAAI,OAAOe,SAAP,KAAqB,UAAzB,EAAqC;IACnC,cAAM,IAAIlB,SAAJ,CAAc,8BAAd,CAAN;IACD,OAXwB;;;IAazB,UAAImB,OAAO,GAAGC,SAAS,CAAC,CAAD,CAAvB,CAbyB;;IAezB,UAAIf,CAAC,GAAG,CAAR,CAfyB;;IAiBzB,aAAOA,CAAC,GAAGH,GAAX,EAAgB;IACd;IACA;IACA;IACA;IACA,YAAImB,MAAM,GAAGpB,CAAC,CAACI,CAAD,CAAd;;IACA,YAAIa,SAAS,CAACI,IAAV,CAAeH,OAAf,EAAwBE,MAAxB,EAAgChB,CAAhC,EAAmCJ,CAAnC,CAAJ,EAA2C;IACzC,iBAAOoB,MAAP;IACD,SARa;;;IAUdhB,QAAAA,CAAC;IACF,OA5BwB;;;IA8BzB,aAAO,KAAK,CAAZ;IACD;IAhC4C,GAA/C;IAkCD;;IC3GD;;;;;;IAMO,IAAM,YAAY,GAAG,UAAC,KAAc;QAAE,eAAkB;aAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;YAAlB,8BAAkB;;QAC7D,OAAO,KAAK,CAAC,IAAI,CAAC,UAAA,IAAI,IAAI,OAAA,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,IAAI,GAAA,CAAC,CAAA;IACtG,CAAC,CAAA;IAYM,IAAM,GAAG,GAAG,UAAC,IAAY,EAAE,GAAoB;QACpD,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACxD,CAAC,CAAA;IAED;;;;;IAKO,IAAM,WAAW,GAAG,UAAC,GAAW;QACrC,OAAO,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,UAAA,CAAC,IAAI,OAAA,CAAC,KAAK,IAAI,GAAA,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;IACtD,CAAC,CAAA;IAED;;;;IAIO,IAAM,SAAS,GAAG,UAAC,GAAY;QACpC,IAAI,GAAG,KAAK,IAAI;YAAE,OAAO,CAAC,CAAA;QAC1B,IAAI,OAAO,GAAG,KAAK,QAAQ;YAAE,OAAO,GAAG,CAAA;QACvC,IAAI,OAAO,GAAG,KAAK,QAAQ;YAAE,OAAO,GAAG,CAAA;QACvC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,IAAI,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;gBAC/B,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAA;aACtC;YACD,OAAO,MAAM,CAAC,GAAG,CAAC,CAAA;SACnB;QACD,OAAO,GAAG,CAAA;IACZ,CAAC,CAAA;IAED;;;;IAIO,IAAM,aAAa,GAAG,UAAC,KAAgC;QAC5D,IAAI,OAAO,KAAK,KAAK,QAAQ;YAAE,OAAO,KAAK,CAAA;QAC3C,KAAK,GAAG,KAAK,CAAC,iBAAiB,EAAE,CAAC,IAAI,EAAE,CAAA;QACxC,IAAI,KAAK,KAAK,aAAa;YAAE,OAAO,KAAK,CAAA;QACzC,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACvB,IAAM,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACxC,IAAI,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;gBAAE,OAAO,KAAK,CAAA;SACzC;QACD,OAAO,IAAI,CAAA;IACb,CAAC,CAAA;IAED;;;;IAIO,IAAM,cAAc,GAAG,UAC5B,KAA2B,EAC3B,SAAmB;;QAEnB,IAAI,OAAO,GAAG,CAAA,MAAA,KAAK,CAAC,OAAO,0CAAE,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,SAAS,CAAC,CAAC,CAAC,GAAA,CAAC,KAAI,CAAC,CAAC,CAAC,EACnE,UAAU,GAAG,CAAC,EACd,aAAa,GAAG,CAAC,EACjB,WAAW,GAAG,CAAC,EACf,YAAY,GAAG,CAAC,CAAA;QAClB,QAAQ,OAAO,CAAC,MAAM;YACpB,KAAK,CAAC;gBACJ,UAAU,GAAG,aAAa,GAAG,WAAW,GAAG,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;gBACpE,MAAK;YACP,KAAK,CAAC;gBACJ,UAAU,GAAG,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;gBACvC,WAAW,GAAG,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;gBACvC,MAAK;YACP,KAAK,CAAC;gBACJ,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;gBACvB,WAAW,GAAG,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;gBACvC,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;gBAC1B,MAAK;YACP;gBACE,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;gBACvB,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;gBAC1B,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;gBACxB,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;SAC5B;;QAED,IAAM,GAAG,GAAG,EAAE,UAAU,YAAA,EAAE,aAAa,eAAA,EAAE,WAAW,aAAA,EAAE,YAAY,cAAA,EAAE,CAAA;QACpE,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE;;YAEnB,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC;kBACtE,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;kBACrB,GAAG,CAAC,GAAG,CAAC,CAAA;SACb;QACD,OAAO,CAAC,UAAU,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,CAAC,CAAA;IAC/D,CAAC,CAAA;IAED;;;;;;IAMO,IAAM,QAAQ,GAAG,UAAC,EAAY,EAAE,IAAU;QAAV,qBAAA,EAAA,UAAU;QAC/C,IAAI,MAAM,GAAG,IAAW,CAAA;QACxB,OAAO;YAAA,iBAON;YAP2B,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YACxC,IAAI,MAAM;gBAAE,OAAM;YAClB,MAAM,GAAG,UAAU,CAAC;gBAClB,EAAE,CAAC,KAAK,CAAC,KAAI,EAAE,IAAI,CAAC,CAAA;gBACpB,YAAY,CAAC,MAAM,CAAC,CAAA;gBACpB,MAAM,GAAG,IAAI,CAAA;aACd,EAAE,IAAI,CAAC,CAAA;SACT,CAAA;IACH,CAAC,CAAA;IAED;;;;;IAKO,IAAM,YAAY,GAAG,UAAC,QAAmC;QAC9D,IAAM,YAAY,GAAa,EAAE,CAAA;;QAEjC,IAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,MAAM,CAAC,GAAG,CAAC,GAAA,CAAC,CAAC,MAAM,CAAC,UAAC,IAAI,EAAE,IAAI;YAC7D,IAAI,IAAI,GAAG,CAAC,EAAE;gBACZ,IAAM,GAAG,GAAG,IAAI,GAAG,IAAI,CAAA;gBACvB,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBACtB,OAAO,GAAG,CAAA;aACX;iBAAM;gBACL,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBACtB,OAAO,IAAI,CAAA;aACZ;SACF,EAAE,CAAC,CAAC,CAAA;QACL,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAA;QAClC,OAAO,YAAY,CAAC,SAAS,CAAC,UAAA,GAAG,IAAI,OAAA,MAAM,IAAI,GAAG,GAAA,CAAC,CAAA;IACrD,CAAC,CAAA;IAED;;;;;;IAMO,IAAM,SAAS,GAAG,UACvB,GAA6B,EAC7B,IAAY,EACZ,QAAqC,EACrC,SAA4B;QAA5B,0BAAA,EAAA,oBAA4B;;QAG5B,IAAI,SAAS,IAAI,CAAC;YAAE,SAAS,GAAG,QAAQ,CAAA;QACxC,IAAI,GAAG,GAAG,EAAE,CAAA;QACZ,IAAM,KAAK,GAAG,EAAE,CAAA;QAChB,IAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,KAAK,CAAA;QAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAA;YACd,IAAI,SAAS,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,CAAA;YAC1C,IAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA;;YAEhC,IAAI,SAAS,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC;gBAAE,SAAS,IAAI,QAAQ,CAAA;;YAEzD,IAAI,QAAQ,GAAG,CAAC;gBAAE,OAAO,KAAK,CAAA;;YAE9B,IAAI,SAAS,GAAG,QAAQ,EAAE;gBACxB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;gBAC5B,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;aACd;;YAED,IAAI,SAAS,KAAK,KAAK,CAAC,MAAM,EAAE;gBAC9B,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,CAAA;gBAChC,OAAO,KAAK,CAAA;aACb;SACF;QACD,IAAI,GAAG;YAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACxB,IAAI,CAAC,KAAK,CAAC,MAAM;YAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACnC,OAAO,KAAK,CAAA;IACd,CAAC,CAAA;IAED;IACO,IAAM,qBAAqB,GAAG,UAAI,GAAQ,EAAE,KAAe;QAChE,IAAM,GAAG,GAAyB,EAAE,EAAE,GAAG,GAAG,EAAE,CAAA;QAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;SAChB;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;YAC1B,IAAI,IAAI;gBAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,EAAC;SAC1B;QACD,OAAO,GAAG,CAAA;IACZ,CAAC;;;;;ICtMD;;;;QAOE;YACE,IAAI,CAAC,IAAI,GAAG,EAAE,CAAA;SACf;;;;;QAMM,oBAAM,GAAb,UAAe,GAAY;;YAEzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAC5B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;aACpB;SACF;;;;QAKM,oBAAM,GAAb;YACE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAA,GAAG;gBACnB,GAAG,CAAC,MAAM,EAAE,CAAA;aACb,CAAC,CAAA;SACH;QACH,UAAC;IAAD,CAAC;;IC7BM,IAAM,QAAQ,GAAG,WAAW,IAAI,EAAE,CAAA;aAEzB,GAAG,CAAE,GAAW,EAAE,GAAoB,EAAE,GAAQ,EAAE,UAAoB;QACpF,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE;YAC9B,KAAK,EAAE,GAAG;YACV,UAAU,EAAE,CAAC,CAAC,UAAU;YACxB,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI;SACnB,CAAC,CAAA;IACJ,CAAC;aAEe,SAAS,CAAE,IAAY;QACrC,IAAI,IAAI,GAAG,CAAA;QACX,IAAI,QAAQ,GAAa,EAAE,EAAE,OAAO,GAAG,EAAE,CAAA;QACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;YAClB,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACtB,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACtB,OAAO,GAAG,EAAE,CAAA;aACb;iBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC1B,SAAQ;aACT;iBAAM;gBACL,OAAO,IAAI,IAAI,CAAA;aAChB;SACF;QACD,OAAO,UAAU,IAAoB;YACnC,OAAO,QAAQ,CAAC,MAAM,CAAC,UAAC,IAAI,EAAE,GAAG;gBAC/B,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;aACjB,EAAE,IAAI,CAAC,CAAA;SACT,CAAA;IACH,CAAC;aAEe,QAAQ,CAAE,KAAU;;QAElC,IAAM,GAAG,GAAG,UAAC,IAAS;YACpB,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC;gBAAE,OAAM;YAClD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG;gBAC3B,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;gBACvB,GAAG,CAAC,KAAK,CAAC,CAAA;aACX,CAAC,CAAA;SACH,CAAA;QACD,GAAG,CAAC,KAAK,CAAC,CAAA;;IAEZ;;IC9CA;;;IAGA,IAAM,aAAa,GAAG,KAAK,CAAC,SAAS,CAAA;IACrC,IAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAA;IAClD,IAAM,OAAO,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAA;IAChF,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM;QACpB,aAAa,CAAC,MAAM,CAAC,GAAG;YAAU,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YAC9C,IAAM,GAAG,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;YACnD,IAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,CAAA;YACnC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACtE,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,CAAA;YACpB,OAAO,GAAG,CAAA;SACX,CAAA;IACH,CAAC,CAAC;;ICVF;;;;;QAQE,kBAAa,KAAU;;YAErB,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,CAAA;;YAEpB,GAAG,CAAC,KAAK,EAAE,aAAa,EAAE,IAAI,CAAC,CAAA;YAC/B,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACxB,IAAI,QAAQ,EAAE;oBACZ,KAAK,CAAC,WAAW,CAAC,GAAG,aAAa,CAAA;iBACnC;qBAAM;oBACL,MAAM,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG;wBACnD,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,aAAa,CAAC,GAAG,CAAC,CAAC,CAAA;qBACpC,CAAC,CAAA;iBACH;aACF;YACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;SACjB;QAED,uBAAI,GAAJ,UAAM,IAAoB;YACxB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG;gBAC3B,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;aACrC,CAAC,CAAA;SACH;QACH,eAAC;IAAD,CAAC,IAAA;IAED;;;;aAIgB,OAAO,CAAE,IAAS;QAChC,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ;YAAE,OAAM;QAC7C,IAAI,OAAwB,CAAA;QAC5B,IAAI,aAAa,IAAI,IAAI,EAAE;YACzB,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,CAAA;SAC9B;aAAM;YACL,OAAO,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAA;SAC7B;QACD,OAAO,OAAO,CAAA;IAChB,CAAC;IAED;;;;;;aAMgB,cAAc,CAAE,IAAS,EAAE,GAAoB,EAAE,GAAQ;QACvE,IAAM,GAAG,GAAG,IAAI,GAAG,EAAE,CAAA;QACrB,IAAM,QAAQ,GAAG,MAAM,CAAC,wBAAwB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;QAC3D,IAAI,QAAQ,IAAI,QAAQ,CAAC,YAAY,KAAK,KAAK,EAAE;YAC/C,OAAM;SACP;QACD,IAAM,MAAM,GAAG,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAA;QACvC,IAAM,MAAM,GAAG,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAA;QACvC,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,KAAK,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACjD,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;SAChB;QACD,IAAI,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,CAAA;QAC1B,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE;YAC/B,GAAG,EAAE;gBACH,IAAM,KAAK,GAAG,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAA;gBAC9C,IAAI,GAAG,CAAC,MAAM,EAAE;oBACd,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;oBACtB,IAAI,OAAO,EAAE;wBACX,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;qBAC/B;iBACF;gBACD,OAAO,KAAK,CAAA;aACb;YACD,GAAG,EAAE,UAAC,MAAM;gBACV,IAAI,MAAM,KAAK,GAAG;oBAAE,OAAM;gBAC1B,GAAG,GAAG,MAAM,CAAA;gBACZ,IAAI,MAAM,IAAI,CAAC,MAAM;oBAAE,OAAM;gBAC7B,IAAI,MAAM,EAAE;oBACV,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;iBAC1B;qBAAM;oBACL,GAAG,GAAG,MAAM,CAAA;iBACb;gBACD,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAA;gBACzB,GAAG,CAAC,MAAM,EAAE,CAAA;aACb;SACF,CAAC,CAAA;IACJ;;ICnFA,IAAI,GAAG,GAAG,CAAC,CAAA;IACX;;;;;;;QAeE,iBAAa,MAAa,EAAE,IAAuB,EAAE,EAAY,EAAE,OAA0B;YAA1B,wBAAA,EAAA,YAA0B;YAC3F,IAAI,CAAC,EAAE,GAAG,GAAG,EAAE,CAAA;YACf,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;YACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;YAChB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAA;YAC1B,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;gBAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;aACnB;iBAAM;gBACL,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,CAAA;aAC9B;YACD,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;YACZ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;SACxB;;;;QAKD,qBAAG,GAAH;YACE,GAAG,CAAC,MAAM,GAAG,IAAI,CAAA;YACjB,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;;YAExD,IAAI,IAAI,CAAC,IAAI,EAAE;gBACb,QAAQ,CAAC,KAAK,CAAC,CAAA;aAChB;YACD,GAAG,CAAC,MAAM,GAAG,IAAI,CAAA;YACjB,OAAO,KAAK,CAAA;SACb;;;;QAKD,wBAAM,GAAN;;YAEE,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;;YAEzB,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAA;YACzB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAA;;YAEnB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAA;SAC1C;QACH,cAAC;IAAD,CAAC;;IC3DD;;;;;QAkBE,eACE,MAAgD,EAChD,IAGC;YALH,iBA+CC;YA/DkB,YAAO,GAAW,OAAO,CAAA;YAGlC,iBAAY,GAAW,EAAE,CAAA;YACzB,QAAG,GAAa,eAAc,CAAA;YAC9B,aAAQ,GAAW,CAAC,CAAA;YACpB,cAAS,GAAW,CAAC,CAAA;;YAkB7B,IAAI,OAAO,MAAM,KAAK,QAAQ;gBAAE,MAAM,GAAG,EAAE,EAAE,EAAE,MAAM,EAAoB,CAAA;iBACpE,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC;gBAAE,MAAM,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,EAAoB,CAAA;;YAEzF,MAAM,GAAG,MAAwB,CAAA;YACjC,IAAI,CAAC,MAAM,GAAG,MAAoB,CAAA;YAClC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;;YAEhB,IAAI,CAAC,MAAM,CAAC,IAAI;gBAAE,MAAM,CAAC,IAAI,GAAG,KAAK,CAAA;YACrC,IAAI,MAAM,CAAC,EAAE;gBAAE,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAmB,CAAA;;YAEtF,IAAI,MAAM,CAAC,UAAU,EAAE;;gBAErB,MAAM,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;gBACvD,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,CAAA;aACpD;;YAED,IAAI,MAAM,CAAC,aAAa,EAAE;gBACxB,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAE,CAAA;;gBAEnD,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,SAAS,EAAK,IAAI,SAAI,OAAS,CAAC,CAAA;gBAClE,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAA,CAAC,CAAA;aACzE;YACD,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,GAA+B,CAAA;;YAEjD,IAAI,CAAC,kBAAkB,EAAE,CAAA;;YAEzB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;gBACpB,OAAO,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAA;aACvC;;YAED,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,gBAAgB,KAAK,UAAU,EAAE;gBAC3D,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,cAAM,OAAA,KAAI,CAAC,MAAM,EAAE,GAAA,EAAE,GAAG,CAAC,CAAC,CAAA;aACtE;;YAED,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,gBAAgB,KAAK,UAAU,EAAE;gBAC3D,IAAI,MAAM,CAAC,gBAAgB,CAAC;oBAC1B,KAAI,CAAC,MAAM,EAAE,CAAA;iBACd,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAA;aAC3D;SACF;;;;QAKS,sBAAM,GAAhB;;YACE,MAAA,MAAA,IAAI,CAAC,MAAM,EAAC,YAAY,kDAAI,CAAA;;YAE5B,IAAI,CAAC,eAAe,EAAE,CAAA;;YAEtB,IAAI,CAAC,MAAM,EAAE,CAAA;;YAEb,IAAI,CAAC,mBAAmB,EAAE,CAAA;;YAE1B,IAAI,CAAC,UAAU,EAAE,CAAA;SAClB;;;;QAKS,yBAAS,GAAnB;YACE,IAAI,CAAC,MAAM,EAAE,CAAA;YACb,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACrC,OAAO,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;aACnC;SACF;;;;;QAMS,2BAAW,GAArB,UAAuB,CAAa,KAAU;;;;QAKpC,+BAAe,GAAzB;YACE,IAAI,CAAC,MAAM;gBAAE,OAAM;YACnB,IAAI,CAAC,YAAY,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;SAC7F;;QAGM,2BAAW,GAAlB;YACQ,IAAA,KAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,EAAhD,KAAK,QAAA,EAAE,MAAM,QAAmC,CAAA;YACvD,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAA;SAC3D;;;;;QAMS,sBAAM,GAAhB;YACU,IAAA,MAAM,GAAK,IAAI,OAAT,CAAS;YACvB,IAAI,MAAM,CAAC,GAAG,EAAE,CAEf;iBAAM,IAAI,MAAM,EAAE;gBACjB,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,gBAAgB,IAAI,CAAC,CAAA;aAC1D;iBAAM,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;gBACtB,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAA;aAC3C;SACF;;;;QAKO,mCAAmB,GAA3B;YACQ,IAAA,KAAmB,IAAI,EAArB,MAAM,YAAA,EAAE,IAAI,UAAS,CAAA;;YAE7B,IAAI,QAAQ,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,CAAA;YAC/B,IAAI,MAAM,CAAC,UAAU,EAAE;gBACrB,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,WAAW,CAAA;gBACxC,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAA;aAC3C;;YAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,QAAQ,CAAA;YACzE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,SAAS,CAAA;;YAE7E,IAAI,MAAM,CAAC,UAAU,EAAE;gBACrB,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAA;gBAC3C,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;gBACpD,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;aACvD;SACF;;;;QAKS,0BAAU,GAApB;YACQ,IAAA,KAAkB,IAAI,EAApB,MAAM,YAAA,EAAE,GAAG,SAAS,CAAA;YACpB,IAAA,aAAa,GAAU,MAAM,cAAhB,EAAE,GAAG,GAAK,MAAM,IAAX,CAAW;YAC/B,IAAA,KAAkB,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAG,EAAE,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,EAA5D,KAAK,QAAA,EAAE,MAAM,QAA+C,CAAA;YACnE,IAAI,CAAC,aAAa;gBAAE,OAAM;YAC1B,aAAa,CAAC,KAAK,GAAG,KAAK,CAAA;YAC3B,aAAa,CAAC,MAAM,GAAG,MAAM,CAAA;YAC7B,aAAa,CAAC,KAAK,CAAC,KAAK,GAAM,KAAK,OAAI,CAAA;YACxC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAM,MAAM,OAAI,CAAA;YAC1C,aAAa,CAAC,KAAK,CAAC,kBAAkB,CAAC,GAAG,UAAU,CAAA;YACpD,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,WAAS,CAAC,GAAG,GAAG,MAAG,CAAA;YACnD,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;SACpB;;;;QAKO,kCAAkB,GAA1B;YACU,IAAA,MAAM,GAAK,IAAI,OAAT,CAAS;YACvB,IAAI,MAAM,EAAE;gBACV,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,qBAAqB;oBACrC,MAAM,CAAC,6BAA6B,CAAC;oBACrC,MAAM,CAAC,0BAA0B,CAAC;oBAClC,UAAU,QAAkB;wBAC1B,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,GAAG,EAAE,CAAC,CAAA;qBACvC,CAAA;gBACH,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAA;gBACrC,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAA;gBACvC,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAA;gBACzC,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,CAAA;gBAC3C,OAAM;aACP;YACD,IAAI,MAAM,CAAC,GAAG,EAAE;;gBAEd,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAA;aACtB;iBAAM,IAAI,MAAM,CAAC,UAAU,EAAE;;gBAE5B,IAAM,SAAO,GAAG,MAAM,CAAC,UAAU,CAAA;gBACjC,IAAI,CAAC,GAAG,GAAG,UAAC,QAAkB,IAAa,OAAA,SAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAA,CAAA;aACnE;iBAAM;;gBAEL,IAAI,CAAC,GAAG,GAAG,UAAC,QAAkB,IAAa,OAAA,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAA,CAAA;aACtE;SACF;QAEM,qBAAK,GAAZ;YACE,OAAO,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SAC/D;;;;;;QAOS,uBAAO,GAAjB,UACE,GAAW,EACX,IAAiB,EACjB,WAAwB;YAH1B,iBAoBC;YAjBC,4BAAA,EAAA,wBAAwB;YAExB,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;gBACjC,IAAI,CAAC,GAAG;oBAAE,MAAM,CAAC,SAAO,IAAI,CAAC,GAAG,uDAAY,CAAC,CAAA;gBAC7C,IAAI,KAAI,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE;oBAC9B,IAAI,QAAM,GAAG,IAAI,KAAK,EAAE,CAAA;oBACxB,QAAM,CAAC,aAAa,CAAC,GAAG,WAAW,CAAA;oBACnC,QAAM,CAAC,MAAM,GAAG,cAAM,OAAA,OAAO,CAAC,QAAM,CAAC,GAAA,CAAA;oBACrC,QAAM,CAAC,OAAO,GAAG,cAAM,OAAA,MAAM,CAAC,SAAO,IAAI,CAAC,GAAG,2CAAU,CAAC,GAAA,CAAA;oBACxD,QAAM,CAAC,GAAG,GAAG,GAAG,CAAA;iBACjB;qBAAM;;oBAEL,IAAI,CAAC,WAAW,CAAC,GAAG,OAAO,CAAA;oBAC3B,IAAI,CAAC,SAAS,CAAC,GAAG,MAAM,CAAA;oBACxB,OAAM;iBACP;aACF,CAAC,CAAA;SACH;;;;;;QAOS,yBAAS,GAAnB,UACE,GAA6B,EAC7B,MAAe;;YACf,kBAAgE;iBAAhE,UAAgE,EAAhE,qBAAgE,EAAhE,IAAgE;gBAAhE,iCAAgE;;YAEhE,IAAI,OAAO,CAAA;YACL,IAAA,KAAgB,IAAI,CAAC,MAAM,EAAzB,IAAI,UAAA,EAAE,GAAG,SAAgB,CAAA;YACjC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;;gBAEnC,OAAO,GAAG,MAAM,CAAA;aACjB;iBAAM,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBAGpE,OAAO,GAAI,MAAuB,CAAC,IAAI,CAAA;aACxC;iBAAM;;gBAEL,OAAO,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAA;aAC7C;YACD,IAAM,iBAAiB,GAAG,MAAA,OAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,EAAC,UAAU,mDAAG,IAAI,CAAC,CAAA;YAC3E,IAAI,iBAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE;gBACtC,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,GAAI,GAAG,GAAG,GAAA,CAAqB,CAAA;gBAC9D,IAAM,IAAI,GAAG,iBAAiB,CAAC,YAAY,OAA9B,iBAAiB,EAAiB,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;gBACpE,GAAG,CAAC,YAAY,OAAhB,GAAG,iBAAc,IAAI,GAAM,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAsB,UAAC;aACtE;iBAAM;gBACL,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;oBACzB,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAC,GAAG,EAAE,KAAK,IAAK,OAAA,KAAK,GAAG,CAAC,GAAG,GAAI,GAAG,GAAG,GAAG,GAAG,GAAA,CAAqB,CAAA;iBAC1F;;gBAED,IAAI;oBACF,GAAG,CAAC,SAAS,OAAb,GAAG,iBAAW,OAAO,GAAK,QAA4B,UAAC;iBACxD;gBAAC,OAAO,GAAG,EAAE;;;;;;;iBAOb;aACF;SACF;;;;;;;;;QAUS,sCAAsB,GAAhC,UACE,MAAe,EACf,OAAoB,EACpB,QAAgB,EAChB,SAAiB;;YAGjB,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;;gBAErC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAA;aACrC;iBAAM,IAAI,OAAO,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;;gBAE3C,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;;gBAEvD,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,IAAI,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;aAC/D;iBAAM,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE;;gBAE3C,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;;gBAE1D,OAAO,CAAC,MAAM,CAAC,KAAK,IAAI,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,UAAU,CAAC,CAAA;aACjE;;YAED,OAAO;gBACL,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC;gBACvC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC;aAC1C,CAAA;SACF;;;;;;;QAQS,2BAAW,GAArB,UAAuB,KAAa,EAAE,WAAe;YAArD,iBAcC;YAdqC,4BAAA,EAAA,eAAe;YAC3C,IAAA,MAAM,GAAK,IAAI,OAAT,CAAS;YACvB,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,0BAA0B,EAAE,UAAC,GAAG,EAAE,GAAG,EAAE,IAAI;gBACrE,IAAM,aAAa,GAAG;oBACpB,GAAG,EAAE,UAAC,CAAS,IAAK,OAAA,CAAC,IAAI,WAAW,GAAG,GAAG,CAAC,GAAA;oBAC3C,IAAI,EAAE,UAAC,CAAS,IAAK,OAAA,CAAC,GAAG,CAAC,GAAA;oBAC1B,KAAK,EAAE,UAAC,CAAS,IAAK,OAAA,CAAC,GAAG,KAAI,CAAC,YAAY,GAAA;oBAC3C,IAAI,EAAE,UAAC,CAAS,IAAK,OAAA,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,UAAU,GAAA;iBACjD,CAAC,IAAI,CAAC,CAAA;gBACP,IAAI,aAAa;oBAAE,OAAO,aAAa,CAAC,GAAG,CAAC,CAAA;;gBAE5C,IAAM,kBAAkB,GAAG,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,UAAU,CAAC,CAAA;gBACrE,OAAO,kBAAkB,GAAG,kBAAkB,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,CAAA;aAChE,CAAC,CAAC,CAAA;SACJ;;;;;;;QAQS,yBAAS,GAAnB,UAAqB,MAAmC,EAAE,SAAkB;YAC1E,IAAI,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;gBAAE,OAAO,MAAgB,CAAA;YAC3D,IAAI,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;gBAAE,OAAO,IAAI,CAAC,WAAW,CAAC,MAAgB,EAAE,SAAS,CAAC,CAAA;YACxF,OAAO,CAAC,CAAA;SACT;;;;;;QAOS,0BAAU,GAApB,UAAsB,KAAa,EAAE,QAAoB;YAApB,yBAAA,EAAA,YAAoB;YACvD,OAAO,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,CAAA;SAC9B;QAES,kCAAkB,GAA5B,UAA8B,KAAa,EAAE,MAAc;YAIzD,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,kBAAkB,CAAC,EAAE;gBAClC,IAAI,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE;oBAC3D,IAAI,CAAC,kBAAkB,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;iBAC5D;qBAAM;oBACL,IAAI,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAA;iBAC1D;gBACD,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC;oBAAE,OAAO,OAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;aACvE;YACD,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAA;YAC3B,IAAM,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAsB,CAAA;YACtE,gBAAgB,CAAC,KAAK,GAAG,CAAC,KAAK,IAAI,GAAG,IAAI,GAAG,CAAA;YAC7C,gBAAgB,CAAC,MAAM,GAAG,CAAC,MAAM,IAAI,GAAG,IAAI,GAAG,CAAA;YAC/C,IAAM,IAAI,GAAG,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAE,CAAA;YAC/C,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAA;YACnC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;YACpB,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAA;YACjB,OAAO,EAAE,gBAAgB,kBAAA,EAAE,IAAI,MAAA,EAAE,CAAA;SAClC;;;;;;;QAQM,oBAAI,GAAX,UAAa,IAAY,EAAE,GAAoB,EAAE,KAAU;YACzD,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ;gBAAE,OAAM;YAC7C,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAA;SACjC;;;;;;;QAQS,yBAAS,GAAnB,UAAqB,IAAY,EAAE,GAAW,EAAE,QAAkB;YAAlE,iBAMC;YALC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE;gBAC/B,GAAG,EAAE;oBACH,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAI,CAAC,CAAA;iBAC3B;aACF,CAAC,CAAA;SACH;;;;;;;;QASS,sBAAM,GAAhB,UACE,IAAuB,EACvB,OAAgC,EAChC,QAA2B;YAA3B,yBAAA,EAAA,aAA2B;YAE3B,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;gBAC/B,QAAQ,GAAG,OAAO,CAAA;gBAClB,OAAO,GAAG,QAAQ,CAAC,OAAQ,CAAA;aAC5B;;YAED,IAAM,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAA;;YAE1D,IAAI,QAAQ,CAAC,SAAS,EAAE;gBACtB,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;aAClC;;YAED,OAAO,SAAS,SAAS,MAAM,CAAA;SAChC;QAxaM,aAAO,GAAW,OAAO,CAAA;QAyalC,YAAC;KA1aD;;ICPA;;;;;IAKO,IAAM,QAAQ,GAAG,UAAC,GAAW;QAClC,OAAO,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG,CAAA;IAC5B,CAAC,CAAA;IAED;;;;;;IAMO,IAAM,kBAAkB,GAAG,UAAC,GAAW,EAAE,CAAS;QACvD,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;IAC3E,CAAC,CAAA;IAcD;IACO,IAAM,cAAc,GAAG,UAC5B,GAA6B,EAC7B,SAAiB,EACjB,SAAiB,EACjB,KAAa,EACb,GAAW,EACX,MAAc;QAEd,GAAG,CAAC,SAAS,EAAE,CAAA;QACf,IAAI,SAAS,GAAG,QAAQ,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,SAAS,GAAG,MAAM,CAAC,CAAA;QAE3D,IAAI,QAAQ,GAAG,KAAK,GAAG,SAAS,CAAA;QAChC,IAAI,MAAM,GAAG,GAAG,GAAG,SAAS,CAAA;QAG5B,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,CAAA;;;;;QAK/C,GAAG,CAAC,MAAM,OAAV,GAAG,EACE,kBAAkB,CACnB,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,EACjB,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CACnD,EACF;;QAEH,GAAG,CAAC,SAAS,EAAE,CAAA;IACjB,CAAC,CAAA;IAED;IACO,IAAM,cAAc,GAAG,UAC5B,GAA6B;QAC7B,YAA4B;aAA5B,UAA4B,EAA5B,qBAA4B,EAA5B,IAA4B;YAA5B,2BAA4B;;QAA5B,IAAI,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAA,CAAW;QAE5B,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE,CAAA;QACxC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;YAAE,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA;QAC5B,GAAG,CAAC,SAAS,EAAE,CAAA;QACf,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;QACpB,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;QACpB,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;QACxB,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;QACxC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;QAC5B,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAA;QAC3C,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAA;QACxB,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAA;QACxC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAA;QACpB,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;QACrC,GAAG,CAAC,SAAS,EAAE,CAAA;IACjB,CAAC,CAAA;IAED;;;IAGO,IAAM,iBAAiB,GAAG,UAC/B,GAA6B,EAC7B,CAAS,EACT,CAAS,EACT,CAAS,EACT,CAAS,EACT,UAAkB;QAElB,IAAM,OAAO,GAAI,yBAAyB,CAAC,IAAI,CAAC,UAAU,CAAgB,CAAC,CAAC,CAAC;aAC1E,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,UAAC,IAAY,IAAK,OAAA,IAAI,CAAC,IAAI,EAAE,GAAA,CAAC,CAAA;QACrC,IAAI,GAAG,GAAG,OAAO,CAAC,KAAK,EAAE,EAAE,SAAS,GAAqC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;;QAErF,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YACvB,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAA;;YAE5B,IAAM,cAAc,GAAG,UAAC,GAAW,IAAK,OAAA,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,GAAA,CAAA;YACrE,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,EAAE;gBAAE,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;iBACvF,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,GAAG,EAAE;gBAAE,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;iBAC5F,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,GAAG,GAAG;gBAAE,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;iBACjG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG;gBAAE,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAA;iBAC7F,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG;gBAAE,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAA;iBACzF,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG;gBAAE,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAA;iBAC7F,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG;gBAAE,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAA;iBACzF,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG;gBAAE,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAA;SACnG;;aAEI,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;aACrD,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAA;aACxD,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;aACtD,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;;QAE5D,IAAM,QAAQ,GAAG,GAAG,CAAC,oBAAoB,OAAxB,GAAG,EAA0B,SAAS,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,IAAI,CAAC,GAAA,CAAsB,CAAC,CAAA;;QAE9F,OAAO,OAAO,CAAC,MAAM,CAAC,UAAC,QAAa,EAAE,IAAS,EAAE,KAAU;YACzD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAC5B,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;gBAAE,QAAQ,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;iBACvD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;gBAAE,QAAQ,CAAC,YAAY,OAArB,QAAQ,EAAiB,IAAI,EAAC;YAC1D,OAAO,QAAQ,CAAA;SAChB,EAAE,QAAQ,CAAC,CAAA;IACd,CAAC,CAAA;IAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;ICjOA;;;;;;;;;IAeA;IACO,IAAM,IAAI,GAAc;QAC7B,MAAM,EAAE,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,IAAI,CAAC;gBAAE,CAAC,GAAG,CAAC,CAAA;YACjB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;SAC5B;QACD,OAAO,EAAE,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC;gBAAE,CAAC,GAAG,CAAC,CAAA;YACjB,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;SACnC;KACF;;;QCJuC,8BAAK;;;;;;QA2C3C,oBAAa,MAAsB,EAAE,IAAsB;;YAA3D,YACE,kBAAM,MAAM,EAAE;gBACZ,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,MAAM,EAAE,IAAI,CAAC,MAAM;aACpB,CAAC,SAQH;YAtDO,YAAM,GAAqB,EAAE,CAAA;YAC7B,YAAM,GAAqB,EAAE,CAAA;YAC7B,aAAO,GAAsB,EAAE,CAAA;YAC/B,mBAAa,GAAsB,EAAE,CAAA;YACrC,kBAAY,GAAqB,EAAE,CAAA;YACnC,oBAAc,GAAgC,EAAiC,CAAA;YAC/E,mBAAa,GAA+B,EAAgC,CAAA;YAG5E,YAAM,GAAG,CAAC,CAAA;YACV,iBAAW,GAAG,CAAC,CAAA;YACf,cAAQ,GAAG,CAAC,CAAA;YACZ,cAAQ,GAAG,CAAC,CAAA;YACZ,eAAS,GAAG,CAAC,CAAA;YACb,kBAAY,GAAG,CAAC,CAAA;YAChB,eAAS,GAAG,CAAC,CAAA;YACb,aAAO,GAAG,CAAC,CAAA;YACX,aAAO,GAAG,CAAC,CAAA;YACX,YAAM,GAAG,CAAC,CAAA;YACV,SAAG,GAAG,IAAI,CAAA;;;;;;;;YAQV,UAAI,GAAkB,CAAC,CAAA;YAQvB,gBAAU,GAAG,IAAI,GAAG,EAAE,CAAA;YAY5B,KAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;YACnB,KAAI,CAAC,SAAS,EAAE,CAAA;YAChB,KAAI,CAAC,YAAY,EAAE,CAAA;;YAEnB,MAAA,MAAM,CAAC,YAAY,0CAAE,IAAI,CAAC,KAAI,CAAC,CAAA;;YAE/B,KAAI,CAAC,IAAI,EAAE,CAAA;;SACZ;QAES,2BAAM,GAAhB;;YACE,iBAAM,MAAM,WAAE,CAAA;YACd,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;YACzD,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;YAC5C,IAAI,CAAC,IAAI,EAAE,CAAA;YACX,MAAA,MAAA,IAAI,CAAC,MAAM,EAAC,WAAW,kDAAI,CAAA;SAC5B;QAES,8BAAS,GAAnB;YACE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;YACf,IAAI,CAAC,WAAW,GAAG,CAAC,CAAA;YACpB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAA;YACjB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAA;YACjB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA;YAClB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAA;YACrB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA;YAClB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAA;YAChB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAA;YAChB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;YACf,IAAI,CAAC,GAAG,GAAG,IAAI,CAAA;YACf,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAA;YACnB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;YACb,iBAAM,SAAS,WAAE,CAAA;SAClB;;;;;QAMO,6BAAQ,GAAhB,UAAkB,IAAsB;YACtC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;YACpC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;YACtC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAA;YAC5C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAA;YAC5C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC,CAAA;YAC9C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC,aAAa,IAAI,EAAE,CAAC,CAAA;YAC1D,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,cAAc,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC,CAAA;YACxD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;YAC5C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;SACzC;;;;QAKO,iCAAY,GAApB;YAAA,iBA6BC;;YA3BC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,gBAAgB,EAAE;gBACrC,IAAM,MAAM,cACV,MAAM,EAAE,KAAK,EACb,YAAY,EAAE,CAAC,EACf,KAAK,EAAE,EAAE,EACT,aAAa,EAAE,MAAM,EACrB,gBAAgB,EAAE,IAAI,EACtB,gBAAgB,EAAE,IAAI,EACtB,SAAS,EAAE,CAAC,IACT,KAAI,CAAC,aAAa,CACtB,CAAA;gBACD,OAAO,MAAM,CAAA;aACd,CAAC,CAAA;;YAEF,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,eAAe,EAAE;gBACpC,IAAM,KAAK,cACT,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE,YAAY,EACvB,UAAU,EAAE,KAAK,EACjB,UAAU,EAAE,eAAe,EAC3B,QAAQ,EAAE,IAAI,EACd,WAAW,EAAE,KAAK,IACf,KAAI,CAAC,YAAY,CACrB,CAAA;gBACD,OAAO,KAAK,CAAA;aACb,CAAC,CAAA;SACH;;;;QAKO,8BAAS,GAAjB;YAAA,iBA2BC;;YAzBC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,UAAC,MAAuB;gBAC3C,KAAI,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAA;gBACxB,KAAI,CAAC,MAAM,EAAE,CAAA;aACd,CAAC,CAAA;;YAEF,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAC,MAAuB;gBAC5C,KAAI,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;gBACzB,KAAI,CAAC,MAAM,EAAE,CAAA;aACd,CAAC,CAAA;;YAEF,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAC,OAAyB;gBAC9C,KAAI,CAAC,cAAc,EAAE,CAAA;aACtB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;;YAElB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAC,OAAyB;gBAC9C,KAAI,CAAC,cAAc,EAAE,CAAA;aACtB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;;YAElB,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,UAAC,OAA0B;gBAChD,KAAI,CAAC,cAAc,EAAE,CAAA;aACtB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;YAClB,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,EAAE,GAAA,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;YAC/D,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,EAAE,GAAA,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;YAC9D,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,EAAE,GAAA,CAAC,CAAA;YAC/C,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,EAAE,GAAA,CAAC,CAAA;SAC9C;;;;QAKY,yBAAI,GAAjB;;;;;;;4BACE,IAAI,CAAC,SAAS,EAAE,CAAA;4BACR,MAAM,GAAK,IAAI,OAAT,CAAS;;4BAEvB,MAAA,MAAM,CAAC,UAAU,0CAAE,IAAI,CAAC,IAAI,CAAC,CAAA;4BAC7B,IAAI,CAAC,IAAI,EAAE,CAAA;4BACX,IAAI,CAAC,IAAI,EAAE,CAAA;;4BAEX,qBAAM,IAAI,CAAC,cAAc,EAAE;;8BAAA;;;4BAA3B,SAA2B,CAAA;;4BAE3B,MAAA,MAAM,CAAC,SAAS,0CAAE,IAAI,CAAC,IAAI,CAAC,CAAA;;;;;SAC7B;QAEO,mCAAc,GAAtB;YAAA,iBAsBC;YArBC,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO;gBACzB,IAAM,cAAc,GAAG;oBACrB,MAAM,EAAE,KAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,IAAI,GAAA,CAAC;oBAC5C,MAAM,EAAE,KAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,IAAI,GAAA,CAAC;oBAC5C,OAAO,EAAE,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,IAAI,GAAA,CAAC;iBAC3C,CACA;gBAAkC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAE,CAAC,OAAO,CAAC,UAAA,OAAO;oBAC7E,IAAM,UAAU,GAAG,cAAc,CAAC,OAAO,CAAC,CAAA;;oBAE1C,IAAM,UAAU,GAAoB,EAAE,CAAA;oBACtC,UAAU,IAAI,UAAU,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,SAAS;wBAC/C,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,QAAQ;4BACrC,UAAU,CAAC,IAAI,CAAC,KAAI,CAAC,eAAe,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAA;yBACpE,CAAC,CAAA;qBACH,CAAC,CAAA;oBACF,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;wBAC3B,KAAI,CAAC,IAAI,EAAE,CAAA;wBACX,OAAO,EAAE,CAAA;qBACV,CAAC,CAAA;iBACH,CAAC,CAAA;aACH,CAAC,CAAA;SACH;;;;;QAMS,gCAAW,GAArB,UAAuB,CAAa;;YAC1B,IAAA,GAAG,GAAK,IAAI,IAAT,CAAS;YACpB,GAAG,CAAC,SAAS,EAAE,CAAA;YACf,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAA;YACvD,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC;gBAAE,OAAM;YACpD,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC;gBAAE,OAAM;YAC3B,MAAA,IAAI,CAAC,aAAa,+CAAlB,IAAI,EAAiB,CAAC,CAAC,CAAA;SACxB;;;;;;;;QASa,oCAAe,GAA7B,UACE,QAAyC,EACzC,SAAiB,EACjB,QAAgB;;;;oBAEhB,sBAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;;4BAEjC,IAAM,IAAI,GAAuC,KAAI,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAA;4BAC1E,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;gCAAE,OAAM;4BAC/B,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;4BACnC,IAAI,CAAC,OAAO;gCAAE,OAAM;;4BAEpB,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,UAAM,OAAO;;;;kDAC/C,OAAO,OAAO,CAAC,SAAS,KAAK,UAAU,CAAA,EAAvC,wBAAuC;4CAC/B,qBAAM,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,EAAA;;4CAAtE,OAAO,GAAG,SAA4D,CAAA;;;4CAExE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAA;4CAC5C,OAAO,EAAE,CAAA;;;;iCACV,CAAC,CAAC,KAAK,CAAC,UAAA,GAAG;gCACV,OAAO,CAAC,KAAK,CAAI,QAAQ,SAAI,SAAS,eAAU,QAAQ,UAAK,GAAK,CAAC,CAAA;gCACnE,MAAM,EAAE,CAAA;6BACT,CAAC,CAAA;yBACH,CAAC,EAAA;;;SACH;QAEO,8BAAS,GAAjB,UAAmB,MAAc,EAAE,KAAgB,EAAE,UAAkB;YAAvE,iBAmBC;YAlBS,IAAA,GAAG,GAAK,IAAI,IAAT,CAAS;YACpB,IAAI,aAAa,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;gBACnC,GAAG,CAAC,SAAS,EAAE,CAAA;gBACf,GAAG,CAAC,SAAS,GAAG,KAAK,CAAC,UAAW,CAAA;gBACjC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAA;gBAC5C,GAAG,CAAC,IAAI,EAAE,CAAA;aACX;YACD,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,QAAQ;gBACjD,IAAM,QAAQ,GAAG,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;gBACjD,IAAI,CAAC,QAAQ;oBAAE,OAAM;;gBAEf,IAAA,KAA0B,KAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,EAA/F,SAAS,QAAA,EAAE,UAAU,QAA0E,CAAA;gBAChG,IAAA,KAAiB,CAAC,KAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,EAAzI,KAAK,QAAA,EAAE,KAAK,QAA6H,CAAA;gBAChJ,GAAG,CAAC,IAAI,EAAE,CAAA;gBACV,OAAO,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAI,CAAC,SAAS,CAAC,CAAC,CAAA;gBACtD,KAAI,CAAC,SAAS,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,CAAA;gBAClE,GAAG,CAAC,OAAO,EAAE,CAAA;aACd,CAAC,CAAA;SACH;;;;QAKS,yBAAI,GAAd;YAAA,iBAiJC;;YAhJO,IAAA,KAAiD,IAAI,EAAnD,MAAM,YAAA,EAAE,GAAG,SAAA,EAAE,cAAc,oBAAA,EAAE,aAAa,mBAAS,CAAA;;YAE3D,MAAA,MAAM,CAAC,UAAU,0CAAE,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;;YAElC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;;YAE3E,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAC,MAAM,EAAE,KAAK,EAAE,UAAU;gBAC9D,KAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;gBACzC,OAAO,MAAM,GAAG,KAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;aAC7E,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;;YAEf,IAAI,CAAC,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA;YACxC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACvC,IAAM,SAAS,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;;YAEpE,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,cAAc,CAAC,YAAY,CAAC,CAAA;;YAE3F,IAAM,QAAQ,GAAG,UAAC,IAAkB,EAAE,IAAY;gBAChD,OAAO,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;aAC3F,CAAA;;YAED,IAAM,QAAQ,GAAG,UAAC,IAAkB,EAAE,MAAc,EAAE,SAAiB;;gBAErE,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,aAAa,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,CAAA;gBACzG,OAAO,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,IAAI,KAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;aACvF,CAAA;YACD,GAAG,CAAC,IAAI,EAAE,CAAA;;YAEV,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,UAAU;;gBAEpC,IAAI,aAAa,GAAG,KAAK,GAAG,UAAU,GAAG,KAAI,CAAC,QAAQ,CAAA;;gBAEtD,IAAI,WAAW,GAAG,KAAI,CAAC,WAAW,GAAG,KAAI,CAAC,YAAY,CAAA;;gBAEtD,IAAM,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,aAAa,CAAC,UAAU,CAAA;gBAC/D,IAAI,aAAa,CAAC,UAAU,CAAC,EAAE;oBAC7B,GAAG,CAAC,SAAS,GAAG,UAAU,CAAA;oBAC1B,cAAc,CACZ,GAAG,EAAE,KAAI,CAAC,YAAY,EAAE,KAAI,CAAC,WAAW,EACxC,aAAa,GAAG,KAAI,CAAC,QAAQ,GAAG,CAAC,EACjC,aAAa,GAAG,KAAI,CAAC,QAAQ,GAAG,CAAC,EACjC,KAAI,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CACtC,CAAA;oBACD,GAAG,CAAC,IAAI,EAAE,CAAA;iBACX;;gBAED,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,KAAI,CAAC,WAAW,CAAA;gBAClD,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,KAAI,CAAC,WAAW,CAAA;gBAClD,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;gBACnB,GAAG,CAAC,MAAM,CAAC,aAAa,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAA;;gBAExC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,QAAQ;oBACjD,IAAM,QAAQ,GAAG,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;oBACjD,IAAI,CAAC,QAAQ;wBAAE,OAAM;oBACf,IAAA,KAA0B,KAAI,CAAC,sBAAsB,CACzD,QAAQ,EACR,OAAO,EACP,KAAI,CAAC,QAAQ,GAAG,KAAI,CAAC,WAAW,EAChC,WAAW,CACZ,EALM,SAAS,QAAA,EAAE,UAAU,QAK3B,CAAA;oBACK,IAAA,KAAiB;wBACrB,KAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC;wBACpE,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,WAAW,CAAC;qBACzC,EAHM,KAAK,QAAA,EAAE,KAAK,QAGlB,CAAA;oBACD,KAAI,CAAC,SAAS,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,CAAA;iBACnE,CAAC,CAAA;;gBAEF,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBACrC,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS,CAAA;oBAC3D,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,aAAa,CAAC,UAAU,CAAA;oBAC9D,IAAM,QAAQ,GAAG,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAA;oBACxE,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS,CAAA;oBAC3D,IAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAA;oBAC/E,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,aAAa,CAAC,WAAW,CAAA;oBACjE,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS,CAAA;oBAC3D,GAAG,CAAC,SAAS,GAAG,SAAS,CAAA;oBACzB,GAAG,CAAC,IAAI,GAAM,UAAU,UAAI,QAAQ,IAAI,CAAC,YAAM,SAAW,CAAA;oBAC1D,IAAI,KAAK,GAAG,EAAE,EAAE,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;oBACxC,IAAI,QAAQ,EAAE;wBACZ,KAAK,GAAG,SAAS,CAAC,GAAG,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE,UAAC,KAAK;;4BAE9C,IAAM,YAAY,GAAG,KAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,IAAI,EAAE,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;;4BAEjF,IAAM,SAAS,GAAG,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,KAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAA;;4BAE5D,IAAI,QAAQ,GAAG,SAAS,GAAG,CAAC,GAAG,KAAI,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;4BACpE,OAAO,KAAI,CAAC,SAAS,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAA;yBAC7C,EAAE,SAAS,CAAC,CAAA;qBACd;yBAAM;wBACL,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;qBACzB;oBACD,KAAK,CAAC,MAAM,CAAC,UAAA,IAAI,IAAI,OAAA,CAAC,CAAC,IAAI,GAAA,CAAC,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,SAAS;wBACnD,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,IAAI,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC,CAAA;qBACjF,CAAC,CAAA;iBACH,CAAC,CAAA;;gBAEF,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,aAAa,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAA;gBACxD,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;aACtB,CAAC,CAAA;YACF,GAAG,CAAC,OAAO,EAAE,CAAA;;YAEb,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAC,GAAG,EAAE,QAAQ;gBACjC,IAAI,MAAM,GAAG,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,KAAI,CAAC,WAAW,CAAC,CAAA;;gBAEzD,KAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,KAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAA;gBACvD,IAAI,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;oBACjC,GAAG,CAAC,SAAS,EAAE,CAAA;oBACf,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,UAAoB,CAAA;oBACxC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAA;oBAC5C,GAAG,CAAC,IAAI,EAAE,CAAA;iBACX;;gBAED,IAAI,GAAG,CAAC,OAAO,IAAI,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;oBAChD,GAAG,CAAC,SAAS,EAAE,CAAA;oBACf,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,UAAoB,CAAA;oBACxC,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;oBACtB,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;oBACrB,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;oBAC1B,GAAG,CAAC,SAAS,EAAE,CAAA;oBACf,GAAG,CAAC,IAAI,EAAE,CAAA;iBACX;;gBAED,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,QAAQ;oBAC7C,IAAM,MAAM,GAAG,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;oBAC/C,IAAI,CAAC,MAAM;wBAAE,OAAM;oBACb,IAAA,KAA0B,KAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,EAA7F,SAAS,QAAA,EAAE,UAAU,QAAwE,CAAA;oBAC9F,IAAA,KAAiB,CAAC,KAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,EAAxH,KAAK,QAAA,EAAE,KAAK,QAA4G,CAAA;oBAC/H,KAAI,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,CAAA;iBACjE,CAAC,CAAA;;gBAEF,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBACjC,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS,CAAA;oBACzD,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,aAAa,CAAC,UAAU,CAAA;oBAC5D,IAAI,QAAQ,GAAG,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAA;oBACtE,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS,CAAA;oBACzD,GAAG,CAAC,SAAS,GAAG,SAAS,CAAA;oBACzB,GAAG,CAAC,IAAI,GAAM,UAAU,UAAI,QAAQ,IAAI,CAAC,YAAM,SAAW,CAAA;oBAC1D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,SAAS;wBACpD,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC,CAAA;qBAC5E,CAAC,CAAA;iBACH,CAAC,CAAA;aACH,CAAC,CAAA;;YAEF,MAAA,MAAM,CAAC,SAAS,0CAAE,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;SAClC;;;;QAKO,gDAA2B,GAAnC;YACQ,IAAA,KAAqD,IAAI,EAAvD,cAAc,oBAAA,EAAE,SAAS,eAAA,EAAE,QAAQ,cAAA,EAAE,SAAS,eAAS,CAAA;YAC/D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YACzB,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,SAAS,CAAA;YACxC,IAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAA;YAClC,IAAM,SAAS,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,GAAG,QAAQ,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;YACtG,IAAI,CAAC,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,CAAA;YACrC,OAAO,EAAE,CAAC,EAAE;gBACV,IAAM,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,SAAU,GAAG,QAAQ,GAAG,SAAS,GAAG,cAAc,CAAC,YAAY,GAAG,SAAS,GAAG,QAAQ,GAAG,CAAC,CAAA;gBACnH,IAAI,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,cAAc,CAAC,gBAAgB,CAAC,GAAG,OAAO,CAAA;gBAClG,IAAI,SAAS,GAAG,KAAK,EAAE;oBACrB,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,GAAG,SAAS,GAAG,SAAS,GAAG,KAAK,IAAI,MAAM,GAAG,OAAO,CAAA;oBACxE,MAAK;iBACN;gBACD,OAAO,GAAG,MAAM,CAAA;gBAChB,SAAS,GAAG,SAAS,CAAA;aACtB;SACF;;;;QAKM,yBAAI,GAAX;;YACE,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC;gBAAE,OAAM;;YAE3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;;YAE3B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAA;;YAEvB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;;YAEb,MAAA,MAAA,IAAI,CAAC,MAAM,EAAC,UAAU,kDAAI,CAAA;;YAE1B,IAAI,CAAC,GAAG,EAAE,CAAA;SACX;;;;;QAMM,yBAAI,GAAX,UAAa,KAAc;YACzB,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC;gBAAE,OAAM;;YAE9C,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK,CAAC,EAAE;gBACzB,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,KAAK,GAAA,CAAC,CAAA;gBACpD,KAAK,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAA;aAC/B;;YAED,IAAI,KAAK,GAAG,CAAC,EAAE;gBACb,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;gBACb,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAA;aACpB;iBAAM;gBACL,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;gBACb,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA;aAC5C;SACF;;;;;QAMO,wBAAG,GAAX,UAAa,GAAe;;YAAf,oBAAA,EAAA,OAAe;YACpB,IAAA,KAA2C,IAAI,EAA7C,GAAG,SAAA,EAAE,IAAI,UAAA,EAAE,SAAS,eAAA,EAAE,cAAc,oBAAS,CAAA;YAC7C,IAAA,gBAAgB,GAA8B,cAAc,iBAA5C,EAAE,gBAAgB,GAAY,cAAc,iBAA1B,EAAE,KAAK,GAAK,cAAc,MAAnB,CAAmB;;YAEpE,IAAI,IAAI,KAAK,CAAC,EAAE;gBACd,MAAA,IAAI,CAAC,WAAW,+CAAhB,IAAI,EAAe,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAC,KAAK,EAAE,KAAK,IAAK,OAAA,KAAK,KAAK,SAAS,GAAA,CAAC,IAAI,EAAE,CAAC,CAAA;gBACjF,OAAM;aACP;;YAED,IAAI,SAAS,KAAK,CAAC,CAAC;gBAAE,OAAM;;YAE5B,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;gBAAE,IAAI,CAAC,2BAA2B,EAAE,CAAA;;YAElE,IAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAA;YACjD,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,CAAA;YAC7C,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;;YAE9B,IAAI,IAAI,KAAK,CAAC,IAAI,aAAa,GAAG,gBAAgB,EAAE;;gBAElD,IAAI,CAAC,GAAG,GAAG,aAAa,GAAG,GAAG,CAAA;gBAC9B,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAA;;gBAExE,IAAI,SAAS,KAAK,KAAK,EAAE;oBACvB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;iBACd;gBACD,SAAS,GAAG,SAAS,GAAG,SAAS,GAAG,GAAG,CAAA;aACxC;iBAAM,IAAI,IAAI,KAAK,CAAC,EAAE;;gBAErB,SAAS,GAAG,SAAS,GAAG,KAAK,GAAG,GAAG,CAAA;;gBAEnC,IAAI,SAAS,KAAK,KAAK,CAAC,IAAI,SAAS,IAAI,CAAC,EAAE;oBAC1C,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;;oBAEb,IAAI,CAAC,OAAO,GAAG,CAAC,CAAA;oBAChB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;iBAChB;aACF;iBAAM,IAAI,IAAI,KAAK,CAAC,EAAE;;gBAErB,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAA;gBAClF,IAAI,WAAW,IAAI,gBAAgB,EAAE;oBACnC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;iBACd;aACF;iBAAM;;gBAEL,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;aACd;YACD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;YAC1B,IAAI,CAAC,IAAI,EAAE,CAAA;YACX,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;SAClC;;;;;;QAOS,mCAAc,GAAxB,UAA0B,CAAS,EAAE,CAAS;YACpC,IAAA,MAAM,GAAK,IAAI,OAAT,CAAS;YACvB,OAAO,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAA;SACpE;QACH,iBAAC;IAAD,CAhiBA,CAAwC,KAAK;;;QCQN,6BAAK;;;;;;QAoD1C,mBAAa,MAAsB,EAAE,IAAqB;;YAA1D,YACE,kBAAM,MAAM,EAAE;gBACZ,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,MAAM,EAAE,IAAI,CAAC,MAAM;aACpB,CAAC,SAQH;YA/DO,UAAI,GAAa,CAAC,CAAA;YAClB,UAAI,GAAa,CAAC,CAAA;YAClB,YAAM,GAAqB,EAAE,CAAA;YAC7B,YAAM,GAAqB,EAAE,CAAA;YAC7B,aAAO,GAAsB,EAAE,CAAA;YAE/B,mBAAa,GAAsB,EAAE,CAAA;YACrC,kBAAY,GAAqB,EAAE,CAAA;YACnC,iBAAW,GAAoB,EAAE,CAAA;YACjC,oBAAc,GAAgC,EAAiC,CAAA;YAC/E,mBAAa,GAA+B,EAAgC,CAAA;YAC5E,kBAAY,GAA8B,EAA+B,CAAA;YAGzE,eAAS,GAAG,CAAC,CAAA;YACb,gBAAU,GAAG,CAAC,CAAA;YACd,eAAS,GAAG,CAAC,CAAA;YACb,aAAO,GAAG,CAAC,CAAA;YACX,eAAS,GAAG,CAAC,CAAA;YACb,eAAS,GAAG,CAAC,CAAA;YACb,cAAQ,GAAG,CAAC,CAAA;YACZ,UAAI,GAAG,KAAK,CAAA;YACZ,WAAK,GAAG,CAAC,CAAA;YACT,SAAG,GAAG,IAAI,CAAA;;;;;;;;YAQV,UAAI,GAAkB,CAAC,CAAA;;;;;;;YAOvB,eAAS,GAAuB,CAAC,CAAC,CAAA;;YAElC,WAAK,GAA0C,EAAE,CAAA;;YAIjD,gBAAU,GAAG,IAAI,GAAG,EAAE,CAAA;YAY5B,KAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;YACnB,KAAI,CAAC,SAAS,EAAE,CAAA;YAChB,KAAI,CAAC,YAAY,EAAE,CAAA;;YAEnB,MAAA,MAAM,CAAC,YAAY,0CAAE,IAAI,CAAC,KAAI,CAAC,CAAA;;YAE/B,KAAI,CAAC,IAAI,EAAE,CAAA;;SACZ;QAES,0BAAM,GAAhB;;YACE,iBAAM,MAAM,WAAE,CAAA;YACd,IAAI,CAAC,IAAI,EAAE,CAAA;YACX,MAAA,MAAA,IAAI,CAAC,MAAM,EAAC,WAAW,kDAAI,CAAA;SAC5B;QAES,6BAAS,GAAnB;YACE,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA;YAClB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAA;YACnB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA;YAClB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAA;YAChB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA;YAClB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA;YAClB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAA;YACjB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAA;YACjB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;YACd,IAAI,CAAC,GAAG,GAAG,IAAI,CAAA;YACf,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAA;YACnB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;YACb,iBAAM,SAAS,WAAE,CAAA;SAClB;;;;;QAMO,4BAAQ,GAAhB,UAAkB,IAAqB;YACrC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;YACpC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;YACtC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;YAC/C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;YAC/C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAA;YAC5C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAA;YAC5C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC,CAAA;;YAE9C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;YACtC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC,aAAa,IAAI,EAAE,CAAC,CAAA;YAC1D,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,cAAc,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC,CAAA;YACxD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC,CAAA;YACtD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;YAC5C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;SACzC;;;;QAKO,gCAAY,GAApB;YAAA,iBAqCC;;YAnCC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,gBAAgB,EAAE;gBACrC,IAAM,MAAM,cACV,MAAM,EAAE,CAAC,EACT,KAAK,EAAE,EAAE,EACT,gBAAgB,EAAE,IAAI,EACtB,gBAAgB,EAAE,IAAI,IACnB,KAAI,CAAC,aAAa,CACtB,CAAA;gBACD,MAAM,CAAC,MAAM,GAAG,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;gBAC7C,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,EAAE,CAAA;gBAChC,OAAO,MAAM,CAAA;aACd,CAAC,CAAA;;YAEF,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,eAAe,EAAE;gBACpC,kBACE,YAAY,EAAE,EAAE,EAChB,SAAS,EAAE,MAAM,EACjB,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,YAAY,EACvB,UAAU,EAAE,KAAK,EACjB,UAAU,EAAE,eAAe,EAC3B,MAAM,EAAE,EAAE,EACV,QAAQ,EAAE,IAAI,EACd,WAAW,EAAE,KAAK,IACf,KAAI,CAAC,YAAY,EACrB;aACF,CAAC,CAAA;;YAEF,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,cAAc,EAAE;gBACnC,kBACE,UAAU,EAAE,SAAS,EACrB,MAAM,EAAE,EAAE,IACP,KAAI,CAAC,WAAW,EACpB;aACF,CAAC,CAAA;SACH;;;;QAKO,6BAAS,GAAjB;YAAA,iBA8BC;;YA5BC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,UAAC,MAAuB;gBAC3C,KAAI,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAA;gBACxB,KAAI,CAAC,MAAM,EAAE,CAAA;aACd,CAAC,CAAA;;YAEF,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAC,MAAuB;gBAC5C,KAAI,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;gBACzB,KAAI,CAAC,MAAM,EAAE,CAAA;aACd,CAAC,CAAA;;YAEF,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAC,OAAyB;gBAC9C,KAAI,CAAC,cAAc,EAAE,CAAA;aACtB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;;YAElB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAC,OAAyB;gBAC9C,KAAI,CAAC,cAAc,EAAE,CAAA;aACtB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;;YAElB,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,UAAC,OAA0B;gBAChD,KAAI,CAAC,cAAc,EAAE,CAAA;aACtB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;YAClB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,EAAE,GAAA,CAAC,CAAA;YACtC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,EAAE,GAAA,CAAC,CAAA;YACtC,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,EAAE,GAAA,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;YAC/D,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,EAAE,GAAA,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;YAC9D,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,EAAE,GAAA,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;YAC7D,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,EAAE,GAAA,CAAC,CAAA;YAC/C,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,EAAE,GAAA,CAAC,CAAA;SAC9C;;;;QAKY,wBAAI,GAAjB;;;;;;;4BACE,IAAI,CAAC,SAAS,EAAE,CAAA;4BACR,MAAM,GAAK,IAAI,OAAT,CAAS;;4BAEvB,MAAA,MAAM,CAAC,UAAU,0CAAE,IAAI,CAAC,IAAI,CAAC,CAAA;;4BAE7B,IAAI,CAAC,IAAI,EAAE,CAAA;;4BAEX,qBAAM,IAAI,CAAC,cAAc,EAAE;;8BAAA;;;4BAA3B,SAA2B,CAAA;;4BAE3B,MAAA,MAAM,CAAC,SAAS,0CAAE,IAAI,CAAC,IAAI,CAAC,CAAA;;;;;SAC7B;QAEO,kCAAc,GAAtB;YAAA,iBAwBC;YAvBC,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO;gBACzB,IAAM,OAAO,GAAG,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,IAAI,GAAA,CAAC,CAAA;gBACjD,IAAI,KAAI,CAAC,MAAM;oBAAE,OAAO,CAAC,IAAI,CAAC,KAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;gBAC/C,IAAM,cAAc,GAAG;oBACrB,MAAM,EAAE,KAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,IAAI,GAAA,CAAC;oBAC5C,MAAM,EAAE,KAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,IAAI,GAAA,CAAC;oBAC5C,OAAO,EAAE,OAAO;iBACjB,CACA;gBAAkC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAE,CAAC,OAAO,CAAC,UAAA,OAAO;oBAC7E,IAAM,UAAU,GAAG,cAAc,CAAC,OAAO,CAAC,CAAA;;oBAE1C,IAAM,UAAU,GAAoB,EAAE,CAAA;oBACtC,UAAU,IAAI,UAAU,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,SAAS;wBAC/C,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,QAAQ;4BACrC,UAAU,CAAC,IAAI,CAAC,KAAI,CAAC,eAAe,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAA;yBACpE,CAAC,CAAA;qBACH,CAAC,CAAA;oBACF,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;wBAC3B,KAAI,CAAC,IAAI,EAAE,CAAA;wBACX,OAAO,EAAE,CAAA;qBACV,CAAC,CAAA;iBACH,CAAC,CAAA;aACH,CAAC,CAAA;SACH;;;;;QAMS,+BAAW,GAArB,UAAuB,CAAa;YAApC,iBAmBC;YAlBS,IAAA,GAAG,GAAK,IAAI,IAAT,CACV;YAAA,gCACI,IAAI,CAAC,OAAO;gBACf,IAAI,CAAC,MAAM;sBACX,OAAO,CAAC,UAAA,GAAG;;gBACX,IAAI,CAAC,GAAG;oBAAE,OAAM;gBACV,IAAA,KAAwB,KAAI,CAAC,oBAAoB,CAAC;oBACtD,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC;iBACzC,CAAC,EAFK,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,KAAK,QAAA,EAAE,MAAM,QAExB,CAAA;gBACF,GAAG,CAAC,SAAS,EAAE,CAAA;gBACf,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAA;gBAC7B,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC;oBAAE,OAAM;gBACpD,IAAI,KAAI,CAAC,IAAI,KAAK,CAAC;oBAAE,OAAM;;gBAE3B,IAAI,OAAO,GAAG,CAAC,QAAQ,KAAK,UAAU;oBAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAI,EAAE,GAAG,CAAC,CAAA;;gBAEpE,MAAA,KAAI,CAAC,aAAa,+CAAlB,KAAI,EAAiB,CAAC,EAAE,GAAG,CAAC,CAAA;aAC7B,CAAC,CAAA;SACH;;;;;;;;QASa,mCAAe,GAA7B,UACE,QAAyC,EACzC,SAAiB,EACjB,QAAgB;;;;oBAEhB,sBAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;4BACjC,IAAI,IAAI,GAAuC,KAAI,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAA;;4BAExE,IAAI,QAAQ,KAAK,SAAS,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,MAAM,IAAI,KAAI,CAAC,MAAM,EAAE;gCACjE,IAAI,GAAG,KAAI,CAAC,MAAM,CAAA;6BACnB;4BACD,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;gCAAE,OAAM;4BAC/B,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;4BACnC,IAAI,CAAC,OAAO;gCAAE,OAAM;;4BAEpB,IAAM,OAAO,GAAG;gCACd,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC;gCAClC,OAAO,CAAC,WAAW,CAAC,IAAI,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,gBAAgB,CAAC;6BACtF,CAAA;4BACD,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAO,EAAuB;oCAAtB,UAAU,QAAA,EAAE,SAAS,QAAA;;;;;;gDAC/C,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;sDAE/B,OAAO,SAAS,KAAK,UAAU,CAAA,EAA/B,wBAA+B;gDACpB,qBAAM,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,EAAA;;gDAApE,UAAU,GAAG,SAAuD,CAAA;qDAChE,SAAS,EAAT,wBAAS;gDACC,qBAAM,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,EAAA;;gDAAlE,SAAS,GAAG,SAAsD,CAAA;;;gDAGtE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,UAAU,CAAC,CAAA;gDAC/C,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC,CAAA;gDACjE,OAAO,EAAE,CAAA;;;;;6BACV,CAAC,CAAC,KAAK,CAAC,UAAA,GAAG;gCACV,OAAO,CAAC,KAAK,CAAI,QAAQ,SAAI,SAAS,eAAU,QAAQ,UAAK,GAAK,CAAC,CAAA;gCACnE,MAAM,EAAE,CAAA;6BACT,CAAC,CAAA;yBACH,CAAC,EAAA;;;SACH;;;;QAKS,wBAAI,GAAd;YAAA,iBAwJC;;YAvJO,IAAA,KAA+D,IAAI,EAAjE,MAAM,YAAA,EAAE,GAAG,SAAA,EAAE,cAAc,oBAAA,EAAE,aAAa,mBAAA,EAAE,YAAY,kBAAS,CAAA;;YAEzE,MAAA,MAAM,CAAC,UAAU,0CAAE,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;;YAElC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;;YAElD,IAAI,CAAC,KAAK,mCACL,IAAI,CAAC,MAAM,SACX,IAAI,CAAC,OAAO,OAChB,CAAA;YACD,IAAI,IAAI,CAAC,MAAM;gBAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAC7C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;gBACrB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAA;gBACxB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAA;aACzB,CAAC,CAAA;;YAEF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAC,EAAY,EAAE,KAAK,EAAE,UAAU;oBAA9B,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAA;gBACxC,IAAA,KAAyD,cAAc,CAAC,KAAK,EAAE,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC,EAAxG,UAAU,QAAA,EAAE,aAAa,QAAA,EAAE,WAAW,QAAA,EAAE,YAAY,QAAoD,CAAA;gBAC/G,IAAM,CAAC,GAAG,KAAK,CAAC,YAAY,GAAG,KAAI,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;;gBAErE,IAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAA;gBACnC,IAAI,aAAa,CAAC,UAAU,CAAC,EAAE;oBAC7B,GAAG,CAAC,SAAS,GAAG,KAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,UAAW,CAAC,CAAA;oBAC9D,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;oBAClC,GAAG,CAAC,IAAI,EAAE,CAAA;iBACX;;gBAED,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,QAAQ;oBACjD,IAAM,QAAQ,GAAG,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;oBACjD,IAAI,CAAC,QAAQ;wBAAE,OAAM;;oBAEf,IAAA,KAA0B,KAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,EAA7E,SAAS,QAAA,EAAE,UAAU,QAAwD,CAAA;oBAC9E,IAAA,KAAiB;wBACrB,KAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC,GAAG,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;wBAC/D,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;qBAC/B,EAHM,KAAK,QAAA,EAAE,KAAK,QAGlB,CAAA;oBACD,KAAI,CAAC,SAAS,CAAC,GAAG,EAAE,QAAQ,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,CAAA;iBAC3E,CAAC,CAAA;gBACF,OAAO;oBACL,CAAC,EAAE,CAAC,GAAG,WAAW;oBAClB,CAAC,EAAE,CAAC,GAAG,UAAU;oBACjB,CAAC,EAAE,CAAC,GAAG,WAAW,GAAG,YAAY;oBACjC,CAAC,EAAE,CAAC,GAAG,UAAU,GAAG,aAAa;iBAClC,CAAA;aACF,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAA;;YAEvD,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAA;YACzF,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAA;;YAE1F,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,SAAS;gBAC7B,IAAA,KAAwB,KAAI,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,GAAI,EAAE,IAAI,CAAC,GAAI,CAAC,CAAC,EAAxF,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,KAAK,QAAA,EAAE,MAAM,QAAqE,CAAA;;gBAE7F,IAAI,QAAQ,GAAG,KAAK,CAAA;;gBAEpB,IAAI,KAAI,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,KAAI,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE;oBACpD,QAAQ,GAAG,SAAS,KAAK,KAAI,CAAC,SAAS,GAAG,KAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,CAAA;iBAClE;;gBAED,IAAM,UAAU,GAAG,QAAQ,GAAG,YAAY,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,IAAI,aAAa,CAAC,UAAU,CAAC,CAAA;gBACrG,IAAI,aAAa,CAAC,UAAU,CAAC,EAAE;;oBAE7B,IAAM,MAAM,GAAQ,CAClB,QAAQ,GAAG,YAAY,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,aAAa,CAAC,MAAM,CAAC;yBAErE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;yBAClB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;yBACxB,GAAG,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAA,CAAC,CAAA;;oBAEvC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;wBACvB,GAAG,CAAC,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAA;wBAC3B,GAAG,CAAC,aAAa,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAA;wBAC1C,GAAG,CAAC,aAAa,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAA;wBAC1C,GAAG,CAAC,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAA;;wBAE1B,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;wBAC3E,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;qBAC9E;;oBAED,GAAG,CAAC,SAAS,GAAG,KAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,CAAA;oBACtE,IAAM,YAAY,GAAG,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC,CAAA;oBACvG,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,CAAC,CAAA;oBACtD,GAAG,CAAC,IAAI,EAAE,CAAA;;oBAEV,GAAG,CAAC,WAAW,GAAG,kBAAkB,CAAA;oBACpC,GAAG,CAAC,aAAa,GAAG,CAAC,CAAA;oBACrB,GAAG,CAAC,aAAa,GAAG,CAAC,CAAA;oBACrB,GAAG,CAAC,UAAU,GAAG,CAAC,CAAA;iBACnB;gBAGD,IAAI,SAAS,IAAI,KAAI,CAAC,MAAM,CAAC,MAAM,EAAE;oBAEnC,SAAS,IAAI,KAAI,CAAC,MAAM,CAAC,MAAM,CAAA;iBAChC;;gBAED,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,QAAQ;oBAC/C,IAAM,OAAO,GAAG,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;oBAChD,IAAM,SAAS,GAAG,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAA;oBAC3D,IAAI,CAAC,OAAO;wBAAE,OAAM;oBACpB,IAAM,SAAS,GAAG,CAAC,QAAQ,IAAI,SAAS,KAAK,OAAO,CAAA;oBACpD,IAAI,CAAC,SAAS;wBAAE,OAAM;oBAChB,IAAA,KAA0B,KAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,EAAvF,SAAS,QAAA,EAAE,UAAU,QAAkE,CAAA;oBACxF,IAAA,KAAiB;wBACrB,CAAC,GAAG,KAAI,CAAC,UAAU,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;wBAC3E,CAAC,GAAG,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC;qBACxC,EAHM,KAAK,QAAA,EAAE,KAAK,QAGlB,CAAA;oBACD,KAAI,CAAC,SAAS,CAAC,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,CAAA;iBACpE,CAAC,CAAA;;gBAEF,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;;oBAEnC,IAAM,KAAK,GAAG,QAAQ,IAAI,YAAY,CAAC,SAAS;0BAC5C,YAAY,CAAC,SAAS;2BACrB,IAAI,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS,CAAC,CAAA;;oBAE/C,IAAM,UAAU,GAAG,QAAQ,IAAI,YAAY,CAAC,UAAU;0BAClD,YAAY,CAAC,UAAU;2BACtB,IAAI,CAAC,UAAU,IAAI,aAAa,CAAC,UAAU,CAAC,CAAA;;oBAEjD,IAAM,IAAI,GAAG,QAAQ,IAAI,YAAY,CAAC,QAAQ;0BAC1C,KAAI,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC;0BACrC,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAA;;oBAE3D,IAAM,UAAU,GAAG,QAAQ,IAAI,YAAY,CAAC,UAAU;0BAClD,YAAY,CAAC,UAAU;0BACvB,IAAI,CAAC,UAAU,IAAI,aAAa,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,CAAA;oBAC1F,IAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAA;oBAC/E,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,aAAa,CAAC,WAAW,CAAA;oBACjE,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS,CAAA;oBAC3D,GAAG,CAAC,IAAI,GAAM,UAAU,UAAI,IAAI,IAAI,CAAC,YAAM,KAAO,CAAA;oBAClD,GAAG,CAAC,SAAS,GAAG,CAAC,QAAQ,IAAI,YAAY,CAAC,SAAS,IAAI,YAAY,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS,CAAC,CAAA;oBAC3H,IAAI,KAAK,GAAG,EAAE,EAAE,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;;oBAExC,IAAI,QAAQ,EAAE;;wBAEZ,IAAI,UAAQ,GAAG,KAAI,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;wBACjD,KAAK,GAAG,SAAS,CAAC,GAAG,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE,cAAM,OAAA,UAAQ,GAAA,EAAE,SAAS,CAAC,CAAA;qBACrE;yBAAM;wBACL,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;qBACzB;oBACD,KAAK,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,SAAS;wBAC5B,GAAG,CAAC,QAAQ,CACV,IAAI,EACJ,CAAC,GAAG,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,EAC1F,CAAC,GAAG,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,IAAI,KAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CACpF,CAAA;qBACF,CAAC,CAAA;iBACH,CAAC,CAAA;aACH,CAAC,CAAA;;YAEF,MAAA,MAAM,CAAC,SAAS,0CAAE,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;SAClC;;;;;;;;;;QAWO,oCAAgB,GAAxB,UACE,CAAS,EACT,CAAS,EACT,KAAa,EACb,MAAc,EACd,UAAkB;YAEV,IAAA,GAAG,GAAK,IAAI,IAAT,CAAS;;YAEpB,IAAI,UAAU,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;gBAC1C,UAAU,GAAG,iBAAiB,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,CAAA;aACrE;YACD,OAAO,UAAU,CAAA;SAClB;;;;QAKO,+CAA2B,GAAnC;YACQ,IAAA,KAA2C,IAAI,EAA7C,cAAc,oBAAA,EAAE,SAAS,eAAA,EAAE,SAAS,eAAS,CAAA;YACrD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YACzB,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;YAC5C,IAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAA;YAClC,IAAI,CAAC,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,CAAA;YACvC,OAAO,EAAE,CAAC,EAAE;gBACV,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,SAAU,IAAI,SAAS,CAAC,CAAA;gBAClE,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,cAAc,CAAC,gBAAgB,CAAC,GAAG,SAAS,CAAA;gBAC1G,IAAI,SAAS,GAAG,KAAK,EAAE;oBACrB,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,GAAG,SAAS,GAAG,SAAS,GAAG,KAAK,IAAI,QAAQ,GAAG,SAAS,CAAA;oBAC9E,MAAK;iBACN;gBACD,SAAS,GAAG,QAAQ,CAAA;gBACpB,SAAS,GAAG,SAAS,CAAA;aACtB;SACF;;;;QAKM,wBAAI,GAAX;;YACE,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC;gBAAE,OAAM;;YAE3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;;YAE3B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAA;;YAEvB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;;YAEb,MAAA,MAAA,IAAI,CAAC,MAAM,EAAC,UAAU,kDAAI,CAAA;;YAE1B,IAAI,CAAC,GAAG,EAAE,CAAA;SACX;;;;;QAMM,wBAAI,GAAX,UAAa,KAAc;YACzB,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC;gBAAE,OAAM;;YAE9C,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK,CAAC,EAAE;gBACzB,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,KAAK,GAAA,CAAC,CAAA;gBACpD,KAAK,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAA;aAC/B;;YAED,IAAI,KAAK,GAAG,CAAC,EAAE;gBACb,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;gBACb,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAA;aACpB;iBAAM;gBACL,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;gBACb,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA;aAC5C;SACF;;;;;QAMO,uBAAG,GAAX,UAAa,GAAe;;YAAf,oBAAA,EAAA,OAAe;YACpB,IAAA,KAAmD,IAAI,EAArD,GAAG,SAAA,EAAE,IAAI,UAAA,EAAE,MAAM,YAAA,EAAE,SAAS,eAAA,EAAE,cAAc,oBAAS,CAAA;YACrD,IAAA,gBAAgB,GAA8B,cAAc,iBAA5C,EAAE,gBAAgB,GAAY,cAAc,iBAA1B,EAAE,KAAK,GAAK,cAAc,MAAnB,CAAmB;;YAEpE,IAAI,IAAI,KAAK,CAAC,EAAE;gBACd,MAAA,IAAI,CAAC,WAAW,+CAAhB,IAAI,EAAe,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAC,KAAK,EAAE,KAAK,IAAK,OAAA,KAAK,KAAK,SAAS,GAAA,CAAC,IAAI,EAAE,CAAC,CAAA;gBACjF,OAAM;aACP;;YAED,IAAI,SAAS,KAAK,CAAC,CAAC;gBAAE,OAAM;;YAE5B,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ;gBAAE,IAAI,CAAC,2BAA2B,EAAE,CAAA;;YAEpE,IAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAA;YACjD,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,CAAA;YAC7C,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;;YAE9B,IAAI,IAAI,KAAK,CAAC,IAAI,aAAa,GAAG,gBAAgB,EAAE;;gBAElD,IAAI,CAAC,GAAG,GAAG,aAAa,GAAG,GAAG,CAAA;gBAC9B,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,GAAG,EAAE,KAAK,GAAG,GAAG,EAAE,gBAAgB,CAAC,CAAA;;gBAEhF,IAAI,SAAS,KAAK,KAAK,EAAE;oBACvB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;iBACd;gBACD,SAAS,GAAG,SAAS,GAAG,SAAS,GAAG,MAAM,CAAC,MAAM,CAAA;aAClD;iBAAM,IAAI,IAAI,KAAK,CAAC,EAAE;;gBAErB,SAAS,GAAG,SAAS,GAAG,KAAK,GAAG,MAAM,CAAC,MAAM,CAAA;;gBAE7C,IAAI,SAAS,KAAK,KAAK,CAAC,IAAI,SAAS,IAAI,CAAC,EAAE;oBAC1C,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;;oBAEb,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA;oBAClB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAA;iBAClB;aACF;iBAAM,IAAI,IAAI,KAAK,CAAC,EAAE;;gBAErB,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAA;gBACtF,IAAI,WAAW,IAAI,gBAAgB,EAAE;oBACnC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;iBACd;aACF;iBAAM;;gBAEL,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;aACd;YACD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;YAC1B,IAAI,CAAC,IAAI,EAAE,CAAA;YACX,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;SAClC;;;;;;QAOO,wCAAoB,GAA5B,UAA8B,EAAkC;gBAAjC,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,UAAO,EAAP,GAAG,mBAAG,CAAC,KAAA,EAAE,UAAO,EAAP,GAAG,mBAAG,CAAC,KAAA;YAC7C,IAAA,KAA4B,IAAI,EAA9B,SAAS,eAAA,EAAE,UAAU,gBAAS,CAAA;YACtC,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAA;YACzC,IAAI,GAAG,GAAG;gBACR,IAAI,CAAC,SAAU,CAAC,CAAC,GAAG,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC;gBAC5C,IAAI,CAAC,SAAU,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,MAAM,IAAI,CAAC;aAC9C,CAAA;YACD,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,CACpB,SAAS,GAAG,GAAG,GAAG,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,EACpC,UAAU,GAAG,GAAG,GAAG,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CACtC,CAAA;YACD,OAAO,GAAG,CAAA;SACX;;;;;;QAOS,kCAAc,GAAxB,UAA0B,CAAS,EAAE,CAAS;YACpC,IAAA,MAAM,GAAK,IAAI,OAAT,CAAS;YACvB,OAAO,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;SACxC;QACH,gBAAC;IAAD,CA7mBA,CAAuC,KAAK;;;QCNH,+BAAK;;;;;;QAoD3C,qBAAa,MAAsB,EAAE,IAAuB;;YAA5D,YACC,kBAAM,MAAM,EAAE;gBACZ,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,MAAM,EAAE,IAAI,CAAC,MAAM;aACpB,CAAC,SAQH;;YA9DO,YAAM,GAAqB,EAAE,CAAA;;YAE7B,YAAM,GAAqB,EAAE,CAAA;;YAE7B,WAAK,GAAoB,EAAE,CAAA;;YAE3B,mBAAa,GAAsB,EAAE,CAAA;YACrC,oBAAc,GAAgC,EAAiC,CAAA;;YAE/E,kBAAY,GAAqB,EAAE,CAAA;YACnC,mBAAa,GAA+B,EAAgC,CAAA;YAC5E,iBAAW,GAAoB,eAAQ,CAAA;YAGvC,eAAS,GAAG,CAAC,CAAA;YACb,gBAAU,GAAG,CAAC,CAAA;YACd,oBAAc,GAAG,CAAC,CAAA;YAClB,qBAAe,GAAG,CAAC,CAAA;YACnB,sBAAgB,GAAG,CAAC,CAAA;YACpB,SAAG,GAAG,IAAI,CAAA;YACV,YAAM,GAAa,EAAE,CAAA;YACrB,gBAAU,GAAa,EAAE,CAAA;YACzB,eAAS,GAAa,EAAE,CAAA;YACxB,eAAS,GAAG,CAAC,CAAA;YACb,aAAO,GAAG,CAAC,CAAA;;;;;;;;YAQX,UAAI,GAAkB,CAAC,CAAA;;;;;;;YAOvB,eAAS,GAAyB,KAAK,CAAC,CAAA;;YAIxC,gBAAU,GAAG,IAAI,GAAG,EAAE,CAAA;YAY5B,KAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;YACnB,KAAI,CAAC,SAAS,EAAE,CAAA;YAChB,KAAI,CAAC,YAAY,EAAE,CAAA;;YAEnB,MAAA,MAAM,CAAC,YAAY,0CAAE,IAAI,CAAC,KAAI,CAAC,CAAA;;YAE/B,KAAI,CAAC,IAAI,EAAE,CAAA;;SACZ;QAES,4BAAM,GAAhB;;YACE,iBAAM,MAAM,WAAE,CAAA;YACd,IAAI,CAAC,IAAI,EAAE,CAAA;YACX,MAAA,MAAA,IAAI,CAAC,MAAM,EAAC,WAAW,kDAAI,CAAA;SAC5B;QAES,+BAAS,GAAnB;YACE,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA;YAClB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAA;YACnB,IAAI,CAAC,cAAc,GAAG,CAAC,CAAA;YACvB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAA;YACxB,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAA;YACzB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAA;YACf,IAAI,CAAC,MAAM,GAAG,EAAE,CAAA;YAChB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;YACpB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAA;YACnB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA;YAClB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAA;YAChB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAA;YACvB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;YACb,iBAAM,SAAS,WAAE,CAAA;SAClB;;;;;QAMO,8BAAQ,GAAhB,UAAkB,IAAuB;YACvC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;YACpC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;YACtC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAA;YAC5C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAA;YAC5C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAA;YAC1C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC,aAAa,IAAI,EAAE,CAAC,CAAA;YAC1D,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,cAAc,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC,CAAA;YACxD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;SACzC;;;;QAKO,kCAAY,GAApB;YAAA,iBA+BC;;YA7BC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,gBAAgB,EAAE;gBACrC,IAAM,MAAM,cACV,IAAI,EAAE,UAAU,EAChB,UAAU,EAAE,CAAC,EACb,UAAU,EAAE,CAAC,EACb,KAAK,EAAE,EAAE,EACT,SAAS,EAAE,CAAC,EACZ,gBAAgB,EAAE,IAAI,EACtB,gBAAgB,EAAE,IAAI,IACnB,KAAI,CAAC,aAAa,CACtB,CAAA;gBACD,MAAM,CAAC,UAAU,GAAG,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;gBACrD,MAAM,CAAC,UAAU,GAAG,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;gBACrD,OAAO,MAAM,CAAA;aACd,CAAC,CAAA;;YAEF,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,eAAe,EAAE;gBACpC,kBACE,YAAY,EAAE,CAAC,EACf,SAAS,EAAE,MAAM,EACjB,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,YAAY,EACvB,UAAU,EAAE,KAAK,EACjB,UAAU,EAAE,eAAe,EAC3B,QAAQ,EAAE,IAAI,EACd,WAAW,EAAE,KAAK,IACf,KAAI,CAAC,YAAY,EACrB;aACF,CAAC,CAAA;SACH;;;;QAKO,+BAAS,GAAjB;YAAA,iBA2BC;;YAzBC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,UAAC,MAAuB;gBAC3C,KAAI,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAA;gBACxB,KAAI,CAAC,MAAM,EAAE,CAAA;aACd,CAAC,CAAA;;YAEF,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAC,MAAuB;gBAC5C,KAAI,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;gBACzB,KAAI,CAAC,MAAM,EAAE,CAAA;aACd,CAAC,CAAA;;YAEF,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAC,OAAyB;gBAC9C,KAAI,CAAC,cAAc,EAAE,CAAA;aACtB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;;YAElB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAC,OAAyB;gBAC9C,KAAI,CAAC,cAAc,EAAE,CAAA;aACtB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;;YAElB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,UAAC,OAAyB;gBAC7C,KAAI,CAAC,mBAAmB,EAAE,CAAA;gBAC1B,KAAI,CAAC,IAAI,EAAE,CAAA;aACZ,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;YAClB,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,EAAE,GAAA,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;YAC/D,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,EAAE,GAAA,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;YAC9D,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,EAAE,GAAA,CAAC,CAAA;SAC9C;;;;QAKY,0BAAI,GAAjB;;;;;;;4BACE,IAAI,CAAC,SAAS,EAAE,CAAA;4BACR,MAAM,GAAK,IAAI,OAAT,CAAS;;4BAEvB,MAAA,MAAM,CAAC,UAAU,0CAAE,IAAI,CAAC,IAAI,CAAC,CAAA;;4BAE7B,IAAI,CAAC,mBAAmB,EAAE,CAAA;4BAC1B,IAAI,CAAC,IAAI,EAAE,CAAA;;4BAEX,qBAAM,IAAI,CAAC,cAAc,EAAE;;8BAAA;;;4BAA3B,SAA2B,CAAA;;4BAE3B,MAAA,MAAM,CAAC,SAAS,0CAAE,IAAI,CAAC,IAAI,CAAC,CAAA;;;;;SAC7B;QAEO,oCAAc,GAAtB;YAAA,iBAsBC;YArBC,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO;gBACzB,IAAM,cAAc,GAAG;oBACrB,MAAM,EAAE,KAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,IAAI,GAAA,CAAC;oBAC5C,MAAM,EAAE,KAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,IAAI,GAAA,CAAC;iBAC7C,CACA;gBAAkC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAE,CAAC,OAAO,CAAC,UAAA,OAAO;oBAC7E,IAAM,UAAU,GAAG,cAAc,CAAC,OAAO,CAAC,CAAA;;oBAE1C,IAAM,UAAU,GAAoB,EAAE,CAAA;oBACtC,UAAU,IAAI,UAAU,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,SAAS;wBAC/C,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,QAAQ;4BACrC,UAAU,CAAC,IAAI,CAAC,KAAI,CAAC,eAAe,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAA;yBACpE,CAAC,CAAA;qBACH,CAAC,CAAA;oBACF,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;wBAC3B,KAAI,CAAC,mBAAmB,EAAE,CAAA;wBAC1B,KAAI,CAAC,IAAI,EAAE,CAAA;wBACX,OAAO,EAAE,CAAA;qBACV,CAAC,CAAA;iBACH,CAAC,CAAA;aACH,CAAC,CAAA;SACH;;;;;;;;QASa,qCAAe,GAA7B,UACE,QAA6B,EAC7B,SAAiB,EACjB,QAAgB;;;;oBAEhB,sBAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;4BACjC,IAAI,IAAI,GAA0B,KAAI,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAA;4BAC3D,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;gCAAE,OAAM;4BAC/B,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;4BACnC,IAAI,CAAC,OAAO;gCAAE,OAAM;;4BAEpB,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,UAAM,OAAO;;;;kDAC/C,OAAO,OAAO,CAAC,SAAS,KAAK,UAAU,CAAA,EAAvC,wBAAuC;4CAC/B,qBAAM,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,EAAA;;4CAAtE,OAAO,GAAG,SAA4D,CAAA;;;4CAExE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAA;4CAC5C,OAAO,EAAE,CAAA;;;;iCACV,CAAC,CAAC,KAAK,CAAC,UAAA,GAAG;gCACV,OAAO,CAAC,KAAK,CAAI,QAAQ,SAAI,SAAS,eAAU,QAAQ,UAAK,GAAK,CAAC,CAAA;gCACnE,MAAM,EAAE,CAAA;6BACT,CAAC,CAAA;yBACH,CAAC,EAAA;;;SACH;;;;QAKS,yCAAmB,GAA7B;YAAA,iBA4GC;YA3GO,IAAA,KAAoC,IAAI,EAAtC,cAAc,oBAAA,EAAE,aAAa,mBAAS,CAAA;YACxC,IAAA,KAAW,IAAI,CAAC,UAAU,EAAG,EAA3B,CAAC,OAAA,EAAE,CAAC,OAAuB,CAAA;;YAEnC,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA;YAC9B,IAAA,KAA+D,IAAI,CAAC,yBAAyB,EAAE,EAA7F,SAAS,eAAA,EAAE,UAAU,gBAAA,EAAE,eAAe,qBAAA,EAAE,gBAAgB,sBAAqC,CAAA;YACrG,IAAM,YAAY,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,GAAA,CAAC,CAAA;gBAClD,WAAW,GAAG,CAAC,EAAE,YAAY,GAAG,EAAC;YACtD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,SAAS;;gBAEjC,IAAI,KAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,KAAK,CAAC;oBAAE,KAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;;gBAEjE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,YAAY,CAAA;;gBAEvC,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAA;gBAEjC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,GAAG,eAAe,GAAG,OAAO,CAAC,CAAA;gBAClE,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,GAAG,gBAAgB,GAAG,OAAO,CAAC,CAAA;aACtE,CAAC,CAAA;;YAEI,IAAA,KAA6B,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,YAAY,CAAE,EAA9E,gBAAgB,sBAAA,EAAE,IAAI,UAAwD,CAAA;YACtF,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAA;;YAExC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,SAAS;gBACjC,IAAM,KAAK,GAAG,SAAS,GAAG,SAAS,CAAA;gBACnC,IAAM,KAAK,GAAG,UAAU,GAAG,SAAS,CAAA;gBACpC,IAAI,YAAY,GAAG,CAAC,CAAA;;gBAEpB,IAAM,SAAS,GAAG,qBAAqB,CAAC,KAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAM,CAAC,CAAA;;gBAEjE,IAAI,CAAC,SAAS,CAAC,MAAM;oBAAE,OAAM;gBAC7B,SAAS,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,SAAS;oBAChC,IAAI,CAAC,IAAI;wBAAE,OAAM;oBACE,IAAI,CAAC,KAAM,CAAC,SAAS,EAAC;oBACzC,IAAM,OAAO,GAAG,eAAe,GAAG,SAAS,GAAG,cAAc,CAAC,UAAU,GAAG,CAAC,CAAA;oBAC3E,IAAM,OAAO,GAAG,gBAAgB,GAAG,SAAS,GAAG,cAAc,CAAC,UAAU,GAAG,CAAC,CAAA;oBACtE,IAAA,KAAoB,KAAI,CAAC,YAAY,CACzC,CAAC,KAAK,EAAE,OAAO,EAAE,gBAAgB,CAAC,EAClC,CAAC,OAAO,EAAE,KAAK,EAAE,eAAe,CAAC,CAClC,EAHM,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,OAAO,QAGrB,CAAA;oBACD,YAAY,IAAI,OAAO,CAAA;;oBAEvB,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,aAAa,CAAC,UAAU,CAAA;oBAC9D,IAAI,aAAa,CAAC,UAAU,CAAC,EAAE;wBAC7B,IAAM,YAAY,GAAG,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,cAAc,CAAC,GAAG,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC,CAAA;wBAC/G,cAAc,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,CAAC,CAAA;wBAChE,IAAI,CAAC,SAAS,GAAG,UAAU,CAAA;wBAC3B,IAAI,CAAC,IAAI,EAAE,CAAA;qBACZ;;oBAED,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,QAAQ;wBAC/C,IAAM,OAAO,GAAG,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;wBAChD,IAAI,CAAC,OAAO;4BAAE,OAAM;wBACd,IAAA,KAA0B,KAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC,EAA7F,SAAS,QAAA,EAAE,UAAU,QAAwE,CAAA;wBAC9F,IAAA,KAAiB;4BACrB,EAAE,GAAG,KAAI,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,GAAG,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC;4BACpF,EAAE,GAAG,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC;yBAC7C,EAHM,KAAK,QAAA,EAAE,KAAK,QAGlB,CAAA;wBACD,KAAI,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,CAAA;qBACnE,CAAC,CAAA;;oBAEF,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;;wBAEnC,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS,CAAA;;wBAEvD,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,aAAa,CAAC,UAAU,CAAA;;wBAE9D,IAAM,IAAI,GAAG,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAA;;wBAEpE,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,aAAa,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,CAAA;wBACzG,IAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAA;wBAC/E,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,aAAa,CAAC,WAAW,CAAA;wBACjE,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS,CAAA;wBAC3D,IAAI,CAAC,IAAI,GAAM,UAAU,UAAI,IAAI,IAAI,CAAC,YAAM,KAAO,CAAA;wBACnD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS,CAAA;wBAC1D,IAAI,KAAK,GAAG,EAAE,EAAE,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;;wBAExC,IAAI,QAAQ,EAAE;;4BAEZ,IAAI,UAAQ,GAAG,KAAI,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAA;4BACrD,KAAK,GAAG,SAAS,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE,cAAM,OAAA,UAAQ,GAAA,EAAE,SAAS,CAAC,CAAA;yBACtE;6BAAM;4BACL,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;yBACzB;wBACD,KAAK,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,SAAS;4BAC5B,IAAI,CAAC,QAAQ,CACX,IAAI,EACJ,EAAE,GAAG,KAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,EACpG,EAAE,GAAG,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,IAAI,KAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CACzF,CAAA;yBACF,CAAC,CAAA;qBACH,CAAC,CAAA;iBACH,CAAC,CAAA;gBACI,IAAA,KAAmB,KAAI,CAAC,YAAY,CACxC,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,YAAY,CAAC,EACnC,CAAC,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,CAAC,CACrC,EAHM,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAGpB,CAAA;gBACD,IAAI,OAAO,GAAG,YAAY,CAAA;gBAC1B,OAAO,OAAO,GAAG,YAAY,GAAG,YAAY,EAAE;oBACtC,IAAA,KAAiB,KAAI,CAAC,YAAY,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,EAA/D,KAAK,QAAA,EAAE,KAAK,QAAmD,CAAA;oBACtE,KAAI,CAAC,SAAS,CACZ,IAAI,EAAE,gBAAgB,EACtB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACd,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,CACrB,CAAA;oBACD,OAAO,IAAI,YAAY,CAAA;iBACxB;aACF,CAAC,CAAA;SACH;;;;QAKS,gCAAU,GAApB;YAAA,iBA6BC;gBA5BO,KAAiD,IAAI,WAA7C,MAAE,GAAG,SAAA,mBAAgB,MAAE,aAAa,oBAAS;;YAE3D,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAC,EAAY,EAAE,KAAK,EAAE,UAAU;oBAA9B,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAA;gBAC/C,IAAA,KAAyD,cAAc,CAAC,KAAK,EAAE,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC,EAAxG,UAAU,QAAA,EAAE,aAAa,QAAA,EAAE,WAAW,QAAA,EAAE,YAAY,QAAoD,CAAA;gBAC/G,IAAM,CAAC,GAAG,KAAK,CAAC,YAAY,GAAG,KAAI,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;;gBAErE,IAAM,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,aAAa,CAAC,UAAU,CAAA;gBAC/D,IAAI,aAAa,CAAC,UAAU,CAAC,EAAE;oBAC7B,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;oBAClC,GAAG,CAAC,SAAS,GAAG,UAAU,CAAA;oBAC1B,GAAG,CAAC,IAAI,EAAE,CAAA;iBACX;;gBAED,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,QAAQ;oBACjD,IAAM,QAAQ,GAAG,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;oBACjD,IAAI,CAAC,QAAQ;wBAAE,OAAM;;oBAEf,IAAA,KAA0B,KAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,EAA7E,SAAS,QAAA,EAAE,UAAU,QAAwD,CAAA;oBAC9E,IAAA,KAAiB,CAAC,KAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC,GAAG,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAjH,KAAK,QAAA,EAAE,KAAK,QAAqG,CAAA;oBACxH,KAAI,CAAC,SAAS,CAAC,GAAG,EAAE,QAAQ,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,CAAA;iBAC3E,CAAC,CAAA;gBACF,OAAO;oBACL,CAAC,EAAE,CAAC,GAAG,WAAW;oBAClB,CAAC,EAAE,CAAC,GAAG,UAAU;oBACjB,CAAC,EAAE,CAAC,GAAG,WAAW,GAAG,YAAY;oBACjC,CAAC,EAAE,CAAC,GAAG,UAAU,GAAG,aAAa;iBAClC,CAAA;aACF,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAA;SACxD;;;;QAKS,0BAAI,GAAd;YAAA,iBAkCC;;gBAjCO,KAAiD,IAAI,EAAnD,MAAM,YAAA,EAAE,GAAG,SAAA,mBAAgB,mBAAwB;;YAE3D,MAAA,MAAM,CAAC,UAAU,0CAAE,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;;YAElC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;;YAE5C,IAAA,KAAiB,IAAI,CAAC,UAAU,EAAG,EAAjC,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAuB,CAAA;;YAEzC,IAAI,CAAC,IAAI,CAAC,gBAAgB;gBAAE,OAAM;YAC5B,IAAA,KAA+E,IAAI,EAAjF,SAAS,eAAA,EAAE,UAAU,gBAAA,EAAE,cAAc,oBAAA,EAAE,eAAe,qBAAA,EAAE,gBAAgB,sBAAS,CAAA;YACzF,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,SAAS;;gBAEjC,IAAM,EAAE,GAAG,cAAc,GAAG,IAAI,CAAC,KAAM,CAAC,MAAM,CAAA;;gBAE9C,IAAM,KAAK,GAAG,KAAI,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAA;gBACxF,IAAI,MAAM,GAAG,KAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,KAAK,CAAA;;gBAE3C,IAAI,MAAM,GAAG,CAAC,EAAE;oBACd,MAAM,GAAG,MAAM,GAAG,EAAE,GAAG,EAAE,CAAA;iBAC1B;gBACD,IAAI,MAAM,GAAG,EAAE,EAAE;oBACf,MAAM,GAAG,MAAM,GAAG,EAAE,CAAA;iBACrB;gBACK,IAAA,KAAmB,KAAI,CAAC,YAAY,CACxC,CAAC,SAAS,GAAG,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,EAC7C,CAAC,MAAM,EAAE,UAAU,GAAG,SAAS,EAAE,CAAC,EAAE,UAAU,CAAC,CAChD,EAHM,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAGpB,CAAA;gBACK,IAAA,KAAmB,KAAI,CAAC,YAAY,CACxC,CAAC,CAAC,GAAG,eAAe,GAAG,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAClD,CAAC,CAAC,EAAE,CAAC,GAAG,gBAAgB,GAAG,SAAS,EAAE,CAAC,EAAE,UAAU,CAAC,CACrD,EAHM,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAGpB,CAAA;gBACD,KAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAI,CAAC,gBAAiB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;aAC5E,CAAC,CAAA;SACH;;;;QAKO,iDAA2B,GAAnC;YAAA,iBAsBC;YArBO,IAAA,KAAgD,IAAI,EAAlD,cAAc,oBAAA,EAAE,SAAS,eAAA,EAAE,cAAc,oBAAS,CAAA;;YAE1D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YACzB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,SAAS;gBACjC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAM,CAAA;gBACzB,IAAI,CAAC,KAAK,CAAC,MAAM;oBAAE,OAAM;gBACzB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,cAAc,CAAC,KAAK,CAAA;gBAChD,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,cAAc,CAAC,SAAS,CAAA;gBAC5D,IAAM,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,KAAK,SAAU,CAAC,SAAS,CAAC,GAAA,CAAC,CAAA;gBACtF,IAAM,EAAE,GAAG,cAAc,GAAG,KAAK,CAAC,MAAM,CAAA;gBACxC,IAAM,UAAU,GAAG,KAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,KAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;gBACtE,IAAI,CAAC,GAAG,CAAC,CAAA;gBACT,OAAO,EAAE,CAAC,EAAE;oBACV,IAAM,SAAS,GAAG,cAAc,GAAG,UAAU,IAAI,EAAE,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,UAAU,CAAA;oBACjF,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,KAAI,CAAC,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,CAAC,gBAAgB,CAAC,GAAG,UAAU,CAAA;oBAC7G,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,KAAK,EAAE;wBAC/B,KAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,SAAS,CAAA;wBACrC,MAAK;qBACN;iBACF;aACF,CAAC,CAAA;SACH;;;;QAKO,0BAAI,GAAX;;YACC,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC;gBAAE,OAAM;;YAE3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;;YAE3B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAA;;YAEvB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;;YAEb,MAAA,MAAA,IAAI,CAAC,MAAM,EAAC,UAAU,kDAAI,CAAA;;YAE1B,IAAI,CAAC,GAAG,EAAE,CAAA;SACX;QAEM,0BAAI,GAAX,UAAa,KAAwB;;YACnC,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC;gBAAE,OAAM;;YAE9C,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;aAC1D;iBAAM,IAAI,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE;gBACvC,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;oBACtC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;iBACvB;qBAAM;oBACL,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;oBACb,OAAO,OAAO,CAAC,KAAK,CAAC,WAAS,KAAK,wDAAa,CAAC,CAAA;iBAClD;aACF;iBAAM;gBACL,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;gBACb,OAAO,OAAO,CAAC,KAAK,CAAC,mEAAoB,OAAO,KAAO,CAAC,CAAA;aACzD;;YAED,IAAI,MAAA,IAAI,CAAC,SAAS,0CAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;gBAChC,IAAI,CAAC,SAAS,GAAG,EAAE,CAAA;;gBAEnB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;aACd;iBAAM;;gBAEL,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;aACd;SACF;;;;;QAMO,yBAAG,GAAX,UAAa,GAAe;YAA5B,iBA+DC;;YA/DY,oBAAA,EAAA,OAAe;YACpB,IAAA,KAAkE,IAAI,EAApE,GAAG,SAAA,EAAE,IAAI,UAAA,EAAE,SAAS,eAAA,EAAE,cAAc,oBAAA,EAAE,cAAc,oBAAA,EAAE,KAAK,WAAS,CAAA;YACpE,IAAA,gBAAgB,GAAuB,cAAc,iBAArC,EAAE,gBAAgB,GAAK,cAAc,iBAAnB,CAAmB;;YAE7D,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,MAAM,MAAK,KAAK,CAAC,MAAM,EAAE;gBACzD,IAAI,MAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;gBACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACrC,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;oBAC9C,IAAI,EAAC,MAAA,IAAI,CAAC,KAAK,0CAAE,QAAQ,CAAC,QAAQ,CAAC,CAAA,IAAI,MAAI,KAAK,QAAQ,EAAE;wBACxD,MAAI,GAAG,CAAC,CAAC,CAAA;wBACT,MAAK;qBACN;iBACF;gBACD,MAAA,IAAI,CAAC,WAAW,+CAAhB,IAAI,EAAe,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAC,KAAK,EAAE,KAAK,IAAK,OAAA,KAAK,KAAK,MAAI,GAAA,CAAC,IAAI,KAAK,CAAC,CAAC,CAAA;gBAChF,OAAM;aACP;;YAED,IAAI,SAAS,KAAK,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM;gBAAE,OAAM;;YAErD,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM;gBAAE,IAAI,CAAC,2BAA2B,EAAE,CAAA;;YAEjF,IAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAA;YACjD,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,CAAA;;YAE7C,KAAK,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,SAAS;gBAC5B,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;gBACxB,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM;oBAAE,OAAM;gBACnC,IAAM,EAAE,GAAG,cAAc,GAAG,KAAK,CAAC,MAAM,CAAA;gBACxC,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,cAAc,CAAC,KAAK,CAAC,CAAA;gBAC1D,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,cAAc,CAAC,SAAS,CAAA;gBAC5D,IAAI,MAAM,GAAG,CAAC,EAAE,UAAU,GAAG,KAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;gBACnD,IAAI,IAAI,KAAK,CAAC,IAAI,aAAa,GAAG,gBAAgB,EAAE;;oBAElD,KAAI,CAAC,GAAG,GAAG,aAAa,GAAG,GAAG,CAAA;oBAC9B,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAA;;oBAExE,IAAI,SAAS,KAAK,KAAK,EAAE;wBACvB,KAAI,CAAC,IAAI,GAAG,CAAC,CAAA;qBACd;oBACD,MAAM,GAAG,CAAC,UAAU,IAAI,SAAS,GAAG,SAAS,CAAC,IAAI,EAAE,CAAA;iBACrD;qBAAM,IAAI,IAAI,KAAK,CAAC,EAAE;;oBAErB,MAAM,GAAG,CAAC,UAAU,IAAI,KAAK,GAAG,SAAS,CAAC,IAAI,EAAE,CAAA;;oBAEhD,IAAI,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,MAAM,MAAK,KAAK,CAAC,MAAM,EAAE;wBACtC,KAAI,CAAC,IAAI,GAAG,CAAC,CAAA;;wBAEb,KAAI,CAAC,UAAU,GAAG,EAAE,CAAA;wBACpB,KAAI,CAAC,SAAS,GAAG,EAAE,CAAA;qBACpB;iBACF;qBAAM,IAAI,IAAI,KAAK,CAAC,IAAI,WAAW,EAAE;;oBAEpC,IAAM,UAAU,GAAG,KAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAA;oBAC7C,IAAM,SAAS,GAAG,KAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;oBAC3C,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,gBAAgB,CAAC,CAAA;oBAC3E,IAAI,WAAW,IAAI,gBAAgB,EAAE;wBACnC,KAAI,CAAC,IAAI,GAAG,CAAC,CAAA;qBACd;iBACF;gBACD,KAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,MAAM,CAAA;aAChC,CAAC,CAAA;YACF,IAAI,CAAC,IAAI,EAAE,CAAA;YACX,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;SAClC;;QAGO,kCAAY,GAApB,UAAyB,CAAI,EAAE,CAAI;YACjC,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC,CAAA;SACzD;;QAGO,+CAAyB,GAAjC;YACE,IAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAA;YACrC,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAA;YAC5B,IAAA,KAA6B,IAAI,CAAC,cAAc,EAA9C,UAAU,gBAAA,EAAE,UAAU,gBAAwB,CAAA;gBAChD,KAAiB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,EAAG,MAAlD,MAAG,MAAE,CAAC,OAAA,EAAE,CAAC,QAAyC;YAC3D,IAAI,SAAS,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,eAAe,GAAG,CAAC,EAAE,gBAAgB,GAAG,CAAC,CAAA;YAC5E,IAAI,IAAI,KAAK,YAAY,EAAE;gBACzB,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,UAAU,IAAI,QAAQ,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAA;gBAC3E,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,CAAA;aACxC;iBAAM;gBACL,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,UAAU,IAAI,QAAQ,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAA;gBACzE,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;aACzC;YACD,eAAe,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,CAAA;YACpE,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;YACvE,IAAI,IAAI,KAAK,YAAY,EAAE;gBACzB,IAAI,CAAC,cAAc,GAAG,eAAe,CAAA;aACtC;iBAAM;gBACL,IAAI,CAAC,cAAc,GAAG,gBAAgB,CAAA;aACvC;YACD,OAAO;gBACL,SAAS,WAAA;gBACT,UAAU,YAAA;gBACV,eAAe,iBAAA;gBACf,gBAAgB,kBAAA;aACjB,CAAA;SACF;QACH,kBAAC;IAAD,CA3lBA,CAAyC,KAAK;;ICN9C;;;;;;QAMa,QAAQ,GAAG,UAAC,GAAY,EAAE,MAAc;QACnD,IAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;QAC/C,IAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAE,CAAA;QAC5B,IAAA,KAAK,GAAa,GAAG,MAAhB,EAAE,MAAM,GAAK,GAAG,OAAR,CAAQ;QAC7B,MAAM,CAAC,KAAK,GAAG,KAAK,CAAA;QACpB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAA;QACtB,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAA;QAChD,GAAG,CAAC,IAAI,EAAE,CAAA;QACV,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAA;QACvC,OAAO,MAAM,CAAA;IACf,EAAC;IAED;;;;;;QAMa,OAAO,GAAG,UACrB,GAAY,EACZ,OAAe;QAEf,IAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;QAC/C,IAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAE,CAAA;QAC5B,IAAA,KAAK,GAAa,GAAG,MAAhB,EAAE,MAAM,GAAK,GAAG,OAAR,CAAQ;QAC7B,MAAM,CAAC,KAAK,GAAG,KAAK,CAAA;QACpB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAA;;QAEtB,IAAI,OAAO,GAAG,CAAC,MAAM,KAAK,QAAQ,EAAE;YAClC,GAAG,CAAC,MAAM,GAAG,aAAW,OAAO,GAAG,GAAG,OAAI,CAAA;YACzC,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAA;SACxC;aAAM;YACL,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAA;YACvC,IAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAA;YAC/C,IAAA,IAAI,GAAK,SAAS,KAAd,CAAc;YAC1B,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;gBAC/B,IAAM,KAAK,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;gBACzB,IAAI,KAAK,KAAK,CAAC;oBAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,CAAA;aAC/C;YACD,GAAG,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;SAClC;QACD,OAAO,MAAM,CAAA;IACf;;;;;;;;;;;;;;"}