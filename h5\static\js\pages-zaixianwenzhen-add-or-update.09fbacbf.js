(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-zaixianwenzhen-add-or-update"],{"4cd7":function(e,i,n){"use strict";var t=n("9fae"),r=n.n(t);r.a},"5d06":function(e,i,n){"use strict";n.r(i);var t=n("c291"),r=n("e2cf");for(var a in r)"default"!==a&&function(e){n.d(i,e,(function(){return r[e]}))}(a);n("4cd7");var o,s=n("f0c5"),u=Object(s["a"])(r["default"],t["b"],t["c"],!1,null,"5a210548",null,!1,t["a"],o);i["default"]=u.exports},"86d9":function(e,i,n){var t=n("24fb");i=t(!1),i.push([e.i,'@charset "UTF-8";\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.content[data-v-5a210548]{min-height:calc(100vh - 44px);box-sizing:border-box}',""]),e.exports=i},"9fae":function(e,i,n){var t=n("86d9");"string"===typeof t&&(t=[[e.i,t,""]]),t.locals&&(e.exports=t.locals);var r=n("4f06").default;r("6d23e330",t,!0,{sourceMap:!1,shadowMode:!1})},c291:function(e,i,n){"use strict";var t={"w-picker":n("e2b1").default},r=function(){var e=this,i=e.$createElement,n=e._self._c||i;return n("v-uni-view",{staticClass:"content"},[n("v-uni-view",{style:{width:"100%",padding:"0",position:"relative",background:"#fff",height:"100%"}},[n("v-uni-form",{staticClass:"app-update-pv",style:{width:"100%",padding:"24rpx",background:"#fff",display:"block",height:"auto"}},[n("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.zhaopianTap.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("照片")]),e.ruleForm.zhaopian?n("v-uni-image",{staticClass:"avator",style:{width:"80rpx",borderRadius:"100%",objectFit:"cover",display:"block",height:"80rpx"},attrs:{src:e.baseUrl+e.ruleForm.zhaopian.split(",")[0],mode:"aspectFill"}}):n("v-uni-image",{staticClass:"avator",style:{width:"80rpx",borderRadius:"100%",objectFit:"cover",display:"block",height:"80rpx"},attrs:{src:"../../static/gen/upload.png",mode:"aspectFill"}})],1),n("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[n("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("问诊时间")]),n("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.wenzhenshijian,placeholder:"问诊时间"},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.toggleTab("wenzhenshijian")}},model:{value:e.ruleForm.wenzhenshijian,callback:function(i){e.$set(e.ruleForm,"wenzhenshijian",i)},expression:"ruleForm.wenzhenshijian"}})],1),n("v-uni-view",{staticClass:" select",style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[n("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("回复状态")]),n("v-uni-picker",{style:{width:"100%",flex:"1",height:"auto"},attrs:{disabled:e.ro.huifuzhuangtai,value:e.huifuzhuangtaiIndex,range:e.huifuzhuangtaiOptions},on:{change:function(i){arguments[0]=i=e.$handleEvent(i),e.huifuzhuangtaiChange.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"uni-input",style:{width:"100%",lineHeight:"80rpx",fontSize:"28rpx",color:"#D4CF5D"}},[e._v(e._s(e.ruleForm.huifuzhuangtai?e.ruleForm.huifuzhuangtai:"请选择回复状态"))])],1)],1),n("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[n("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("医生工号")]),n("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.yishenggonghao,placeholder:"医生工号",type:"text"},model:{value:e.ruleForm.yishenggonghao,callback:function(i){e.$set(e.ruleForm,"yishenggonghao",i)},expression:"ruleForm.yishenggonghao"}})],1),n("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[n("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("医生姓名")]),n("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.yishengxingming,placeholder:"医生姓名",type:"text"},model:{value:e.ruleForm.yishengxingming,callback:function(i){e.$set(e.ruleForm,"yishengxingming",i)},expression:"ruleForm.yishengxingming"}})],1),n("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[n("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("用户账号")]),n("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.yonghuzhanghao,placeholder:"用户账号",type:"text"},model:{value:e.ruleForm.yonghuzhanghao,callback:function(i){e.$set(e.ruleForm,"yonghuzhanghao",i)},expression:"ruleForm.yonghuzhanghao"}})],1),n("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[n("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("姓名")]),n("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.xingming,placeholder:"姓名",type:"text"},model:{value:e.ruleForm.xingming,callback:function(i){e.$set(e.ruleForm,"xingming",i)},expression:"ruleForm.xingming"}})],1),n("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[n("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("问诊内容")]),n("v-uni-textarea",{style:{border:"0",padding:"24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"300rpx"},attrs:{placeholder:"问诊内容"},model:{value:e.ruleForm.wenzhenneirong,callback:function(i){e.$set(e.ruleForm,"wenzhenneirong",i)},expression:"ruleForm.wenzhenneirong"}})],1),n("v-uni-view",{staticClass:"btn",style:{width:"100%",alignItems:"center",justifyContent:"center",display:"flex",height:"auto"}},[n("v-uni-button",{staticClass:"bg-red",style:{border:"0",padding:"0px",margin:"0",color:"rgb(255, 255, 255)",background:"#D4CF5D",width:"48%",lineHeight:"80rpx",fontSize:"28rpx",height:"80rpx"},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.onSubmitTap.apply(void 0,arguments)}}},[e._v("提交")])],1)],1),n("w-picker",{ref:"wenzhenshijian",attrs:{mode:"dateTime",step:"1",current:!1,hasSecond:!1,themeColor:"#333333"},on:{confirm:function(i){arguments[0]=i=e.$handleEvent(i),e.wenzhenshijianConfirm.apply(void 0,arguments)}}})],1)],1)},a=[];n.d(i,"b",(function(){return r})),n.d(i,"c",(function(){return a})),n.d(i,"a",(function(){return t}))},d138:function(e,i,n){"use strict";var t=n("4ea4");Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,n("a481"),n("f559"),n("ac6a"),n("c5f6"),n("28a5"),n("96cf");var r=t(n("3b8d")),a=t(n("e2b1")),o=t(n("064f")),s=t(n("bd56")),u={data:function(){return{cross:"",ruleForm:{wenzhenneirong:"",zhaopian:"",wenzhenshijian:"",huifuzhuangtai:"待回复",yishenggonghao:"",yishengxingming:"",yonghuzhanghao:"",xingming:""},huifuzhuangtaiOptions:[],huifuzhuangtaiIndex:0,user:{},ro:{wenzhenneirong:!1,zhaopian:!1,wenzhenshijian:!1,huifuzhuangtai:!1,yishenggonghao:!1,yishengxingming:!1,yonghuzhanghao:!1,xingming:!1}}},components:{wPicker:a.default,xiaEditor:o.default,multipleSelect:s.default},computed:{baseUrl:function(){return this.$base.url}},onLoad:function(){var e=(0,r.default)(regeneratorRuntime.mark((function e(i){var n,t,r,a,o=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.ruleForm.wenzhenshijian=this.$utils.getCurDateTime(),n=uni.getStorageSync("nowTable"),e.next=4,this.$api.session(n);case 4:if(t=e.sent,this.user=t.data,this.ruleForm.yonghuzhanghao=this.user.yonghuzhanghao,this.ro.yonghuzhanghao=!0,this.ruleForm.xingming=this.user.xingming,this.ro.xingming=!0,this.ro.huifuzhuangtai=!0,this.huifuzhuangtaiOptions="已回复,待回复".split(","),this.ruleForm.userid=uni.getStorageSync("appUserid"),i.refid&&(this.ruleForm.refid=Number(i.refid),this.ruleForm.nickname=uni.getStorageSync("nickname")),!i.id){e.next=20;break}return this.ruleForm.id=i.id,e.next=18,this.$api.info("zaixianwenzhen",this.ruleForm.id);case 18:t=e.sent,this.ruleForm=t.data;case 20:if(this.cross=i.cross,!i.cross){e.next=60;break}r=uni.getStorageSync("crossObj"),e.t0=regeneratorRuntime.keys(r);case 24:if((e.t1=e.t0()).done){e.next=60;break}if(a=e.t1.value,"wenzhenneirong"!=a){e.next=30;break}return this.ruleForm.wenzhenneirong=r[a],this.ro.wenzhenneirong=!0,e.abrupt("continue",24);case 30:if("zhaopian"!=a){e.next=34;break}return this.ruleForm.zhaopian=r[a].split(",")[0],this.ro.zhaopian=!0,e.abrupt("continue",24);case 34:if("wenzhenshijian"!=a){e.next=38;break}return this.ruleForm.wenzhenshijian=r[a],this.ro.wenzhenshijian=!0,e.abrupt("continue",24);case 38:if("huifuzhuangtai"!=a){e.next=42;break}return this.ruleForm.huifuzhuangtai=r[a],this.ro.huifuzhuangtai=!0,e.abrupt("continue",24);case 42:if("yishenggonghao"!=a){e.next=46;break}return this.ruleForm.yishenggonghao=r[a],this.ro.yishenggonghao=!0,e.abrupt("continue",24);case 46:if("yishengxingming"!=a){e.next=50;break}return this.ruleForm.yishengxingming=r[a],this.ro.yishengxingming=!0,e.abrupt("continue",24);case 50:if("yonghuzhanghao"!=a){e.next=54;break}return this.ruleForm.yonghuzhanghao=r[a],this.ro.yonghuzhanghao=!0,e.abrupt("continue",24);case 54:if("xingming"!=a){e.next=58;break}return this.ruleForm.xingming=r[a],this.ro.xingming=!0,e.abrupt("continue",24);case 58:e.next=24;break;case 60:this.styleChange(),this.$forceUpdate(),uni.getStorageSync("raffleType")&&null!=uni.getStorageSync("raffleType")&&(uni.removeStorageSync("raffleType"),setTimeout((function(){o.onSubmitTap()}),300));case 63:case"end":return e.stop()}}),e,this)})));function i(i){return e.apply(this,arguments)}return i}(),methods:{styleChange:function(){this.$nextTick((function(){}))},wenzhenshijianConfirm:function(e){console.log(e),this.ruleForm.wenzhenshijian=e.result,this.$forceUpdate()},huifuzhuangtaiChange:function(e){this.huifuzhuangtaiIndex=e.target.value,this.ruleForm.huifuzhuangtai=this.huifuzhuangtaiOptions[this.huifuzhuangtaiIndex]},zhaopianTap:function(){var e=this;this.$api.upload((function(i){e.ruleForm.zhaopian="upload/"+i.file,e.$forceUpdate(),e.$nextTick((function(){e.styleChange()}))}))},getUUID:function(){return(new Date).getTime()},onSubmitTap:function(){var e=(0,r.default)(regeneratorRuntime.mark((function e(){var i,n,t,r,a,o,s,u,h,l;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!this.cross){e.next=17;break}if(uni.setStorageSync("crossCleanType",!0),a=uni.getStorageSync("statusColumnName"),o=uni.getStorageSync("statusColumnValue"),""==a){e.next=17;break}if(i||(i=uni.getStorageSync("crossObj")),a.startsWith("[")){e.next=13;break}for(s in i)s==a&&(i[s]=o);return u=uni.getStorageSync("crossTable"),e.next=11,this.$api.update("".concat(u),i);case 11:e.next=17;break;case 13:n=Number(uni.getStorageSync("appUserid")),t=i["id"],r=uni.getStorageSync("statusColumnName"),r=r.replace(/\[/,"").replace(/\]/,"");case 17:if(!t||!n){e.next=40;break}return this.ruleForm.crossuserid=n,this.ruleForm.crossrefid=t,h={page:1,limit:10,crossuserid:n,crossrefid:t},e.next=23,this.$api.list("zaixianwenzhen",h);case 23:if(l=e.sent,!(l.data.total>=r)){e.next=30;break}return this.$utils.msg(uni.getStorageSync("tips")),uni.removeStorageSync("crossCleanType"),e.abrupt("return",!1);case 30:if(!this.ruleForm.id){e.next=35;break}return e.next=33,this.$api.update("zaixianwenzhen",this.ruleForm);case 33:e.next=37;break;case 35:return e.next=37,this.$api.add("zaixianwenzhen",this.ruleForm);case 37:this.$utils.msgBack("提交成功");case 38:e.next=48;break;case 40:if(!this.ruleForm.id){e.next=45;break}return e.next=43,this.$api.update("zaixianwenzhen",this.ruleForm);case 43:e.next=47;break;case 45:return e.next=47,this.$api.add("zaixianwenzhen",this.ruleForm);case 47:this.$utils.msgBack("提交成功");case 48:case"end":return e.stop()}}),e,this)})));function i(){return e.apply(this,arguments)}return i}(),optionsChange:function(e){this.index=e.target.value},bindDateChange:function(e){this.date=e.target.value},getDate:function(e){var i=new Date,n=i.getFullYear(),t=i.getMonth()+1,r=i.getDate();return"start"===e?n-=60:"end"===e&&(n+=2),t=t>9?t:"0"+t,r=r>9?r:"0"+r,"".concat(n,"-").concat(t,"-").concat(r)},toggleTab:function(e){if(this.ro[e])return!1;this.$refs[e].show()}}};i.default=u},e2cf:function(e,i,n){"use strict";n.r(i);var t=n("d138"),r=n.n(t);for(var a in t)"default"!==a&&function(e){n.d(i,e,(function(){return t[e]}))}(a);i["default"]=r.a}}]);