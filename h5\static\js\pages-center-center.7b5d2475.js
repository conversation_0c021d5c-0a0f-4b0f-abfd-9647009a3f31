(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-center-center"],{1160:function(e,t,i){"use strict";var n={"uni-popup":i("1c89").default},r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"content"},[i("v-uni-view",{style:{padding:"240rpx 30rpx 120rpx",backgroundColor:"#DFD8CC",backgroundImage:"url(http://codegen.caihongy.cn/20231129/75a2ef6b312c4b459d7ae49148603572.png)",width:"100%",backgroundSize:"100% 400rpx",position:"relative",backgroundRepeat:"no-repeat",height:"auto"}},[e.user&&e.user.id?i("v-uni-view",{staticClass:"header",class:{status:e.isH5Plus},style:{padding:"0 24rpx",margin:"0 0 20rpx 0",borderRadius:"20rpx",background:"#FFFFFF",display:"flex",width:"100%",position:"relative",height:"320rpx"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onPageTap("../user-info/user-info")}}},["yonghu"==e.tableName?i("v-uni-view",{staticClass:"userinfo",style:{alignContent:"center",alignItems:"center",flex:"1",display:"flex",height:"100%"}},[i("v-uni-image",{style:{width:"100rpx",padding:"0",margin:"0 20rpx 0 0",borderRadius:"100%",height:"100rpx"},attrs:{src:e.user.touxiang?e.baseUrl+e.user.touxiang:"/static/gen/upload.png"}}),i("v-uni-view",{staticClass:"info",style:{width:"300rpx",margin:"0 20rpx 0 0",flex:"1",flexDirection:"column",justifyContent:"center",display:"flex"}},[i("v-uni-view",{style:{width:"100%",lineHeight:"36rpx",fontSize:"24rpx",color:"#000"}},[e._v(e._s(e.user.yonghuzhanghao)),e.user.vip&&"是"==e.user.vip?i("v-uni-text",[e._v("(VIP)")]):e._e()],1)],1),i("v-uni-view",{staticClass:"info",style:{padding:"20rpx",borderRadius:"20rpx 20rpx 0 0",left:"20rpx",bottom:"0",background:"#D4CF5D",display:"flex",width:"calc(100% - 40rpx)",position:"absolute",justifyContent:"space-around"}},[i("v-uni-view",{style:{width:"auto",lineHeight:"36rpx",fontSize:"24rpx",color:"#FFFFFF"}},[e._v("手机："+e._s(e.user.shouji))]),e.user.money?i("v-uni-view",{style:{width:"auto",lineHeight:"36rpx",fontSize:"24rpx",color:"#FFFFFF"}},[e._v("余额："+e._s(e.user.money))]):e._e(),e.user.jf||0==e.user.jf?i("v-uni-view",{style:{width:"auto",lineHeight:"36rpx",fontSize:"24rpx",color:"#FFFFFF"}},[e._v("积分："+e._s(e.user.jf))]):e._e()],1)],1):e._e(),"yisheng"==e.tableName?i("v-uni-view",{staticClass:"userinfo",style:{alignContent:"center",alignItems:"center",flex:"1",display:"flex",height:"100%"}},[i("v-uni-view",{staticClass:"info",style:{width:"300rpx",margin:"0 20rpx 0 0",flex:"1",flexDirection:"column",justifyContent:"center",display:"flex"}},[i("v-uni-view",{style:{width:"100%",lineHeight:"36rpx",fontSize:"24rpx",color:"#000"}},[e._v(e._s(e.user.yishenggonghao)),e.user.vip&&"是"==e.user.vip?i("v-uni-text",[e._v("(VIP)")]):e._e()],1)],1),i("v-uni-view",{staticClass:"info",style:{padding:"20rpx",borderRadius:"20rpx 20rpx 0 0",left:"20rpx",bottom:"0",background:"#D4CF5D",display:"flex",width:"calc(100% - 40rpx)",position:"absolute",justifyContent:"space-around"}},[i("v-uni-view",{style:{width:"auto",lineHeight:"36rpx",fontSize:"24rpx",color:"#FFFFFF"}},[e._v("联系方式："+e._s(e.user.lianxifangshi))]),e.user.money?i("v-uni-view",{style:{width:"auto",lineHeight:"36rpx",fontSize:"24rpx",color:"#FFFFFF"}},[e._v("余额："+e._s(e.user.money))]):e._e(),e.user.jf||0==e.user.jf?i("v-uni-view",{style:{width:"auto",lineHeight:"36rpx",fontSize:"24rpx",color:"#FFFFFF"}},[e._v("积分："+e._s(e.user.jf))]):e._e()],1)],1):e._e(),i("v-uni-view",{staticClass:"setting",style:{alignItems:"center",top:"10rpx",display:"flex",width:"100rpx",position:"absolute",right:"20rpx",justifyContent:"center",height:"40rpx"}},[i("v-uni-text",{staticClass:"icon iconfont icon-qita6",style:{border:"0",width:"30rpx",lineHeight:"30rpx",fontSize:"30rpx",color:"#000",borderRadius:"0"}}),i("v-uni-text",{style:{color:"#000",lineHeight:"2",fontSize:"24rpx"}},[e._v("设置")])],1)],1):i("v-uni-view",{staticClass:"header",class:{status:e.isH5Plus},style:{padding:"0 24rpx",margin:"0 0 20rpx 0",borderRadius:"20rpx",background:"#FFFFFF",display:"flex",width:"100%",position:"relative",height:"320rpx"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.loginClick.apply(void 0,arguments)}}},[i("v-uni-view",{style:{alignItems:"center",color:"#666",display:"flex",width:"100%",fontSize:"36rpx",justifyContent:"center",height:"100%"}},[e._v("登录/注册")])],1),i("v-uni-view",{staticClass:"list",style:{width:"100%",height:"auto"}},[e.user&&e.user.id?i("v-uni-view",{style:{width:"100%",margin:"0 0 20rpx 0",overflow:"hidden",borderRadius:"10rpx",height:"auto"}},[i("v-uni-view",{style:{width:"100%",padding:"10rpx 20rpx",background:"#D4CF5D",justifyContent:"space-between",display:"flex",height:"auto"}},[i("v-uni-view",{style:{color:"#fff",fontSize:"28rpx",lineHeight:"72rpx"}},[e._v("我的订单")]),i("v-uni-view",{style:{color:"#fff",fontSize:"28rpx",lineHeight:"72rpx"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onPageTap("../shop-order/shop-order")}}},[e._v("查看全部")])],1),i("v-uni-view",{style:{width:"100%",padding:"20rpx 0",background:"#ffffff",display:"flex",height:"auto"}},[i("v-uni-view",{staticClass:"list-item",style:{alignItems:"center",flex:"1",flexDirection:"column",display:"flex",height:"auto"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onPageTap("../shop-order/shop-order?nav=未支付")}}},[i("span",{staticClass:"icon iconfont icon-menu02",style:{color:"#333",lineHeight:"1",fontSize:"36rpx"}}),i("v-uni-view",{staticClass:"title",style:{color:"#333",lineHeight:"2",fontSize:"24rpx"}},[e._v("未支付")])],1),i("v-uni-view",{staticClass:"list-item",style:{alignItems:"center",flex:"1",flexDirection:"column",display:"flex",height:"auto"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onPageTap("../shop-order/shop-order?nav=已支付")}}},[i("span",{staticClass:"icon iconfont icon-menu03",style:{color:"#333",lineHeight:"1",fontSize:"36rpx"}}),i("v-uni-view",{staticClass:"title",style:{color:"#333",lineHeight:"2",fontSize:"24rpx"}},[e._v("已支付")])],1),i("v-uni-view",{staticClass:"list-item",style:{alignItems:"center",flex:"1",flexDirection:"column",display:"flex",height:"auto"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onPageTap("../shop-order/shop-order?nav=已完成")}}},[i("span",{staticClass:"icon iconfont icon-menu08",style:{color:"#333",lineHeight:"1",fontSize:"36rpx"}}),i("v-uni-view",{staticClass:"title",style:{color:"#333",lineHeight:"2",fontSize:"24rpx"}},[e._v("已完成")])],1),i("v-uni-view",{staticClass:"list-item",style:{alignItems:"center",flex:"1",flexDirection:"column",display:"flex",height:"auto"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onPageTap("../shop-order/shop-order?nav=已取消")}}},[i("span",{staticClass:"icon iconfont icon-menu09",style:{color:"#333",lineHeight:"1",fontSize:"36rpx"}}),i("v-uni-view",{staticClass:"title",style:{color:"#333",lineHeight:"2",fontSize:"24rpx"}},[e._v("已取消")])],1),i("v-uni-view",{staticClass:"list-item",style:{alignItems:"center",flex:"1",flexDirection:"column",display:"flex",height:"auto"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onPageTap("../shop-order/shop-order?nav=已退款")}}},[i("span",{staticClass:"icon iconfont icon-menu12",style:{color:"#333",lineHeight:"1",fontSize:"36rpx"}}),i("v-uni-view",{staticClass:"title",style:{color:"#333",lineHeight:"2",fontSize:"24rpx"}},[e._v("已退款")])],1)],1)],1):e._e(),i("v-uni-view",{style:{width:"100%",overflow:"hidden",borderRadius:"10rpx",height:"auto"}},[i("v-uni-view",{style:{width:"100%",padding:"10rpx 20rpx",background:"#D4CF5D",justifyContent:"space-between",display:"flex",height:"auto"}},[i("v-uni-view",{style:{color:"#fff",fontSize:"28rpx",lineHeight:"72rpx"}},[e._v("我的服务")])],1),i("v-uni-view",{style:{width:"100%",padding:"0 24rpx",flexWrap:"wrap",background:"#ffffff",display:"flex",height:"auto"}},[e._l(e.menuList,(function(t){return[e._l(t.backMenu,(function(n,r){return e.role==t.roleName?[e._l(n.child,(function(t,n){return["orders/已发货"!=t.tableName&&"orders/已退款"!=t.tableName&&"orders/已取消"!=t.tableName&&"orders/未支付"!=t.tableName&&"orders/已支付"!=t.tableName&&"orders/已完成"!=t.tableName&&"exampaper"!=t.tableName&&"examquestion"!=t.tableName?i("v-uni-view",{key:t.tableName+"_0",staticClass:"li",style:{borderColor:"#ccc",margin:"10rpx 1%",alignItems:"center",borderWidth:"0",flexDirection:"column",display:"flex",width:"23%",borderStyle:"solid",justifyContent:"center",height:"160rpx"},attrs:{"hover-class":"hover"},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.onPageTap("../"+t.tableName+"/list?userid="+e.user.id+"&menuJump="+t.menuJump)}}},[i("v-uni-view",{class:t.appFrontIcon,style:{color:"#d4cf5d",fontSize:"60rpx"}}),i("v-uni-view",{staticClass:"text",style:{width:"100%",padding:"0",lineHeight:"88rpx",fontSize:"24rpx",color:"#9E9E9E",textAlign:"center"}},[e._v(e._s(t.menu))]),i("v-uni-view",{staticClass:"icon iconfont ",style:{width:"28rpx",lineHeight:"28rpx",fontSize:"28rpx",color:"#999"}})],1):e._e()]}))]:e._e()}))]})),e.user&&e.user.id?i("v-uni-view",{staticClass:"li",style:{borderColor:"#ccc",margin:"10rpx 1%",alignItems:"center",borderWidth:"0",flexDirection:"column",display:"flex",width:"23%",borderStyle:"solid",justifyContent:"center",height:"160rpx"},attrs:{"hover-class":"hover"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onPageTap("../shop-recharge/pay-confirm")}}},[i("v-uni-view",{staticClass:"cuIcon-moneybag",style:{color:"#d4cf5d",fontSize:"60rpx"}}),i("v-uni-view",{staticClass:"text",style:{width:"100%",padding:"0",lineHeight:"88rpx",fontSize:"24rpx",color:"#9E9E9E",textAlign:"center"}},[e._v("用户充值")]),i("v-uni-view",{staticClass:"icon iconfont ",style:{width:"28rpx",lineHeight:"28rpx",fontSize:"28rpx",color:"#999"}})],1):e._e(),e.user&&e.user.id?i("v-uni-view",{staticClass:"li",style:{borderColor:"#ccc",margin:"10rpx 1%",alignItems:"center",borderWidth:"0",flexDirection:"column",display:"flex",width:"23%",borderStyle:"solid",justifyContent:"center",height:"160rpx"},attrs:{"hover-class":"hover"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.passwordShow()}}},[i("v-uni-view",{staticClass:"cuIcon-lock",style:{color:"#d4cf5d",fontSize:"60rpx"}}),i("v-uni-view",{staticClass:"text",style:{width:"100%",padding:"0",lineHeight:"88rpx",fontSize:"24rpx",color:"#9E9E9E",textAlign:"center"}},[e._v("修改密码")]),i("v-uni-view",{staticClass:"icon iconfont ",style:{width:"28rpx",lineHeight:"28rpx",fontSize:"28rpx",color:"#999"}})],1):e._e()],2)],1)],1),i("v-uni-view",{staticStyle:{width:"100%",height:"80px"}})],1),i("uni-popup",{ref:"passwordPopup",staticClass:"popup-content",attrs:{type:"bottom"}},[i("v-uni-view",{staticClass:"passwordForm"},[i("v-uni-view",{staticClass:"passwordInput"},[i("v-uni-input",{attrs:{type:"password",placeholder:"原密码"},model:{value:e.passwordForm.mima,callback:function(t){e.$set(e.passwordForm,"mima",t)},expression:"passwordForm.mima"}})],1),i("v-uni-view",{staticClass:"passwordInput"},[i("v-uni-input",{attrs:{type:"password",placeholder:"新密码"},model:{value:e.passwordForm.newmima,callback:function(t){e.$set(e.passwordForm,"newmima",t)},expression:"passwordForm.newmima"}})],1),i("v-uni-view",{staticClass:"passwordInput"},[i("v-uni-input",{attrs:{type:"password",placeholder:"确认密码"},model:{value:e.passwordForm.newmima1,callback:function(t){e.$set(e.passwordForm,"newmima1",t)},expression:"passwordForm.newmima1"}})],1),i("v-uni-view",{staticClass:"passwordBtnView"},[i("v-uni-button",{staticClass:"passwordBtn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.updatePassword.apply(void 0,arguments)}}},[e._v("更新")])],1)],1)],1)],1)},s=[];i.d(t,"b",(function(){return r})),i.d(t,"c",(function(){return s})),i.d(t,"a",(function(){return n}))},"27ae":function(e,t,i){"use strict";i.r(t);var n=i("55b4"),r=i.n(n);for(var s in n)"default"!==s&&function(e){i.d(t,e,(function(){return n[e]}))}(s);t["default"]=r.a},"55b4":function(e,t,i){"use strict";var n=i("4ea4");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("c5f6"),i("96cf");var r=n(i("3b8d")),s=n(i("2971")),o={data:function(){return{isH5Plus:!0,user:{},tableName:"",role:"",menuList:[],iconArr:["cuIcon-same","cuIcon-deliver","cuIcon-evaluate","cuIcon-shop","cuIcon-ticket","cuIcon-cascades","cuIcon-discover","cuIcon-question","cuIcon-pic","cuIcon-filter","cuIcon-footprint","cuIcon-pulldown","cuIcon-pullup","cuIcon-moreandroid","cuIcon-refund","cuIcon-qrcode","cuIcon-remind","cuIcon-profile","cuIcon-home","cuIcon-message","cuIcon-link","cuIcon-lock","cuIcon-unlock","cuIcon-vip","cuIcon-weibo","cuIcon-activity","cuIcon-friendadd","cuIcon-friendfamous","cuIcon-friend","cuIcon-goods","cuIcon-selection"],passwordForm:{mima:"",newmima:"",newmima1:""}}},computed:{baseUrl:function(){return this.$base.url}},onLoad:function(){var e=(0,r.default)(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t=s.default.list(),this.menuList=t;case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),onShow:function(){var e=(0,r.default)(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return uni.removeStorageSync("useridTag"),this.role=uni.getStorageSync("appRole"),e.next=4,this.getSession();case 4:this.getThumbsup(),this.getStoreup(),this.tableName=uni.getStorageSync("nowTable"),t=s.default.list(),this.menuList=t,this.mypic=uni.getStorageSync("headportrait"),this.$forceUpdate();case 11:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),methods:{getSession:function(){var e=(0,r.default)(regeneratorRuntime.mark((function e(){var t,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=uni.getStorageSync("nowTable"),e.next=3,this.$api.session(t);case 3:i=e.sent,this.user=i.data,this.$forceUpdate();case 6:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),getThumbsup:function(){var e=(0,r.default)(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.user&&this.user.id){e.next=2;break}return e.abrupt("return",!1);case 2:return e.next=4,this.$api.page("storeup",{userid:this.user.id,type:21});case 4:t=e.sent,this.user.thumbsnum=Number(t.data.total),this.$forceUpdate();case 7:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),getStoreup:function(){var e=(0,r.default)(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.user&&this.user.id){e.next=2;break}return e.abrupt("return",!1);case 2:return e.next=4,this.$api.page("storeup",{userid:this.user.id,type:1});case 4:t=e.sent,this.user.storenum=Number(t.data.total),this.$forceUpdate();case 7:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),loginClick:function(){uni.navigateTo({url:"../login/login"})},onPageTap:function(e){uni.setStorageSync("useridTag",1),uni.navigateTo({url:e,fail:function(){uni.switchTab({url:e})}})},recommendDetail:function(e){uni.navigateTo({url:"../".concat(this.recommendTable,"/detail?id=").concat(e),fail:function(){uni.switchTab({url:"../".concat(this.recommendTable,"/detail?id=").concat(e)})}})},passwordShow:function(){this.passwordForm={mima:"",newmima:"",newmima1:""},this.$forceUpdate(),this.$refs.passwordPopup.open()},updatePassword:function(){var e=(0,r.default)(regeneratorRuntime.mark((function e(){var t,i,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(""!=this.passwordForm.mima){e.next=3;break}return this.$utils.msg("原密码不能为空"),e.abrupt("return",!1);case 3:if(""!=this.passwordForm.newmima){e.next=6;break}return this.$utils.msg("新密码不能为空"),e.abrupt("return",!1);case 6:if(""!=this.passwordForm.newmima1){e.next=9;break}return this.$utils.msg("确认密码不能为空"),e.abrupt("return",!1);case 9:return t="",this.user.mima?t=this.user.mima:this.user.password&&(t=this.user.password),i=this.passwordForm.mima,e.next=14,this.$api.encrypt("md5",this.passwordForm.mima);case 14:if(n=e.sent,i=n.data,t==i){e.next=19;break}return this.$utils.msg("原密码不正确"),e.abrupt("return",!1);case 19:if(this.passwordForm.newmima==this.passwordForm.newmima1){e.next=22;break}return this.$utils.msg("两次密码不一致"),e.abrupt("return",!1);case 22:if(this.passwordForm.mima!=this.passwordForm.newmima){e.next=25;break}return this.$utils.msg("新密码与原密码相同！"),e.abrupt("return",!1);case 25:return this.tableName,this.user.password=this.passwordForm.newmima,this.user.mima=this.passwordForm.newmima,e.next=30,this.$api.update(this.tableName,this.user);case 30:this.$utils.msg("修改密码成功,下次登录系统生效"),this.$refs.passwordPopup.close(),this.getSession();case 33:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}};t.default=o},cc1a:function(e,t,i){var n=i("f268");"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var r=i("4f06").default;r("fe7367ae",n,!0,{sourceMap:!1,shadowMode:!1})},d2c0:function(e,t,i){"use strict";i.r(t);var n=i("1160"),r=i("27ae");for(var s in r)"default"!==s&&function(e){i.d(t,e,(function(){return r[e]}))}(s);i("d8f6");var o,a=i("f0c5"),u=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"3d8eafb6",null,!1,n["a"],o);t["default"]=u.exports},d8f6:function(e,t,i){"use strict";var n=i("cc1a"),r=i.n(n);r.a},f268:function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.content[data-v-3d8eafb6]{height:calc(100vh - 94px);box-sizing:border-box}.passwordForm[data-v-3d8eafb6]{width:100%;padding:10px 20px 60px;background:#fff}.passwordForm .passwordInput[data-v-3d8eafb6]{background:#eee;margin:0 0 10px;padding:0 10px}.passwordForm .passwordInput uni-input[data-v-3d8eafb6]{height:36px;line-height:36px;font-size:14px}.passwordForm .passwordBtnView[data-v-3d8eafb6]{width:100%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;padding:10px 0}.passwordForm .passwordBtnView .passwordBtn[data-v-3d8eafb6]{padding:0 20px;width:auto;height:20px;line-height:20px;font-size:14px;color:#fff;background:#007aff;border:none;border-radius:5px}',""]),e.exports=t}}]);