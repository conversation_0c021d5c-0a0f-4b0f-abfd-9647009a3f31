{"_from": "@dcloudio/uni-ui", "_id": "@dcloudio/uni-ui@1.0.8", "_inBundle": false, "_integrity": "sha1-tM2ERwDzrqSxLvtvSLcG930n2H0=", "_location": "/@dcloudio/uni-ui", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "@dcloudio/uni-ui", "name": "@dcloudio/uni-ui", "escapedName": "@dcloudio%2funi-ui", "scope": "@dcloudio", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npm.taobao.org/@dcloudio/uni-ui/download/@dcloudio/uni-ui-1.0.8.tgz", "_shasum": "b4cd844700f3aea4b12efb6f48b706f77d27d87d", "_spec": "@dcloudio/uni-ui", "_where": "F:\\充电\\工作\\2019\\11\\food", "author": "", "browserslist": ["Android >= 4.4", "ios >= 8"], "bundleDependencies": false, "deprecated": false, "description": "当前 `uni-ui` 版本可以在 `nvue` 页面中使用，因为 `nvue` 的特性，故 `uni-ui` 大部分组件样式有较大改动，您如果觉得最新的 `nvue` 版本有兼容问题，可以使用 [vue](https://github.com/dcloudio/uni-ui/tree/last-vue)  版本", "devDependencies": {"@dcloudio/uni-app-plus": "^2.0.0-alpha-22420190823018", "@dcloudio/uni-cli-shared": "^2.0.0-alpha-22420190823018", "@dcloudio/uni-h5": "^2.0.0-alpha-22420190823018", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-mp-alipay": "^2.0.0-alpha-22420190823018", "@dcloudio/uni-mp-baidu": "^2.0.0-alpha-22420190823018", "@dcloudio/uni-mp-qq": "^2.0.0-alpha-22420190823018", "@dcloudio/uni-mp-toutiao": "^2.0.0-alpha-22420190823018", "@dcloudio/uni-mp-weixin": "^2.0.0-alpha-22420190823018", "@dcloudio/uni-stat": "^2.0.0-alpha-22420190823018", "@dcloudio/uni-template-compiler": "^2.0.0-alpha-22420190823018", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.0-alpha-22420190823018", "@dcloudio/vue-cli-plugin-uni": "^2.0.0-alpha-22420190823018", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.0-alpha-22420190823018", "@dcloudio/webpack-uni-mp-loader": "^2.0.0-alpha-22420190823018", "@dcloudio/webpack-uni-pages-loader": "^2.0.0-alpha-22420190823018", "@types/html5plus": "*", "@types/uni-app": "*", "@vue/cli-plugin-babel": "3.5.1", "@vue/cli-service": "^3.11.0", "babel-plugin-import": "^1.11.0", "flyio": "^0.6.2", "mini-types": "*", "miniprogram-api-typings": "^2.8.0-2", "node-sass": "^4.12.0", "postcss-comment": "^2.0.0", "regenerator-runtime": "^0.12.1", "sass-loader": "^7.3.1", "vue": "^2.6.10", "vue-template-compiler": "^2.6.10", "vuex": "^3.0.1"}, "license": "Apache-2.0", "name": "@dcloudio/uni-ui", "scripts": {"build": "npm run build:h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:lib": "node build/build-lib.js", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "serve": "npm run dev:h5"}, "uni-app": {"scripts": {}}, "version": "1.0.8"}