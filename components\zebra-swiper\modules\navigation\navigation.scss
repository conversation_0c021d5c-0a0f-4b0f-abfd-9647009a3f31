.swiper-button-prev,
.swiper-button-next {
  position: absolute;
  top: 50%;
  // width: calc(80rpx / 44 * 27);
  height: 80rpx;
  margin-top: calc(0rpx - (80rpx / 2));
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #007aff;
  font-size: 50rpx;
  &.swiper-button-disabled {
    opacity: 0.35;
    cursor: auto;
    pointer-events: none;
  }
  &:after {
    font-family: swiper-icons;
    font-size: 80rpx;
    text-transform: none !important;
    letter-spacing: 0;
    text-transform: none;
    font-variant: initial;
    line-height: 1;
  }
}
.swiper-button-prev,
.swiper-rtl .swiper-button-next {
  // &:after {
  //   content: 'prev';
  // }
  left: 10px;
  right: auto;
}
.swiper-button-next,
.swiper-rtl .swiper-button-prev {
  // &:after {
  //   content: 'next';
  // }
  right: 10px;
  left: auto;
}

.swiper-button-lock {
  display: none;
}
