(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-zhangqichufang-add-or-update"],{2780:function(e,n,t){"use strict";var i=t("4ea4");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,t("a481"),t("f559"),t("ac6a"),t("28a5"),t("c5f6"),t("96cf");var r=i(t("3b8d")),a=i(t("e2b1")),o=i(t("064f")),s=i(t("bd56")),u={data:function(){return{cross:"",ruleForm:{chufangmingcheng:"",chufangneirong:"",chufangtupian:"",shangchuanshijian:"",yishenggonghao:"",yishengxingming:""},user:{},ro:{chufangmingcheng:!1,chufangneirong:!1,chufangtupian:!1,shang<PERSON><PERSON><PERSON><PERSON><PERSON>:!1,y<PERSON><PERSON><PERSON><PERSON>:!1,yishengxingming:!1}}},components:{wPicker:a.default,xiaEditor:o.default,multipleSelect:s.default},computed:{baseUrl:function(){return this.$base.url}},onLoad:function(){var e=(0,r.default)(regeneratorRuntime.mark((function e(n){var t,i,r,a,o=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.ruleForm.shangchuanshijian=this.$utils.getCurDateTime(),t=uni.getStorageSync("nowTable"),e.next=4,this.$api.session(t);case 4:if(i=e.sent,this.user=i.data,this.ruleForm.yishenggonghao=this.user.yishenggonghao,this.ro.yishenggonghao=!0,this.ruleForm.yishengxingming=this.user.yishengxingming,this.ro.yishengxingming=!0,this.ruleForm.userid=uni.getStorageSync("appUserid"),n.refid&&(this.ruleForm.refid=Number(n.refid),this.ruleForm.nickname=uni.getStorageSync("nickname")),!n.id){e.next=18;break}return this.ruleForm.id=n.id,e.next=16,this.$api.info("zhangqichufang",this.ruleForm.id);case 16:i=e.sent,this.ruleForm=i.data;case 18:if(this.cross=n.cross,!n.cross){e.next=50;break}r=uni.getStorageSync("crossObj"),e.t0=regeneratorRuntime.keys(r);case 22:if((e.t1=e.t0()).done){e.next=50;break}if(a=e.t1.value,"chufangmingcheng"!=a){e.next=28;break}return this.ruleForm.chufangmingcheng=r[a],this.ro.chufangmingcheng=!0,e.abrupt("continue",22);case 28:if("chufangneirong"!=a){e.next=32;break}return this.ruleForm.chufangneirong=r[a],this.ro.chufangneirong=!0,e.abrupt("continue",22);case 32:if("chufangtupian"!=a){e.next=36;break}return this.ruleForm.chufangtupian=r[a].split(",")[0],this.ro.chufangtupian=!0,e.abrupt("continue",22);case 36:if("shangchuanshijian"!=a){e.next=40;break}return this.ruleForm.shangchuanshijian=r[a],this.ro.shangchuanshijian=!0,e.abrupt("continue",22);case 40:if("yishenggonghao"!=a){e.next=44;break}return this.ruleForm.yishenggonghao=r[a],this.ro.yishenggonghao=!0,e.abrupt("continue",22);case 44:if("yishengxingming"!=a){e.next=48;break}return this.ruleForm.yishengxingming=r[a],this.ro.yishengxingming=!0,e.abrupt("continue",22);case 48:e.next=22;break;case 50:this.styleChange(),this.$forceUpdate(),uni.getStorageSync("raffleType")&&null!=uni.getStorageSync("raffleType")&&(uni.removeStorageSync("raffleType"),setTimeout((function(){o.onSubmitTap()}),300));case 53:case"end":return e.stop()}}),e,this)})));function n(n){return e.apply(this,arguments)}return n}(),methods:{styleChange:function(){this.$nextTick((function(){}))},shangchuanshijianConfirm:function(e){console.log(e),this.ruleForm.shangchuanshijian=e.result,this.$forceUpdate()},chufangtupianTap:function(){var e=this;this.$api.upload((function(n){e.ruleForm.chufangtupian="upload/"+n.file,e.$forceUpdate(),e.$nextTick((function(){e.styleChange()}))}))},getUUID:function(){return(new Date).getTime()},onSubmitTap:function(){var e=(0,r.default)(regeneratorRuntime.mark((function e(){var n,t,i,r,a,o,s,u,c,g;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!this.cross){e.next=17;break}if(uni.setStorageSync("crossCleanType",!0),a=uni.getStorageSync("statusColumnName"),o=uni.getStorageSync("statusColumnValue"),""==a){e.next=17;break}if(n||(n=uni.getStorageSync("crossObj")),a.startsWith("[")){e.next=13;break}for(s in n)s==a&&(n[s]=o);return u=uni.getStorageSync("crossTable"),e.next=11,this.$api.update("".concat(u),n);case 11:e.next=17;break;case 13:t=Number(uni.getStorageSync("appUserid")),i=n["id"],r=uni.getStorageSync("statusColumnName"),r=r.replace(/\[/,"").replace(/\]/,"");case 17:if(!i||!t){e.next=40;break}return this.ruleForm.crossuserid=t,this.ruleForm.crossrefid=i,c={page:1,limit:10,crossuserid:t,crossrefid:i},e.next=23,this.$api.list("zhangqichufang",c);case 23:if(g=e.sent,!(g.data.total>=r)){e.next=30;break}return this.$utils.msg(uni.getStorageSync("tips")),uni.removeStorageSync("crossCleanType"),e.abrupt("return",!1);case 30:if(!this.ruleForm.id){e.next=35;break}return e.next=33,this.$api.update("zhangqichufang",this.ruleForm);case 33:e.next=37;break;case 35:return e.next=37,this.$api.add("zhangqichufang",this.ruleForm);case 37:this.$utils.msgBack("提交成功");case 38:e.next=48;break;case 40:if(!this.ruleForm.id){e.next=45;break}return e.next=43,this.$api.update("zhangqichufang",this.ruleForm);case 43:e.next=47;break;case 45:return e.next=47,this.$api.add("zhangqichufang",this.ruleForm);case 47:this.$utils.msgBack("提交成功");case 48:case"end":return e.stop()}}),e,this)})));function n(){return e.apply(this,arguments)}return n}(),optionsChange:function(e){this.index=e.target.value},bindDateChange:function(e){this.date=e.target.value},getDate:function(e){var n=new Date,t=n.getFullYear(),i=n.getMonth()+1,r=n.getDate();return"start"===e?t-=60:"end"===e&&(t+=2),i=i>9?i:"0"+i,r=r>9?r:"0"+r,"".concat(t,"-").concat(i,"-").concat(r)},toggleTab:function(e){if(this.ro[e])return!1;this.$refs[e].show()}}};n.default=u},"3c30":function(e,n,t){"use strict";var i=t("7182"),r=t.n(i);r.a},7182:function(e,n,t){var i=t("c1b4");"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var r=t("4f06").default;r("ba866cc4",i,!0,{sourceMap:!1,shadowMode:!1})},"788f":function(e,n,t){"use strict";t.r(n);var i=t("ca3c"),r=t("b703");for(var a in r)"default"!==a&&function(e){t.d(n,e,(function(){return r[e]}))}(a);t("3c30");var o,s=t("f0c5"),u=Object(s["a"])(r["default"],i["b"],i["c"],!1,null,"4719c93a",null,!1,i["a"],o);n["default"]=u.exports},b703:function(e,n,t){"use strict";t.r(n);var i=t("2780"),r=t.n(i);for(var a in i)"default"!==a&&function(e){t.d(n,e,(function(){return i[e]}))}(a);n["default"]=r.a},c1b4:function(e,n,t){var i=t("24fb");n=i(!1),n.push([e.i,'@charset "UTF-8";\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.content[data-v-4719c93a]{min-height:calc(100vh - 44px);box-sizing:border-box}',""]),e.exports=n},ca3c:function(e,n,t){"use strict";var i={"w-picker":t("e2b1").default},r=function(){var e=this,n=e.$createElement,t=e._self._c||n;return t("v-uni-view",{staticClass:"content"},[t("v-uni-view",{style:{width:"100%",padding:"0",position:"relative",background:"#fff",height:"100%"}},[t("v-uni-form",{staticClass:"app-update-pv",style:{width:"100%",padding:"24rpx",background:"#fff",display:"block",height:"auto"}},[t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("处方名称")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.chufangmingcheng,placeholder:"处方名称",type:"text"},model:{value:e.ruleForm.chufangmingcheng,callback:function(n){e.$set(e.ruleForm,"chufangmingcheng",n)},expression:"ruleForm.chufangmingcheng"}})],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"},on:{click:function(n){arguments[0]=n=e.$handleEvent(n),e.chufangtupianTap.apply(void 0,arguments)}}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("处方图片")]),e.ruleForm.chufangtupian?t("v-uni-image",{staticClass:"avator",style:{width:"80rpx",borderRadius:"100%",objectFit:"cover",display:"block",height:"80rpx"},attrs:{src:e.baseUrl+e.ruleForm.chufangtupian.split(",")[0],mode:"aspectFill"}}):t("v-uni-image",{staticClass:"avator",style:{width:"80rpx",borderRadius:"100%",objectFit:"cover",display:"block",height:"80rpx"},attrs:{src:"../../static/gen/upload.png",mode:"aspectFill"}})],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("上传时间")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.shangchuanshijian,placeholder:"上传时间"},on:{click:function(n){arguments[0]=n=e.$handleEvent(n),e.toggleTab("shangchuanshijian")}},model:{value:e.ruleForm.shangchuanshijian,callback:function(n){e.$set(e.ruleForm,"shangchuanshijian",n)},expression:"ruleForm.shangchuanshijian"}})],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("医生工号")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.yishenggonghao,placeholder:"医生工号",type:"text"},model:{value:e.ruleForm.yishenggonghao,callback:function(n){e.$set(e.ruleForm,"yishenggonghao",n)},expression:"ruleForm.yishenggonghao"}})],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("医生姓名")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.yishengxingming,placeholder:"医生姓名",type:"text"},model:{value:e.ruleForm.yishengxingming,callback:function(n){e.$set(e.ruleForm,"yishengxingming",n)},expression:"ruleForm.yishengxingming"}})],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("处方内容")]),t("v-uni-textarea",{style:{border:"0",padding:"24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"300rpx"},attrs:{placeholder:"处方内容"},model:{value:e.ruleForm.chufangneirong,callback:function(n){e.$set(e.ruleForm,"chufangneirong",n)},expression:"ruleForm.chufangneirong"}})],1),t("v-uni-view",{staticClass:"btn",style:{width:"100%",alignItems:"center",justifyContent:"center",display:"flex",height:"auto"}},[t("v-uni-button",{staticClass:"bg-red",style:{border:"0",padding:"0px",margin:"0",color:"rgb(255, 255, 255)",background:"#D4CF5D",width:"48%",lineHeight:"80rpx",fontSize:"28rpx",height:"80rpx"},on:{click:function(n){arguments[0]=n=e.$handleEvent(n),e.onSubmitTap.apply(void 0,arguments)}}},[e._v("提交")])],1)],1),t("w-picker",{ref:"shangchuanshijian",attrs:{mode:"dateTime",step:"1",current:!1,hasSecond:!1,themeColor:"#333333"},on:{confirm:function(n){arguments[0]=n=e.$handleEvent(n),e.shangchuanshijianConfirm.apply(void 0,arguments)}}})],1)],1)},a=[];t.d(n,"b",(function(){return r})),t.d(n,"c",(function(){return a})),t.d(n,"a",(function(){return i}))}}]);