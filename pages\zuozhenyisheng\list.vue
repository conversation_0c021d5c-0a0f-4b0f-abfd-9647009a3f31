<template>
<!-- category 1 -->
<mescroll-uni @init="mescrollInit" :up="upOption" :down="downOption" @down="downCallback" @up="upCallback">
	<view class="content">
		<view class="search-container" :style='{"width":"100%","padding":"0 0 40rpx","position":"relative","background":"linear-gradient(135deg, #667eea 0%, #764ba2 100%)","height":"auto"}'>
			<!-- 搜索头部区域 -->
			<view class="search-header" :style='{"width":"100%","padding":"10rpx 14rpx 10rpx","background":"transparent","height":"auto"}'>
				<!-- <view class="search-title" :style='{"color":"#fff","fontSize":"32rpx","fontWeight":"600","marginBottom":"20rpx","textAlign":"center"}'>
					<text class="icon iconfont icon-yisheng" :style='{"marginRight":"12rpx","fontSize":"36rpx"}'></text>
					医生查询
				</view> -->
			</view>

			<!-- 搜索表单区域 -->
			<view class="search-form-container" :style='{"width":"100%","padding":"0 24rpx 20rpx","background":"transparent"}'>
				<!-- 查询类型选择器 -->
				<view class="query-selector" :style='{"marginBottom":"20rpx"}' v-if="queryList.length>1">
					<picker mode="selector" :range="queryList" range-key="queryName" :value="queryIndex" @change="queryChange">
						<view class="selector-wrapper" :style='{"background":"rgba(255,255,255,0.15)","borderRadius":"16rpx","padding":"16rpx 20rpx","display":"flex","alignItems":"center","justifyContent":"space-between","backdropFilter":"blur(10rpx)","border":"1rpx solid rgba(255,255,255,0.2)"}'>
							<text :style='{"color":"#fff","fontSize":"28rpx","fontWeight":"500"}'>{{queryList[queryIndex].queryName}}</text>
							<text class="icon iconfont icon-jiantou18" :style='{"color":"rgba(255,255,255,0.8)","fontSize":"24rpx","transform":"rotate(90deg)"}'></text>
						</view>
					</picker>
				</view>

				<!-- 搜索输入框 -->
				<view class="search-input-wrapper" :style='{"position":"relative","marginBottom":"20rpx"}'>
					<view class="search-input-container" v-if="queryIndex==0" :style='{"position":"relative","background":"rgba(255,255,255,0.95)","borderRadius":"16rpx","boxShadow":"0 8rpx 32rpx rgba(0,0,0,0.1)","border":"1rpx solid rgba(255,255,255,0.3)","overflow":"hidden"}'>
						<text class="icon iconfont icon-fangdajing07" :style='{"position":"absolute","left":"20rpx","top":"50%","transform":"translateY(-50%)","color":"#4a90e2","fontSize":"32rpx","zIndex":"2"}'></text>
						<input
							:style='{"padding":"20rpx 20rpx 20rpx 70rpx","color":"#333","background":"transparent","width":"100%","fontSize":"28rpx","height":"80rpx","lineHeight":"80rpx","border":"none","outline":"none"}'
							v-model="searchForm.yishengxingming"
							type="text"
							placeholder="请输入医生姓名"
							placeholder-style="color: #999; font-size: 28rpx;"
						/>
					</view>

					<view class="search-input-container" v-if="queryIndex==1" :style='{"position":"relative","background":"rgba(255,255,255,0.95)","borderRadius":"16rpx","boxShadow":"0 8rpx 32rpx rgba(0,0,0,0.1)","border":"1rpx solid rgba(255,255,255,0.3)","overflow":"hidden"}'>
						<text class="icon iconfont icon-fangdajing07" :style='{"position":"absolute","left":"20rpx","top":"50%","transform":"translateY(-50%)","color":"#4a90e2","fontSize":"32rpx","zIndex":"2"}'></text>
						<input
							:style='{"padding":"20rpx 20rpx 20rpx 70rpx","color":"#333","background":"transparent","width":"100%","fontSize":"28rpx","height":"80rpx","lineHeight":"80rpx","border":"none","outline":"none"}'
							v-model="searchForm.zhicheng"
							type="text"
							placeholder="请输入职称"
							placeholder-style="color: #999; font-size: 28rpx;"
						/>
					</view>
				</view>

				<!-- 搜索按钮 -->
				<view class="search-button-wrapper" :style='{"textAlign":"center"}'>
					<button
						class="search-btn"
						:style='{"background":"linear-gradient(135deg, #4a90e2 0%, #357abd 100%)","border":"none","borderRadius":"16rpx","color":"#fff","fontSize":"28rpx","fontWeight":"600","padding":"20rpx 60rpx","boxShadow":"0 8rpx 24rpx rgba(74, 144, 226, 0.3)","transition":"all 0.3s ease","minWidth":"200rpx","height":"80rpx","lineHeight":"40rpx"}'
						@tap="search"
					>
						<text class="icon iconfont icon-sousuo" :style='{"marginRight":"8rpx","fontSize":"24rpx"}'></text>
						开始搜索
					</button>
				</view>
			</view>

			<!-- 排序和筛选区域 -->
			<view class="filter-section" :style='{"background":"rgba(255,255,255,0.95)","margin":"0 24rpx","borderRadius":"16rpx 16rpx 0 0","boxShadow":"0 -4rpx 20rpx rgba(0,0,0,0.1)","position":"relative","zIndex":"2"}'>
				<!-- 排序选项 -->
				<view class="sort-container" :style='{"padding":"20rpx 24rpx","borderBottom":"1rpx solid #f0f0f0","display":"flex","alignItems":"center","justifyContent":"space-between"}'>
					<view class="sort-label" :style='{"color":"#666","fontSize":"26rpx","fontWeight":"500"}'>
						<text class="icon iconfont icon-paixu" :style='{"marginRight":"8rpx","color":"#4a90e2","fontSize":"28rpx"}'></text>
						排序方式
					</view>
					<view @click="sortClick('addtime')" class="sort-option" :style='{"display":"flex","alignItems":"center","padding":"8rpx 16rpx","borderRadius":"20rpx","background":listSort=="addtime"?"rgba(74, 144, 226, 0.1)":"transparent","border":"1rpx solid "+(listSort=="addtime"?"#4a90e2":"#e0e0e0"),"transition":"all 0.3s ease"}'>
						<text :style='{"color":listSort=="addtime"?"#4a90e2":"#666","fontSize":"24rpx","marginRight":"6rpx"}'>按日期</text>
						<text v-if="listSort!='addtime'" class="icon iconfont icon-shijian18" :style='{"fontSize":"20rpx","color":"#999"}'></text>
						<text v-else-if="listSort=='addtime'&&listOrder=='asc'" class="icon iconfont icon-shangsheng" :style='{"fontSize":"20rpx","color":"#4a90e2"}'></text>
						<text v-else-if="listSort=='addtime'&&listOrder=='desc'" class="icon iconfont icon-xiajiang" :style='{"fontSize":"20rpx","color":"#4a90e2"}'></text>
					</view>
				</view>

				<!-- 科室分类 -->
				<view class="category-container" :style='{"padding":"20rpx 24rpx"}'>
					<view class="category-label" :style='{"color":"#666","fontSize":"26rpx","fontWeight":"500","marginBottom":"16rpx"}'>
						<text class="icon iconfont icon-keshi" :style='{"marginRight":"8rpx","color":"#4a90e2","fontSize":"28rpx"}'></text>
						科室分类
					</view>
					<scroll-view scroll-x="true" class="category-scroll" :style='{"whiteSpace":"nowrap","width":"100%"}'>
						<view class="category-tabs" :style='{"display":"inline-flex","alignItems":"center","gap":"12rpx","paddingBottom":"8rpx"}'>
							<view
								:class='categoryName === item.keshi ? "category-tab active" : "category-tab"'
								v-for="(item,index) in categoryList"
								:key="index"
								@tap="categoryClick(item.keshi)"
								:style='{"display":"inline-block","padding":"12rpx 24rpx","borderRadius":"24rpx","fontSize":"26rpx","fontWeight":"500","transition":"all 0.3s ease","whiteSpace":"nowrap","background":categoryName === item.keshi?"linear-gradient(135deg, #4a90e2 0%, #357abd 100%)":"rgba(74, 144, 226, 0.08)","color":categoryName === item.keshi?"#fff":"#4a90e2","border":"1rpx solid "+(categoryName === item.keshi?"transparent":"rgba(74, 144, 226, 0.2)"),"boxShadow":categoryName === item.keshi?"0 4rpx 12rpx rgba(74, 144, 226, 0.3)":"none"}'
							>
								{{item.keshi}}
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
			<!-- 医生列表区域 -->
			<view class="list" :style='{"width":"100%","padding":"24rpx","margin":"0","background":"linear-gradient(180deg, rgba(255,255,255,0.95) 0%, #f8f9ff 100%)","minHeight":"calc(100vh - 400rpx)","height":"auto"}'>
				<view @tap="onDetailTap(product)" class="listm flex flex-between" :style='{"boxShadow":"0 2rpx 4rpx rgba(0,0,0,.3)","padding":"20rpx","margin":"0 0 20rpx","borderRadius":"20rpx","flexWrap":"wrap","background":"#FFFFFF","display":"flex","width":"100%","height":"auto"}' v-for="(product,index) in list" :key="index">
					<image :style='{"width":"260rpx","objectFit":"cover","borderRadius":"10rpx","display":"block","height":"200rpx"}' mode="aspectFill" class="listmpic" v-if="preHttp(product.zhaopian)" :src="product.zhaopian.split(',')[0]"></image>
					<image :style='{"width":"260rpx","objectFit":"cover","borderRadius":"10rpx","display":"block","height":"200rpx"}' mode="aspectFill" class="listmpic" v-else :src="product.zhaopian?baseUrl+product.zhaopian.split(',')[0]:''"></image>

					<view class="listmr" :style='{"width":"calc(100% - 260rpx)","padding":"0","margin":"0","flexWrap":"wrap","display":"flex","height":"auto"}'>
						<view class="col3 f30 elip mb15" :style='{"width":"100%","padding":"0 20rpx","margin":"0","lineHeight":"48rpx","fontSize":"28rpx","color":"#000000"}'>{{product.yishengxingming}}</view>
						<view :style='{"padding":"0 20rpx","order":"1"}'>
							<text class="icon iconfont icon-shijian21" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#666"}'></text>
							<text :style='{"color":"#666","lineHeight":"1.5","fontSize":"24rpx"}'>{{product.addtime}}</text>
						</view>
						<view :style='{"padding":"0 20rpx","display":"inline-block"}'>
							<text class="icon iconfont icon-shoucang10" :style='{"margin":"0 4rpx 0 0","lineHeight":"1.5","fontSize":"24rpx","color":"#666"}'></text>
							<text :style='{"color":"#666","lineHeight":"1.5","fontSize":"24rpx"}'>{{product.storeupnum}}</text>
						</view>
					</view>
					<!-- #ifdef MP-WEIXIN -->
					<view :style='{"width":"100%","padding":"8rpx 20rpx","justifyContent":"space-between","display":"flex","height":"auto"}'>
						<view :style='{"display":"flex"}' v-if="(userid && isAuth('zuozhenyisheng','修改')) || (!userid && isAuthFront('zuozhenyisheng','修改'))" @tap.stop.proevent="onUpdate" :data-row="product">
							<text :style='{"margin":"0 8rpx 0 0","fontSize":"28rpx","lineHeight":"1","color":"#666","display":"inline-block"}' class="cuIcon-edit"></text>
							<text :style='{"fontSize":"28rpx","lineHeight":"1","color":"#666","display":"inline-block"}'>修改</text>
						</view>
						<view :style='{"display":"flex"}' v-if="(userid && isAuth('zuozhenyisheng','删除')) || (!userid && isAuthFront('zuozhenyisheng','删除'))" @tap.stop.proevent="onDelete" :data-row="product">
							<text :style='{"margin":"0 8rpx 0 0","fontSize":"28rpx","lineHeight":"1","color":"#666","display":"inline-block"}' class="cuIcon-delete"></text>
							<text :style='{"fontSize":"28rpx","lineHeight":"1","color":"#666","display":"inline-block"}'>删除</text>
						</view>
					</view>
					<!-- #endif -->
					<!-- #ifndef MP-WEIXIN -->
					<view :style='{"width":"100%","padding":"8rpx 20rpx","justifyContent":"space-between","display":"flex","height":"auto"}'>
						<view :style='{"display":"flex"}' v-if="(userid && isAuth('zuozhenyisheng','修改')) || (!userid && isAuthFront('zuozhenyisheng','修改'))" @tap.stop.proevent="onUpdateTap(product)">
							<text :style='{"margin":"0 8rpx 0 0","fontSize":"28rpx","lineHeight":"1","color":"#666","display":"inline-block"}' class="cuIcon-edit"></text>
							<text :style='{"fontSize":"28rpx","lineHeight":"1","color":"#666","display":"inline-block"}'>修改</text>
						</view>
						<view :style='{"display":"flex"}' v-if="(userid && isAuth('zuozhenyisheng','删除')) || (!userid && isAuthFront('zuozhenyisheng','删除'))" @tap.stop.proevent="onDeleteTap(product.id)">
							<text :style='{"margin":"0 8rpx 0 0","fontSize":"28rpx","lineHeight":"1","color":"#666","display":"inline-block"}' class="cuIcon-delete"></text>
							<text :style='{"fontSize":"28rpx","lineHeight":"1","color":"#666","display":"inline-block"}'>删除</text>
						</view>
					</view>
					<!-- #endif -->
				</view>
			</view>
			
			
			

			

			</view>
			
			
			
		</view>

		<!-- 新增医生按钮 -->
		<button
			:style='{"border":"0","boxShadow":"0 8rpx 24rpx rgba(74, 144, 226, 0.4)","color":"#fff","bottom":"40rpx","right":"24rpx","outline":"none","borderRadius":"50%","background":"linear-gradient(135deg, #4a90e2 0%, #357abd 100%)","width":"120rpx","lineHeight":"120rpx","fontSize":"24rpx","position":"fixed","height":"120rpx","zIndex":"999","display":"flex","alignItems":"center","justifyContent":"center","transition":"all 0.3s ease"}'
			v-if="userid && isAuth('zuozhenyisheng','新增')"
			class="add-btn"
			@click="onAddTap()"
		>
			<text class="icon iconfont icon-tianjia" :style='{"fontSize":"32rpx","color":"#fff"}'></text>
		</button>
		<button
			:style='{"border":"0","boxShadow":"0 8rpx 24rpx rgba(74, 144, 226, 0.4)","color":"#fff","bottom":"40rpx","right":"24rpx","outline":"none","borderRadius":"50%","background":"linear-gradient(135deg, #4a90e2 0%, #357abd 100%)","width":"120rpx","lineHeight":"120rpx","fontSize":"24rpx","position":"fixed","height":"120rpx","zIndex":"999","display":"flex","alignItems":"center","justifyContent":"center","transition":"all 0.3s ease"}'
			v-if="!userid && isAuthFront('zuozhenyisheng','新增')"
			class="add-btn"
			@click="onAddTap()"
		>
			<text class="icon iconfont icon-tianjia" :style='{"fontSize":"32rpx","color":"#fff"}'></text>
		</button>
	</view>
</mescroll-uni>
</template>

<script>
	export default {
		data() {
			return {
				leftListNews7: [],
				rightListNews7: [],
				tempListNews7: [],
				btnColor: ['#409eff','#67c23a','#909399','#e6a23c','#f56c6c','#356c6c','#351c6c','#f093a9','#a7c23a','#104eff','#10441f','#a21233','#503319'],
				queryList:[
					{
						queryName:"医生姓名",
					},
					{
						queryName:"职称",
					},
				],
				queryIndex: 0,
				list: [],
				lists: [],
                userid: '',
				mescroll: null, //mescroll实例对象
				downOption: {
					auto: false //是否在初始化后,自动执行下拉回调callback; 默认true
				},
				upOption: {
					noMoreSize: 5, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
					textNoMore: '~ 没有更多了 ~',
				},
				hasNext: true,
				searchForm:{},
				categoryList:[],
				categoryName :'全部',
				CustomBar: '0',
				listSort: 'id',
				listOrder: 'desc',
			};
		},
		watch: {
		},
		mounted() {
		},
		computed: {
			baseUrl() {
				return this.$base.url;
			},
			copyFlowListNews7() {
				return this.cloneData(this.list);
			},
		},
		async onShow() {
            if(uni.getStorageSync("useridTag")==1){
                this.userid=uni.getStorageSync("useridTag");
                uni.removeStorageSync("useridTag");
            } else {
                this.userid = "";
            }
			this.btnColor = this.btnColor.sort(()=> {
				return (0.5-Math.random());
			});
            let res = {};
            if(this.userid) {
                res = await this.$api.page('keshi', {page:1,limit:100});
            } else {
                res = await this.$api.list('keshi', {page:1,limit:100});
            }
			res.data.list.splice(0,0,{id:0,keshi:'全部'})
			this.categoryList = res.data.list;
			this.hasNext = true
			// 重新加载数据
			if (this.mescroll) this.mescroll.resetUpScroll()
		},
		onLoad(options) {
            if(uni.getStorageSync("useridTag")==1){
                this.userid=uni.getStorageSync("useridTag");
                uni.removeStorageSync("useridTag");
            } else {
                this.userid = "";
            }
			this.hasNext = true
			// 重新加载数据
			if (this.mescroll) this.mescroll.resetUpScroll()
		},
		components: {
		},
		methods: {
			uGetRect(selector, all) {
				return new Promise(resolve => {
					uni.createSelectorQuery()
					.in(this)
					[all ? 'selectAll' : 'select'](selector)
					.boundingClientRect(rect => {
						if (all && Array.isArray(rect) && rect.length) {
							resolve(rect);
						}
						if (!all && rect) {
							resolve(rect);
						}
					})
					.exec();
				});
			},
			cloneData(data) {
				return JSON.parse(JSON.stringify(data));
			},
			sortClick(type){
				if(this.listSort==type){
					if(this.listOrder == 'desc'){
						this.listOrder = 'asc'
					}else{
						this.listOrder = 'desc'
					}
				}else{
					this.listSort = type
					this.listOrder = 'desc'
				}
				this.search()
			},
            priceChange(price) {
                return Number(price).toFixed(2);
            },
            preHttp(str) {
                return str && str.substr(0,4)=='http';
            },
			//查询条件切换
			queryChange(e) {
				this.queryIndex=e.detail.value;
				this.searchForm.yishengxingming="";
				this.searchForm.zhicheng="";
			},
			//类别搜索
			categoryClick(categoryName){
				this.categoryName = categoryName;
				this.mescroll.resetUpScroll();
			},
			// mescroll组件初始化的回调,可获取到mescroll对象
			mescrollInit(mescroll) {
				this.mescroll = mescroll;
			},
			/*下拉刷新的回调 */
			downCallback(mescroll) {
				this.hasNext = true
				// 重置分页参数页数为1
				mescroll.resetUpScroll()
			},
			/*上拉加载的回调: mescroll携带page的参数, 其中num:当前页 从1开始, size:每页数据条数,默认10 */
			async upCallback(mescroll) {
				let params = {
					page: mescroll.num,
					limit: mescroll.size
				}
				params['sort'] = this.listSort;
				params['order'] = this.listOrder;

				if(this.categoryName!='全部'){
					params.keshi = '%' + this.categoryName + '%'
				}
				if(this.searchForm.yishengxingming){
					params['yishengxingming'] = '%' + this.searchForm.yishengxingming + '%'
				}
				if(this.searchForm.zhicheng){
					params['zhicheng'] = '%' + this.searchForm.zhicheng + '%'
				}
				let user = uni.getStorageSync("appUserid")?JSON.parse(uni.getStorageSync('userSession')):{}
                let res = {}
                if(this.userid) {
                    res = await this.$api.page(`zuozhenyisheng`, params);
                } else {
                    res = await this.$api.list(`zuozhenyisheng`, params);
                }

				// 如果是第一页数据置空
				if (mescroll.num == 1) this.list = [];
				this.list = this.list.concat(res.data.list);
				this.$forceUpdate()
				
				let length = Math.ceil(this.list.length/6)
				let arr = [];
				for (let i = 0; i<length; i++){
					arr[i] = this.list.slice(i*6, (i+1)*6)
				}
				this.lists = arr
				if (res.data.list.length == 0) this.hasNext = false;
				mescroll.endSuccess(mescroll.size, this.hasNext);
			},
			// 详情
			onDetailTap(item) {
                uni.setStorageSync("useridTag",this.userid);
				this.$utils.jump(`./detail?id=${item.id}&userid=`+this.userid)
			},
			onUpdate(e){
				this.onUpdateTap(e.currentTarget.dataset.row)
			},
			// 修改
			onUpdateTap(row){
                uni.setStorageSync("useridTag",this.userid);
				this.$utils.jump(`./add-or-update?id=${row.id}`)
			},
			// 添加
			onAddTap(){
                uni.setStorageSync("useridTag",this.userid);
				this.$utils.jump(`./add-or-update`)
			},
			onDelete(e){
				this.onDeleteTap(e.currentTarget.dataset.row.id)
			},
			onDeleteTap(id){
				var _this = this;
				uni.showModal({
					title: '提示',
					content: '是否确认删除',
					success: async function(res) {
						if (res.confirm) {
							await _this.$api.del('zuozhenyisheng', JSON.stringify([id]));
							let obj = await _this.$api.list('storeup',{
								page: 1,
								limit: 100,
								tablename: 'zuozhenyisheng',
								refid: id,
							})
							if(obj.data.list.length){
								let arr = []
								for(let x in obj.data.list){
									arr.push(obj.data.list[x].id)
								}
								await _this.$api.del('storeup',JSON.stringify(arr))
							}
							_this.$utils.msg('删除成功');
							_this.hasNext = true
							// 重置分页参数页数为1
							_this.search()
						}
					}
				});
			},
			// 搜索
			async search(){
				this.mescroll.num = 1
				let searchForm = {
					page: this.mescroll.num,
					limit: this.mescroll.size
				}
				searchForm['sort'] = this.listSort;
				searchForm['order'] = this.listOrder;

				if(this.searchForm.yishengxingming){
					searchForm['yishengxingming'] = '%' + this.searchForm.yishengxingming + '%'
				}
				if(this.categoryName!='全部'){
					searchForm.keshi = '%' + this.categoryName + '%'
				}
				if(this.searchForm.zhicheng){
					searchForm['zhicheng'] = '%' + this.searchForm.zhicheng + '%'
				}
                let res = {};
                if(this.userid) {
                    res = await this.$api.page(`zuozhenyisheng`, searchForm);
                } else {
                    res = await this.$api.list(`zuozhenyisheng`, searchForm);
                }
				// 如果是第一页数据置空
				if (this.mescroll.num == 1) this.list = [];
				this.list = this.list.concat(res.data.list);
				let length = Math.ceil(this.list.length/6)
				let arr = [];
				for (let i = 0; i<length; i++){
					arr[i] = this.list.slice(i*6, (i+1)*6)
				}
				this.lists = arr
				if (res.data.list.length == 0) this.hasNext = false;
				this.mescroll.endSuccess(this.mescroll.size, this.hasNext);
			}
		}
	};
</script>

<style lang="scss" scoped>
	.content {
		min-height: calc(100vh - 44px);
		box-sizing: border-box;
	}
	
	/* 搜索容器样式 */
	.search-container {
		position: relative;
		overflow: hidden;
	}

	.search-container::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		opacity: 0.95;
		z-index: 1;
	}

	.search-header,
	.search-form-container {
		position: relative;
		z-index: 2;
	}

	/* 搜索按钮悬停效果 */
	.search-btn:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.4) !important;
	}

	/* 筛选区域样式 */
	.filter-section {
		backdrop-filter: blur(10rpx);
	}

	/* 分类标签样式 */
	.category-tab:active {
		transform: translateY(1rpx);
	}

	/* 排序选项悬停效果 */
	.sort-option:active {
		transform: scale(0.98);
	}

	/* 响应式设计 */
	@media screen and (max-width: 750rpx) {
		.search-form-container {
			padding: 0 20rpx 20rpx !important;
		}

		.filter-section {
			margin: 0 20rpx !important;
		}

		.category-tabs {
			gap: 8rpx !important;
		}

		.category-tab {
			padding: 10rpx 20rpx !important;
			font-size: 24rpx !important;
		}
	}

	/* 滚动条样式 */
	.category-scroll::-webkit-scrollbar {
		display: none;
	}

	/* 输入框聚焦效果 */
	.search-input-container input:focus {
		outline: none;
		box-shadow: 0 0 0 2rpx rgba(74, 144, 226, 0.3);
	}

	/* 新增按钮样式 */
	.add-btn {
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.add-btn:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.5) !important;
	}

	.add-btn:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 12rpx 32rpx rgba(74, 144, 226, 0.5) !important;
	}
</style>
