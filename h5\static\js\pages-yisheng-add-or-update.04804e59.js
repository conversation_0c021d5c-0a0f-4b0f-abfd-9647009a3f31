(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-yisheng-add-or-update"],{"118c":function(e,i,t){"use strict";var r=t("4ea4");Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,t("a481"),t("f559"),t("ac6a"),t("c5f6"),t("28a5"),t("96cf");var n=r(t("3b8d")),a=r(t("e2b1")),o=r(t("064f")),s=r(t("bd56")),l={data:function(){return{cross:"",ruleForm:{yisheng<PERSON>hao:"",yishengxingming:"",mima:"",xingbie:"",zhaopian:"",lianxifangshi:"",shenfenzheng:"",youxiang:"",money:""},xingbieOptions:[],xingbieIndex:0,user:{},ro:{yish<PERSON><PERSON><PERSON>:!1,yishengxingming:!1,mima:!1,xingbie:!1,zhaopian:!1,lianxifangshi:!1,shenfenzheng:!1,youxiang:!1,money:!1}}},components:{wPicker:a.default,xiaEditor:o.default,multipleSelect:s.default},computed:{baseUrl:function(){return this.$base.url}},onLoad:function(){var e=(0,n.default)(regeneratorRuntime.mark((function e(i){var t,r,n,a,o=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=uni.getStorageSync("nowTable"),e.next=3,this.$api.session(t);case 3:if(r=e.sent,this.user=r.data,this.xingbieOptions="男,女".split(","),this.ruleForm.userid=uni.getStorageSync("appUserid"),i.refid&&(this.ruleForm.refid=Number(i.refid),this.ruleForm.nickname=uni.getStorageSync("nickname")),!i.id){e.next=14;break}return this.ruleForm.id=i.id,e.next=12,this.$api.info("yisheng",this.ruleForm.id);case 12:r=e.sent,this.ruleForm=r.data;case 14:if(this.cross=i.cross,!i.cross){e.next=58;break}n=uni.getStorageSync("crossObj"),e.t0=regeneratorRuntime.keys(n);case 18:if((e.t1=e.t0()).done){e.next=58;break}if(a=e.t1.value,"yishenggonghao"!=a){e.next=24;break}return this.ruleForm.yishenggonghao=n[a],this.ro.yishenggonghao=!0,e.abrupt("continue",18);case 24:if("yishengxingming"!=a){e.next=28;break}return this.ruleForm.yishengxingming=n[a],this.ro.yishengxingming=!0,e.abrupt("continue",18);case 28:if("mima"!=a){e.next=32;break}return this.ruleForm.mima=n[a],this.ro.mima=!0,e.abrupt("continue",18);case 32:if("xingbie"!=a){e.next=36;break}return this.ruleForm.xingbie=n[a],this.ro.xingbie=!0,e.abrupt("continue",18);case 36:if("zhaopian"!=a){e.next=40;break}return this.ruleForm.zhaopian=n[a].split(",")[0],this.ro.zhaopian=!0,e.abrupt("continue",18);case 40:if("lianxifangshi"!=a){e.next=44;break}return this.ruleForm.lianxifangshi=n[a],this.ro.lianxifangshi=!0,e.abrupt("continue",18);case 44:if("shenfenzheng"!=a){e.next=48;break}return this.ruleForm.shenfenzheng=n[a],this.ro.shenfenzheng=!0,e.abrupt("continue",18);case 48:if("youxiang"!=a){e.next=52;break}return this.ruleForm.youxiang=n[a],this.ro.youxiang=!0,e.abrupt("continue",18);case 52:if("money"!=a){e.next=56;break}return this.ruleForm.money=n[a],this.ro.money=!0,e.abrupt("continue",18);case 56:e.next=18;break;case 58:this.styleChange(),this.$forceUpdate(),uni.getStorageSync("raffleType")&&null!=uni.getStorageSync("raffleType")&&(uni.removeStorageSync("raffleType"),setTimeout((function(){o.onSubmitTap()}),300));case 61:case"end":return e.stop()}}),e,this)})));function i(i){return e.apply(this,arguments)}return i}(),methods:{styleChange:function(){this.$nextTick((function(){}))},xingbieChange:function(e){this.xingbieIndex=e.target.value,this.ruleForm.xingbie=this.xingbieOptions[this.xingbieIndex]},zhaopianTap:function(){var e=this;this.$api.upload((function(i){e.ruleForm.zhaopian="upload/"+i.file,e.$forceUpdate(),e.$nextTick((function(){e.styleChange()}))}))},getUUID:function(){return(new Date).getTime()},onSubmitTap:function(){var e=(0,n.default)(regeneratorRuntime.mark((function e(){var i,t,r,n,a,o,s,l,u,g;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.ruleForm.yishenggonghao){e.next=3;break}return this.$utils.msg("医生工号不能为空"),e.abrupt("return");case 3:if(this.ruleForm.yishengxingming){e.next=6;break}return this.$utils.msg("医生姓名不能为空"),e.abrupt("return");case 6:if(this.ruleForm.mima){e.next=9;break}return this.$utils.msg("密码不能为空"),e.abrupt("return");case 9:if(!this.ruleForm.lianxifangshi||this.$validate.isMobile(this.ruleForm.lianxifangshi)){e.next=12;break}return this.$utils.msg("联系方式应输入手机格式"),e.abrupt("return");case 12:if(!this.ruleForm.shenfenzheng||this.$validate.checkIdCard(this.ruleForm.shenfenzheng)){e.next=15;break}return this.$utils.msg("身份证应输入身份证格式"),e.abrupt("return");case 15:if(!this.ruleForm.youxiang||this.$validate.isEmail(this.ruleForm.youxiang)){e.next=18;break}return this.$utils.msg("邮箱应输入邮件格式"),e.abrupt("return");case 18:if(!this.ruleForm.money||this.$validate.isNumber(this.ruleForm.money)){e.next=21;break}return this.$utils.msg("余额应输入数字"),e.abrupt("return");case 21:if(!this.cross){e.next=38;break}if(uni.setStorageSync("crossCleanType",!0),a=uni.getStorageSync("statusColumnName"),o=uni.getStorageSync("statusColumnValue"),""==a){e.next=38;break}if(i||(i=uni.getStorageSync("crossObj")),a.startsWith("[")){e.next=34;break}for(s in i)s==a&&(i[s]=o);return l=uni.getStorageSync("crossTable"),e.next=32,this.$api.update("".concat(l),i);case 32:e.next=38;break;case 34:t=Number(uni.getStorageSync("appUserid")),r=i["id"],n=uni.getStorageSync("statusColumnName"),n=n.replace(/\[/,"").replace(/\]/,"");case 38:if(!r||!t){e.next=61;break}return this.ruleForm.crossuserid=t,this.ruleForm.crossrefid=r,u={page:1,limit:10,crossuserid:t,crossrefid:r},e.next=44,this.$api.list("yisheng",u);case 44:if(g=e.sent,!(g.data.total>=n)){e.next=51;break}return this.$utils.msg(uni.getStorageSync("tips")),uni.removeStorageSync("crossCleanType"),e.abrupt("return",!1);case 51:if(!this.ruleForm.id){e.next=56;break}return e.next=54,this.$api.update("yisheng",this.ruleForm);case 54:e.next=58;break;case 56:return e.next=58,this.$api.add("yisheng",this.ruleForm);case 58:this.$utils.msgBack("提交成功");case 59:e.next=69;break;case 61:if(!this.ruleForm.id){e.next=66;break}return e.next=64,this.$api.update("yisheng",this.ruleForm);case 64:e.next=68;break;case 66:return e.next=68,this.$api.add("yisheng",this.ruleForm);case 68:this.$utils.msgBack("提交成功");case 69:case"end":return e.stop()}}),e,this)})));function i(){return e.apply(this,arguments)}return i}(),optionsChange:function(e){this.index=e.target.value},bindDateChange:function(e){this.date=e.target.value},getDate:function(e){var i=new Date,t=i.getFullYear(),r=i.getMonth()+1,n=i.getDate();return"start"===e?t-=60:"end"===e&&(t+=2),r=r>9?r:"0"+r,n=n>9?n:"0"+n,"".concat(t,"-").concat(r,"-").concat(n)},toggleTab:function(e){if(this.ro[e])return!1;this.$refs[e].show()}}};i.default=l},1519:function(e,i,t){var r=t("8ac5");"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var n=t("4f06").default;n("9337f6ec",r,!0,{sourceMap:!1,shadowMode:!1})},"20cc":function(e,i,t){"use strict";t.r(i);var r=t("c2d3"),n=t("bee7");for(var a in n)"default"!==a&&function(e){t.d(i,e,(function(){return n[e]}))}(a);t("f939");var o,s=t("f0c5"),l=Object(s["a"])(n["default"],r["b"],r["c"],!1,null,"8f1dfa24",null,!1,r["a"],o);i["default"]=l.exports},"8ac5":function(e,i,t){var r=t("24fb");i=r(!1),i.push([e.i,'@charset "UTF-8";\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.content[data-v-8f1dfa24]{min-height:calc(100vh - 44px);box-sizing:border-box}',""]),e.exports=i},bee7:function(e,i,t){"use strict";t.r(i);var r=t("118c"),n=t.n(r);for(var a in r)"default"!==a&&function(e){t.d(i,e,(function(){return r[e]}))}(a);i["default"]=n.a},c2d3:function(e,i,t){"use strict";var r,n=function(){var e=this,i=e.$createElement,t=e._self._c||i;return t("v-uni-view",{staticClass:"content"},[t("v-uni-view",{style:{width:"100%",padding:"0",position:"relative",background:"#fff",height:"100%"}},[t("v-uni-form",{staticClass:"app-update-pv",style:{width:"100%",padding:"24rpx",background:"#fff",display:"block",height:"auto"}},[t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("医生工号")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.yishenggonghao,placeholder:"医生工号",type:"text"},model:{value:e.ruleForm.yishenggonghao,callback:function(i){e.$set(e.ruleForm,"yishenggonghao",i)},expression:"ruleForm.yishenggonghao"}})],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("医生姓名")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.yishengxingming,placeholder:"医生姓名",type:"text"},model:{value:e.ruleForm.yishengxingming,callback:function(i){e.$set(e.ruleForm,"yishengxingming",i)},expression:"ruleForm.yishengxingming"}})],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("密码")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.mima,placeholder:"密码",type:"text"},model:{value:e.ruleForm.mima,callback:function(i){e.$set(e.ruleForm,"mima",i)},expression:"ruleForm.mima"}})],1),t("v-uni-view",{staticClass:" select",style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("性别")]),t("v-uni-picker",{style:{width:"100%",flex:"1",height:"auto"},attrs:{disabled:e.ro.xingbie,value:e.xingbieIndex,range:e.xingbieOptions},on:{change:function(i){arguments[0]=i=e.$handleEvent(i),e.xingbieChange.apply(void 0,arguments)}}},[t("v-uni-view",{staticClass:"uni-input",style:{width:"100%",lineHeight:"80rpx",fontSize:"28rpx",color:"#D4CF5D"}},[e._v(e._s(e.ruleForm.xingbie?e.ruleForm.xingbie:"请选择性别"))])],1)],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.zhaopianTap.apply(void 0,arguments)}}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("照片")]),e.ruleForm.zhaopian?t("v-uni-image",{staticClass:"avator",style:{width:"80rpx",borderRadius:"100%",objectFit:"cover",display:"block",height:"80rpx"},attrs:{src:e.baseUrl+e.ruleForm.zhaopian.split(",")[0],mode:"aspectFill"}}):t("v-uni-image",{staticClass:"avator",style:{width:"80rpx",borderRadius:"100%",objectFit:"cover",display:"block",height:"80rpx"},attrs:{src:"../../static/gen/upload.png",mode:"aspectFill"}})],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("联系方式")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.lianxifangshi,placeholder:"联系方式",type:"text"},model:{value:e.ruleForm.lianxifangshi,callback:function(i){e.$set(e.ruleForm,"lianxifangshi",i)},expression:"ruleForm.lianxifangshi"}})],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("身份证")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.shenfenzheng,placeholder:"身份证",type:"text"},model:{value:e.ruleForm.shenfenzheng,callback:function(i){e.$set(e.ruleForm,"shenfenzheng",i)},expression:"ruleForm.shenfenzheng"}})],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("邮箱")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.youxiang,placeholder:"邮箱",type:"text"},model:{value:e.ruleForm.youxiang,callback:function(i){e.$set(e.ruleForm,"youxiang",i)},expression:"ruleForm.youxiang"}})],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("余额")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.money,placeholder:"余额",type:"digit"},model:{value:e.ruleForm.money,callback:function(i){e.$set(e.ruleForm,"money",e._n(i))},expression:"ruleForm.money"}})],1),t("v-uni-view",{staticClass:"btn",style:{width:"100%",alignItems:"center",justifyContent:"center",display:"flex",height:"auto"}},[t("v-uni-button",{staticClass:"bg-red",style:{border:"0",padding:"0px",margin:"0",color:"rgb(255, 255, 255)",background:"#D4CF5D",width:"48%",lineHeight:"80rpx",fontSize:"28rpx",height:"80rpx"},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.onSubmitTap.apply(void 0,arguments)}}},[e._v("提交")])],1)],1)],1)],1)},a=[];t.d(i,"b",(function(){return n})),t.d(i,"c",(function(){return a})),t.d(i,"a",(function(){return r}))},f939:function(e,i,t){"use strict";var r=t("1519"),n=t.n(r);n.a}}]);