(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common-linkOthers-linkOthers"],{1905:function(t,n,e){"use strict";e.r(n);var u=e("422c"),i=e("374a");for(var r in i)"default"!==r&&function(t){e.d(n,t,(function(){return i[t]}))}(r);var o,a=e("f0c5"),c=Object(a["a"])(i["default"],u["b"],u["c"],!1,null,null,null,!1,u["a"],o);n["default"]=c.exports},"374a":function(t,n,e){"use strict";e.r(n);var u=e("bb63"),i=e.n(u);for(var r in u)"default"!==r&&function(t){e.d(n,t,(function(){return u[t]}))}(r);n["default"]=i.a},"422c":function(t,n,e){"use strict";var u,i=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("v-uni-view",[e("v-uni-view",[e("v-uni-web-view",{attrs:{src:t.url}})],1)],1)},r=[];e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return r})),e.d(n,"a",(function(){return u}))},bb63:function(t,n,e){"use strict";var u=e("4ea4");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i=u(e("7618")),r={data:function(){return{url:""}},onLoad:function(t){this.url=decodeURIComponent(t.url),this.isNotEmpty(t.title)&&this.setTitle(t.title)},methods:{isNotEmpty:function(t){return void 0!=(0,i.default)(t)&&null!=t&&""!=t&&"undefined"!=t&&0!=t.length},setTitle:function(t){uni.setNavigationBarTitle({title:t})}}};n.default=r}}]);