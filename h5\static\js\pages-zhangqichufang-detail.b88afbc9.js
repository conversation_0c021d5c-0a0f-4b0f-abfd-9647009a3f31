(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-zhangqichufang-detail"],{"04b7":function(t,i,e){var n=e("24fb");i=n(!1),i.push([t.i,'@charset "UTF-8";\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */uni-page-body[data-v-05582977]{--animate-duration:1s;--animate-delay:1s;--animate-repeat:1}.content[data-v-05582977]{min-height:calc(100vh - 44px);box-sizing:border-box}.seat-list[data-v-05582977]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-flex-wrap:wrap;flex-wrap:wrap;background:#fff;margin:%?20?%;border-radius:%?20?%;padding:%?20?%;font-size:%?30?%}.seat-list .seat-item[data-v-05582977]{width:33.33%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column;margin-bottom:%?20?%}.seat-list .seat-item .seat-icon[data-v-05582977]{width:%?50?%;height:%?50?%;margin-bottom:%?10?%}uni-audio[data-v-05582977]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column}.audio[data-v-05582977] .uni-audio-default{width:inherit}',""]),t.exports=i},1059:function(t,i,e){var n=e("04b7");"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=e("4f06").default;a("39d02196",n,!0,{sourceMap:!1,shadowMode:!1})},"117c":function(t,i,e){"use strict";e.r(i);var n=e("d64f"),a=e("5315");for(var r in a)"default"!==r&&function(t){e.d(i,t,(function(){return a[t]}))}(r);e("b1de");var s,o=e("f0c5"),l=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"05582977",null,!1,n["a"],s);i["default"]=l.exports},5315:function(t,i,e){"use strict";e.r(i);var n=e("ab30"),a=e.n(n);for(var r in n)"default"!==r&&function(t){e.d(i,t,(function(){return n[t]}))}(r);i["default"]=a.a},ab30:function(t,i,e){"use strict";var n=e("4ea4");Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("28a5"),e("ac6a"),e("f559"),e("55dd"),e("96cf");var a=n(e("3b8d")),r={data:function(){return{btnColor:["#409eff","#67c23a","#909399","#e6a23c","#f56c6c","#356c6c","#351c6c","#f093a9","#a7c23a","#104eff","#10441f","#a21233","#503319"],id:"",userid:"",detail:{},swiperList:[],commentList:[],mescroll:null,downOption:{auto:!1},upOption:{noMoreSize:3,textNoMore:"~ 没有更多了 ~"},hasNext:!0,user:{},count:0,timer:null,title:""}},components:{},computed:{baseUrl:function(){return this.$base.url}},onLoad:function(){var t=(0,a.default)(regeneratorRuntime.mark((function t(i){var e,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e=uni.getStorageSync("nowTable"),t.next=3,this.$api.session(e);case 3:n=t.sent,this.user=n.data,this.id=i.id,i.userid&&(this.userid=i.userid),this.init();case 8:case"end":return t.stop()}}),t,this)})));function i(i){return t.apply(this,arguments)}return i}(),onUnload:function(){this.timer&&clearInterval(this.timer)},onShow:function(){var t=(0,a.default)(regeneratorRuntime.mark((function t(i){var e,n,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e=uni.getStorageSync("nowTable"),t.next=3,this.$api.session(e);case 3:if(n=t.sent,this.user=n.data,this.btnColor=this.btnColor.sort((function(){return.5-Math.random()})),a=uni.getStorageSync("crossCleanType"),!a){t.next=14;break}return uni.removeStorageSync("crossCleanType"),t.next=11,this.$api.info("zhangqichufang",this.id);case 11:n=t.sent,this.detail=n.data,this.title=this.detail.chufangmingcheng;case 14:case"end":return t.stop()}}),t,this)})));function i(i){return t.apply(this,arguments)}return i}(),destroyed:function(){},methods:{callClick:function(t){uni.makePhoneCall({phoneNumber:t})},onPayTap:function(){var t=this;if(!this.user)return this.$utils.msg("请先登录"),setTimeout((function(){t.$utils.jump("../login/login")}),1500),!1;uni.setStorageSync("paytable","zhangqichufang"),uni.setStorageSync("payObject",this.detail),this.$utils.jump("../pay-confirm/pay-confirm?type=1")},onDetailTap:function(t){uni.setStorageSync("useridTag",this.userid),this.$utils.jump("./detail?id=".concat(t.id,"&userid=")+this.userid)},onAcrossTap:function(){var t=(0,a.default)(regeneratorRuntime.mark((function t(i,e,n,a,r,s){var o,l,u,c=arguments;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(c.length>6&&void 0!==c[6]?c[6]:1,o=this,this.user){t.next=6;break}return this.$utils.msg("请先登录"),setTimeout((function(){o.$utils.jump("../login/login")}),1500),t.abrupt("return",!1);case 6:if(uni.setStorageSync("crossTable","zhangqichufang"),uni.setStorageSync("crossObj",this.detail),uni.setStorageSync("statusColumnName",a),uni.setStorageSync("statusColumnValue",s),uni.setStorageSync("tips",r),""==a||a.startsWith("[")){t.next=21;break}l=uni.getStorageSync("crossObj"),t.t0=regeneratorRuntime.keys(l);case 14:if((t.t1=t.t0()).done){t.next=21;break}if(u=t.t1.value,u!=a||l[u]!=s){t.next=19;break}return this.$utils.msg(r),t.abrupt("return");case 19:t.next=14;break;case 21:this.$utils.jump("../".concat(i,"/add-or-update?cross=true"));case 22:case"end":return t.stop()}}),t,this)})));function i(i,e,n,a,r,s){return t.apply(this,arguments)}return i}(),init:function(){var t=(0,a.default)(regeneratorRuntime.mark((function t(){var i,e,n=arguments;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return i=n.length>0&&void 0!==n[0]?n[0]:1,this.timer&&clearInterval(this.timer),t.next=4,this.$api.info("zhangqichufang",this.id);case 4:if(e=t.sent,this.detail=e.data,this.title=this.detail.chufangmingcheng,this.swiperList=this.detail.chufangtupian?this.detail.chufangtupian.split(","):[],2!=i){t.next=12;break}return this.detail.discussnum++,t.next=12,this.$api.update("zhangqichufang",this.detail);case 12:case"end":return t.stop()}}),t,this)})));function i(){return t.apply(this,arguments)}return i}(),mescrollInit:function(t){this.mescroll=t},downCallback:function(t){this.hasNext=!0,t.resetUpScroll()},upCallback:function(){var t=(0,a.default)(regeneratorRuntime.mark((function t(i){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:i.endSuccess(i.size,this.hasNext);case 1:case"end":return t.stop()}}),t,this)})));function i(i){return t.apply(this,arguments)}return i}(),onChatTap:function(){this.$utils.jump("../chat/chat")},download:function(t){var i=this;t=i.$base.url+t,uni.downloadFile({url:t,success:function(e){200===e.statusCode&&(i.$utils.msg("下载成功"),window.open(t))}})},onCartTabTap:function(){this.$utils.tab("../shop-cart/shop-cart")}}};i.default=r},b1de:function(t,i,e){"use strict";var n=e("1059"),a=e.n(n);a.a},d64f:function(t,i,e){"use strict";var n={"mescroll-uni":e("f05e").default},a=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",[e("mescroll-uni",{attrs:{up:t.upOption,down:t.downOption},on:{init:function(i){arguments[0]=i=t.$handleEvent(i),t.mescrollInit.apply(void 0,arguments)},down:function(i){arguments[0]=i=t.$handleEvent(i),t.downCallback.apply(void 0,arguments)},up:function(i){arguments[0]=i=t.$handleEvent(i),t.upCallback.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"content"},[e("v-uni-view",{staticClass:"container",style:{width:"100%",padding:"0",position:"relative",background:"#DFD8CC",height:"auto"}},[e("v-uni-swiper",{staticClass:"swiper",style:{width:"100%",background:"#fff",height:"400rpx",zIndex:"100"},attrs:{"indicator-dots":!1,autoplay:!1,circular:!1,"indicator-active-color":"#000000","indicator-color":"rgba(0, 0, 0, .3)",duration:500,interval:5e3,vertical:!1}},t._l(t.swiperList,(function(i,n){return e("v-uni-swiper-item",{key:n,style:{width:"100%",background:"#fff",height:"400rpx"}},["http"==i.substring(0,4)?e("v-uni-image",{style:{width:"100%",objectFit:"cover",display:"block",height:"400rpx"},attrs:{mode:"aspectFill",src:i}}):e("v-uni-image",{style:{width:"100%",objectFit:"cover",display:"block",height:"400rpx"},attrs:{mode:"aspectFill",src:t.baseUrl+i}})],1)})),1),e("v-uni-view",{staticClass:"detail-content",style:{padding:"20rpx 30rpx",margin:"-40rpx 0 0 0",overflow:"hidden",borderRadius:"40rpx 40rpx 0 0",flexWrap:"wrap",background:"#fff",display:"flex",width:"100%",height:"auto",zIndex:"1111"}},[e("v-uni-view",{staticClass:"detail-list-item title",style:{margin:"0 0 24rpx 0",borderColor:"#D8D8D8",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[e("v-uni-view",{staticClass:"lable",style:{width:"auto",padding:"0 10rpx 0 0",lineHeight:"48rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[t._v("处方名称：")]),e("v-uni-view",{staticClass:"text",style:{padding:"0px",margin:"0px",lineHeight:"48rpx",fontSize:"28rpx",color:"rgb(0, 0, 0)",flex:"1"}},[t._v(t._s(t.detail.chufangmingcheng))])],1),e("v-uni-view",{staticClass:"detail-list-item",style:{margin:"0 0 24rpx 0",borderColor:"#D8D8D8",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[e("v-uni-view",{staticClass:"lable",style:{width:"auto",padding:"0 10rpx 0 0",lineHeight:"48rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[t._v("上传时间：")]),e("v-uni-view",{staticClass:"text",style:{padding:"0px",margin:"0px",lineHeight:"48rpx",fontSize:"28rpx",color:"rgb(0, 0, 0)",flex:"1"}},[t._v(t._s(t.detail.shangchuanshijian))])],1),e("v-uni-view",{staticClass:"detail-list-item",style:{margin:"0 0 24rpx 0",borderColor:"#D8D8D8",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[e("v-uni-view",{staticClass:"lable",style:{width:"auto",padding:"0 10rpx 0 0",lineHeight:"48rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[t._v("医生工号：")]),e("v-uni-view",{staticClass:"text",style:{padding:"0px",margin:"0px",lineHeight:"48rpx",fontSize:"28rpx",color:"rgb(0, 0, 0)",flex:"1"}},[t._v(t._s(t.detail.yishenggonghao))])],1),e("v-uni-view",{staticClass:"detail-list-item",style:{margin:"0 0 24rpx 0",borderColor:"#D8D8D8",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[e("v-uni-view",{staticClass:"lable",style:{width:"auto",padding:"0 10rpx 0 0",lineHeight:"48rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[t._v("医生姓名：")]),e("v-uni-view",{staticClass:"text",style:{padding:"0px",margin:"0px",lineHeight:"48rpx",fontSize:"28rpx",color:"rgb(0, 0, 0)",flex:"1"}},[t._v(t._s(t.detail.yishengxingming))])],1),e("v-uni-view",{staticClass:"detail-list-item",style:{margin:"0 0 24rpx 0",borderColor:"#D8D8D8",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[e("v-uni-view",{staticClass:"lable",style:{width:"auto",padding:"0 10rpx 0 0",lineHeight:"48rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[t._v("处方内容：")]),e("v-uni-view",{staticClass:"text",style:{padding:"0px",margin:"0px",lineHeight:"48rpx",fontSize:"28rpx",color:"rgb(0, 0, 0)",flex:"1"}},[t._v(t._s(t.detail.chufangneirong))])],1),e("v-uni-view",{staticClass:"bottom-content bg-white tabbar border shop",style:{width:"100%",padding:"0",flexWrap:"wrap",background:"none",display:"flex",height:"auto"}},[t.userid&&t.isAuth("zhangqichufang","私聊")?e("v-uni-button",{style:{border:"0",padding:"0 20rpx",margin:"0",color:"rgb(255, 255, 255)",background:"rgb(255, 170, 51)",width:"auto",fontSize:"28rpx",height:"80rpx"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.chatClick.apply(void 0,arguments)}}},[t._v("联系TA")]):t._e(),!t.userid&&t.isAuthFront("zhangqichufang","私聊")?e("v-uni-button",{style:{border:"0",padding:"0 20rpx",margin:"0",color:"rgb(255, 255, 255)",background:"rgb(255, 170, 51)",width:"auto",fontSize:"28rpx",height:"80rpx"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.chatClick.apply(void 0,arguments)}}},[t._v("联系TA")]):t._e()],1)],1)],1)],1)],1)],1)},r=[];e.d(i,"b",(function(){return a})),e.d(i,"c",(function(){return r})),e.d(i,"a",(function(){return n}))}}]);