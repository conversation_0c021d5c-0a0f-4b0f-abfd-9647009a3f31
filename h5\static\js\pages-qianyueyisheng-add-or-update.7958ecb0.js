(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-qianyueyisheng-add-or-update"],{6884:function(e,i,r){"use strict";var t=r("df76"),n=r.n(t);n.a},a02d:function(e,i,r){"use strict";var t={"w-picker":r("e2b1").default},n=function(){var e=this,i=e.$createElement,r=e._self._c||i;return r("v-uni-view",{staticClass:"content"},[r("v-uni-view",{style:{width:"100%",padding:"0",position:"relative",background:"#fff",height:"100%"}},[r("v-uni-form",{staticClass:"app-update-pv",style:{width:"100%",padding:"24rpx",background:"#fff",display:"block",height:"auto"}},[r("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[r("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("签约编号")]),r("v-uni-view",{staticClass:"right-input",style:{padding:"0px 24rpx",margin:"0px",lineHeight:"80rpx",fontSize:"28rpx",color:"rgb(0, 0, 0)",flex:"1"}},[e._v(e._s(e.ruleForm.qianyuebianhao))])],1),r("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[r("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("签约时间")]),r("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.qianyueshijian,placeholder:"签约时间"},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.toggleTab("qianyueshijian")}},model:{value:e.ruleForm.qianyueshijian,callback:function(i){e.$set(e.ruleForm,"qianyueshijian",i)},expression:"ruleForm.qianyueshijian"}})],1),r("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[r("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("上门地址")]),r("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.shangmendizhi,placeholder:"上门地址",type:"text"},model:{value:e.ruleForm.shangmendizhi,callback:function(i){e.$set(e.ruleForm,"shangmendizhi",i)},expression:"ruleForm.shangmendizhi"}})],1),r("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[r("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("医生工号")]),r("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.yishenggonghao,placeholder:"医生工号",type:"text"},model:{value:e.ruleForm.yishenggonghao,callback:function(i){e.$set(e.ruleForm,"yishenggonghao",i)},expression:"ruleForm.yishenggonghao"}})],1),r("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[r("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("医生姓名")]),r("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.yishengxingming,placeholder:"医生姓名",type:"text"},model:{value:e.ruleForm.yishengxingming,callback:function(i){e.$set(e.ruleForm,"yishengxingming",i)},expression:"ruleForm.yishengxingming"}})],1),r("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[r("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("科室")]),r("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.keshi,placeholder:"科室",type:"text"},model:{value:e.ruleForm.keshi,callback:function(i){e.$set(e.ruleForm,"keshi",i)},expression:"ruleForm.keshi"}})],1),r("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.zhaopianTap.apply(void 0,arguments)}}},[r("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("照片")]),e.ruleForm.zhaopian?r("v-uni-image",{staticClass:"avator",style:{width:"80rpx",borderRadius:"100%",objectFit:"cover",display:"block",height:"80rpx"},attrs:{src:e.baseUrl+e.ruleForm.zhaopian.split(",")[0],mode:"aspectFill"}}):r("v-uni-image",{staticClass:"avator",style:{width:"80rpx",borderRadius:"100%",objectFit:"cover",display:"block",height:"80rpx"},attrs:{src:"../../static/gen/upload.png",mode:"aspectFill"}})],1),r("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[r("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("用户账号")]),r("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.yonghuzhanghao,placeholder:"用户账号",type:"text"},model:{value:e.ruleForm.yonghuzhanghao,callback:function(i){e.$set(e.ruleForm,"yonghuzhanghao",i)},expression:"ruleForm.yonghuzhanghao"}})],1),r("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[r("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("姓名")]),r("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.xingming,placeholder:"姓名",type:"text"},model:{value:e.ruleForm.xingming,callback:function(i){e.$set(e.ruleForm,"xingming",i)},expression:"ruleForm.xingming"}})],1),r("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[r("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("手机")]),r("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.shouji,placeholder:"手机",type:"text"},model:{value:e.ruleForm.shouji,callback:function(i){e.$set(e.ruleForm,"shouji",i)},expression:"ruleForm.shouji"}})],1),r("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[r("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("签约内容")]),r("v-uni-textarea",{style:{border:"0",padding:"24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"300rpx"},attrs:{placeholder:"签约内容"},model:{value:e.ruleForm.qianyueneirong,callback:function(i){e.$set(e.ruleForm,"qianyueneirong",i)},expression:"ruleForm.qianyueneirong"}})],1),r("v-uni-view",{staticClass:"btn",style:{width:"100%",alignItems:"center",justifyContent:"center",display:"flex",height:"auto"}},[r("v-uni-button",{staticClass:"bg-red",style:{border:"0",padding:"0px",margin:"0",color:"rgb(255, 255, 255)",background:"#D4CF5D",width:"48%",lineHeight:"80rpx",fontSize:"28rpx",height:"80rpx"},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.onSubmitTap.apply(void 0,arguments)}}},[e._v("提交")])],1)],1),r("w-picker",{ref:"qianyueshijian",attrs:{mode:"dateTime",step:"1",current:!1,hasSecond:!1,themeColor:"#333333"},on:{confirm:function(i){arguments[0]=i=e.$handleEvent(i),e.qianyueshijianConfirm.apply(void 0,arguments)}}})],1)],1)},a=[];r.d(i,"b",(function(){return n})),r.d(i,"c",(function(){return a})),r.d(i,"a",(function(){return t}))},cba0:function(e,i,r){"use strict";r.r(i);var t=r("a02d"),n=r("d87b");for(var a in n)"default"!==a&&function(e){r.d(i,e,(function(){return n[e]}))}(a);r("6884");var o,s=r("f0c5"),u=Object(s["a"])(n["default"],t["b"],t["c"],!1,null,"375f5885",null,!1,t["a"],o);i["default"]=u.exports},d87b:function(e,i,r){"use strict";r.r(i);var t=r("f853"),n=r.n(t);for(var a in t)"default"!==a&&function(e){r.d(i,e,(function(){return t[e]}))}(a);i["default"]=n.a},df76:function(e,i,r){var t=r("ee6f");"string"===typeof t&&(t=[[e.i,t,""]]),t.locals&&(e.exports=t.locals);var n=r("4f06").default;n("92cf7c66",t,!0,{sourceMap:!1,shadowMode:!1})},ee6f:function(e,i,r){var t=r("24fb");i=t(!1),i.push([e.i,'@charset "UTF-8";\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.content[data-v-375f5885]{min-height:calc(100vh - 44px);box-sizing:border-box}',""]),e.exports=i},f853:function(e,i,r){"use strict";var t=r("4ea4");Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,r("a481"),r("f559"),r("ac6a"),r("28a5"),r("c5f6"),r("96cf");var n=t(r("3b8d")),a=t(r("e2b1")),o=t(r("064f")),s=t(r("bd56")),u={data:function(){return{cross:"",ruleForm:{qianyuebianhao:this.getUUID(),qianyueneirong:"",qianyueshijian:"",shangmendizhi:"",yishenggonghao:"",yishengxingming:"",keshi:"",zhaopian:"",yonghuzhanghao:"",xingming:"",shouji:"",shhf:""},user:{},ro:{qianyuebianhao:!1,qianyueneirong:!1,qianyueshijian:!1,shangmendizhi:!1,yishenggonghao:!1,yishengxingming:!1,keshi:!1,zhaopian:!1,yonghuzhanghao:!1,xingming:!1,shouji:!1,sfsh:!1,shhf:!1}}},components:{wPicker:a.default,xiaEditor:o.default,multipleSelect:s.default},computed:{baseUrl:function(){return this.$base.url}},onLoad:function(){var e=(0,n.default)(regeneratorRuntime.mark((function e(i){var r,t,n,a,o=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=uni.getStorageSync("nowTable"),e.next=3,this.$api.session(r);case 3:if(t=e.sent,this.user=t.data,this.ruleForm.yonghuzhanghao=this.user.yonghuzhanghao,this.ro.yonghuzhanghao=!0,this.ruleForm.xingming=this.user.xingming,this.ro.xingming=!0,this.ruleForm.shouji=this.user.shouji,this.ro.shouji=!0,this.ruleForm.userid=uni.getStorageSync("appUserid"),i.refid&&(this.ruleForm.refid=Number(i.refid),this.ruleForm.nickname=uni.getStorageSync("nickname")),!i.id){e.next=19;break}return this.ruleForm.id=i.id,e.next=17,this.$api.info("qianyueyisheng",this.ruleForm.id);case 17:t=e.sent,this.ruleForm=t.data;case 19:if(this.cross=i.cross,!i.cross){e.next=71;break}n=uni.getStorageSync("crossObj"),e.t0=regeneratorRuntime.keys(n);case 23:if((e.t1=e.t0()).done){e.next=71;break}if(a=e.t1.value,"qianyuebianhao"!=a){e.next=29;break}return this.ruleForm.qianyuebianhao=n[a],this.ro.qianyuebianhao=!0,e.abrupt("continue",23);case 29:if("qianyueneirong"!=a){e.next=33;break}return this.ruleForm.qianyueneirong=n[a],this.ro.qianyueneirong=!0,e.abrupt("continue",23);case 33:if("qianyueshijian"!=a){e.next=37;break}return this.ruleForm.qianyueshijian=n[a],this.ro.qianyueshijian=!0,e.abrupt("continue",23);case 37:if("shangmendizhi"!=a){e.next=41;break}return this.ruleForm.shangmendizhi=n[a],this.ro.shangmendizhi=!0,e.abrupt("continue",23);case 41:if("yishenggonghao"!=a){e.next=45;break}return this.ruleForm.yishenggonghao=n[a],this.ro.yishenggonghao=!0,e.abrupt("continue",23);case 45:if("yishengxingming"!=a){e.next=49;break}return this.ruleForm.yishengxingming=n[a],this.ro.yishengxingming=!0,e.abrupt("continue",23);case 49:if("keshi"!=a){e.next=53;break}return this.ruleForm.keshi=n[a],this.ro.keshi=!0,e.abrupt("continue",23);case 53:if("zhaopian"!=a){e.next=57;break}return this.ruleForm.zhaopian=n[a].split(",")[0],this.ro.zhaopian=!0,e.abrupt("continue",23);case 57:if("yonghuzhanghao"!=a){e.next=61;break}return this.ruleForm.yonghuzhanghao=n[a],this.ro.yonghuzhanghao=!0,e.abrupt("continue",23);case 61:if("xingming"!=a){e.next=65;break}return this.ruleForm.xingming=n[a],this.ro.xingming=!0,e.abrupt("continue",23);case 65:if("shouji"!=a){e.next=69;break}return this.ruleForm.shouji=n[a],this.ro.shouji=!0,e.abrupt("continue",23);case 69:e.next=23;break;case 71:this.styleChange(),this.$forceUpdate(),uni.getStorageSync("raffleType")&&null!=uni.getStorageSync("raffleType")&&(uni.removeStorageSync("raffleType"),setTimeout((function(){o.onSubmitTap()}),300));case 74:case"end":return e.stop()}}),e,this)})));function i(i){return e.apply(this,arguments)}return i}(),methods:{styleChange:function(){this.$nextTick((function(){}))},qianyueshijianConfirm:function(e){console.log(e),this.ruleForm.qianyueshijian=e.result,this.$forceUpdate()},zhaopianTap:function(){var e=this;this.$api.upload((function(i){e.ruleForm.zhaopian="upload/"+i.file,e.$forceUpdate(),e.$nextTick((function(){e.styleChange()}))}))},getUUID:function(){return(new Date).getTime()},onSubmitTap:function(){var e=(0,n.default)(regeneratorRuntime.mark((function e(){var i,r,t,n,a,o,s,u,l,h;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.ruleForm.qianyuebianhao&&(this.ruleForm.qianyuebianhao=String(this.ruleForm.qianyuebianhao)),!this.cross){e.next=18;break}if(uni.setStorageSync("crossCleanType",!0),a=uni.getStorageSync("statusColumnName"),o=uni.getStorageSync("statusColumnValue"),""==a){e.next=18;break}if(i||(i=uni.getStorageSync("crossObj")),a.startsWith("[")){e.next=14;break}for(s in i)s==a&&(i[s]=o);return u=uni.getStorageSync("crossTable"),e.next=12,this.$api.update("".concat(u),i);case 12:e.next=18;break;case 14:r=Number(uni.getStorageSync("appUserid")),t=i["id"],n=uni.getStorageSync("statusColumnName"),n=n.replace(/\[/,"").replace(/\]/,"");case 18:if(!t||!r){e.next=41;break}return this.ruleForm.crossuserid=r,this.ruleForm.crossrefid=t,l={page:1,limit:10,crossuserid:r,crossrefid:t},e.next=24,this.$api.list("qianyueyisheng",l);case 24:if(h=e.sent,!(h.data.total>=n)){e.next=31;break}return this.$utils.msg(uni.getStorageSync("tips")),uni.removeStorageSync("crossCleanType"),e.abrupt("return",!1);case 31:if(!this.ruleForm.id){e.next=36;break}return e.next=34,this.$api.update("qianyueyisheng",this.ruleForm);case 34:e.next=38;break;case 36:return e.next=38,this.$api.add("qianyueyisheng",this.ruleForm);case 38:this.$utils.msgBack("提交成功");case 39:e.next=49;break;case 41:if(!this.ruleForm.id){e.next=46;break}return e.next=44,this.$api.update("qianyueyisheng",this.ruleForm);case 44:e.next=48;break;case 46:return e.next=48,this.$api.add("qianyueyisheng",this.ruleForm);case 48:this.$utils.msgBack("提交成功");case 49:case"end":return e.stop()}}),e,this)})));function i(){return e.apply(this,arguments)}return i}(),optionsChange:function(e){this.index=e.target.value},bindDateChange:function(e){this.date=e.target.value},getDate:function(e){var i=new Date,r=i.getFullYear(),t=i.getMonth()+1,n=i.getDate();return"start"===e?r-=60:"end"===e&&(r+=2),t=t>9?t:"0"+t,n=n>9?n:"0"+n,"".concat(r,"-").concat(t,"-").concat(n)},toggleTab:function(e){if(this.ro[e])return!1;this.$refs[e].show()}}};i.default=u}}]);