(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-zuozhenyisheng-add-or-update"],{"0187":function(e,i,t){"use strict";var r=t("1b3b"),n=t.n(r);n.a},"1b3b":function(e,i,t){var r=t("7c49");"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var n=t("4f06").default;n("b11796aa",r,!0,{sourceMap:!1,shadowMode:!1})},"6db3":function(e,i,t){"use strict";var r=t("4ea4");Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,t("a481"),t("f559"),t("ac6a"),t("28a5"),t("c5f6"),t("96cf");var n=r(t("3b8d")),a=r(t("e2b1")),s=r(t("064f")),o=r(t("bd56")),u={data:function(){return{cross:"",ruleForm:{yishenggonghao:"",yishengxingming:"",xingbie:"",keshi:"",yiling:"",zhaopian:"",zhicheng:"",shanzhang:"",lianxifangshi:"",zuozhenshijian:"",discussnum:"",storeupnum:""},keshiOptions:[],keshiIndex:0,user:{},ro:{yishenggonghao:!1,yishengxingming:!1,xingbie:!1,keshi:!1,yiling:!1,zhaopian:!1,zhicheng:!1,shanzhang:!1,lianxifangshi:!1,zuozhenshijian:!1,discussnum:!1,storeupnum:!1}}},components:{wPicker:a.default,xiaEditor:s.default,multipleSelect:o.default},computed:{baseUrl:function(){return this.$base.url}},onLoad:function(){var e=(0,n.default)(regeneratorRuntime.mark((function e(i){var t,r,n,a,s=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=uni.getStorageSync("nowTable"),e.next=3,this.$api.session(t);case 3:return r=e.sent,this.user=r.data,e.next=7,this.$api.option("keshi","keshi",{});case 7:if(r=e.sent,this.keshiOptions=r.data,this.keshiOptions.unshift("请选择科室"),this.ruleForm.userid=uni.getStorageSync("appUserid"),i.refid&&(this.ruleForm.refid=Number(i.refid),this.ruleForm.nickname=uni.getStorageSync("nickname")),!i.id){e.next=18;break}return this.ruleForm.id=i.id,e.next=16,this.$api.info("zuozhenyisheng",this.ruleForm.id);case 16:r=e.sent,this.ruleForm=r.data;case 18:if(this.cross=i.cross,!i.cross){e.next=74;break}n=uni.getStorageSync("crossObj"),e.t0=regeneratorRuntime.keys(n);case 22:if((e.t1=e.t0()).done){e.next=74;break}if(a=e.t1.value,"yishenggonghao"!=a){e.next=28;break}return this.ruleForm.yishenggonghao=n[a],this.ro.yishenggonghao=!0,e.abrupt("continue",22);case 28:if("yishengxingming"!=a){e.next=32;break}return this.ruleForm.yishengxingming=n[a],this.ro.yishengxingming=!0,e.abrupt("continue",22);case 32:if("xingbie"!=a){e.next=36;break}return this.ruleForm.xingbie=n[a],this.ro.xingbie=!0,e.abrupt("continue",22);case 36:if("keshi"!=a){e.next=40;break}return this.ruleForm.keshi=n[a],this.ro.keshi=!0,e.abrupt("continue",22);case 40:if("yiling"!=a){e.next=44;break}return this.ruleForm.yiling=n[a],this.ro.yiling=!0,e.abrupt("continue",22);case 44:if("zhaopian"!=a){e.next=48;break}return this.ruleForm.zhaopian=n[a].split(",")[0],this.ro.zhaopian=!0,e.abrupt("continue",22);case 48:if("zhicheng"!=a){e.next=52;break}return this.ruleForm.zhicheng=n[a],this.ro.zhicheng=!0,e.abrupt("continue",22);case 52:if("shanzhang"!=a){e.next=56;break}return this.ruleForm.shanzhang=n[a],this.ro.shanzhang=!0,e.abrupt("continue",22);case 56:if("lianxifangshi"!=a){e.next=60;break}return this.ruleForm.lianxifangshi=n[a],this.ro.lianxifangshi=!0,e.abrupt("continue",22);case 60:if("zuozhenshijian"!=a){e.next=64;break}return this.ruleForm.zuozhenshijian=n[a],this.ro.zuozhenshijian=!0,e.abrupt("continue",22);case 64:if("discussnum"!=a){e.next=68;break}return this.ruleForm.discussnum=n[a],this.ro.discussnum=!0,e.abrupt("continue",22);case 68:if("storeupnum"!=a){e.next=72;break}return this.ruleForm.storeupnum=n[a],this.ro.storeupnum=!0,e.abrupt("continue",22);case 72:e.next=22;break;case 74:this.styleChange(),this.$forceUpdate(),uni.getStorageSync("raffleType")&&null!=uni.getStorageSync("raffleType")&&(uni.removeStorageSync("raffleType"),setTimeout((function(){s.onSubmitTap()}),300));case 77:case"end":return e.stop()}}),e,this)})));function i(i){return e.apply(this,arguments)}return i}(),methods:{styleChange:function(){this.$nextTick((function(){}))},keshiChange:function(e){this.keshiIndex=e.target.value,this.ruleForm.keshi=this.keshiOptions[this.keshiIndex]},zhaopianTap:function(){var e=this;this.$api.upload((function(i){e.ruleForm.zhaopian="upload/"+i.file,e.$forceUpdate(),e.$nextTick((function(){e.styleChange()}))}))},getUUID:function(){return(new Date).getTime()},onSubmitTap:function(){var e=(0,n.default)(regeneratorRuntime.mark((function e(){var i,t,r,n,a,s,o,u,l,h;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.ruleForm.yishenggonghao){e.next=3;break}return this.$utils.msg("医生工号不能为空"),e.abrupt("return");case 3:if(this.ruleForm.yishengxingming){e.next=6;break}return this.$utils.msg("医生姓名不能为空"),e.abrupt("return");case 6:if(!this.ruleForm.lianxifangshi||this.$validate.isMobile(this.ruleForm.lianxifangshi)){e.next=9;break}return this.$utils.msg("联系方式应输入手机格式"),e.abrupt("return");case 9:if(!this.ruleForm.discussnum||this.$validate.isIntNumer(this.ruleForm.discussnum)){e.next=12;break}return this.$utils.msg("评论数应输入整数"),e.abrupt("return");case 12:if(!this.ruleForm.storeupnum||this.$validate.isIntNumer(this.ruleForm.storeupnum)){e.next=15;break}return this.$utils.msg("收藏数应输入整数"),e.abrupt("return");case 15:if(!this.cross){e.next=32;break}if(uni.setStorageSync("crossCleanType",!0),a=uni.getStorageSync("statusColumnName"),s=uni.getStorageSync("statusColumnValue"),""==a){e.next=32;break}if(i||(i=uni.getStorageSync("crossObj")),a.startsWith("[")){e.next=28;break}for(o in i)o==a&&(i[o]=s);return u=uni.getStorageSync("crossTable"),e.next=26,this.$api.update("".concat(u),i);case 26:e.next=32;break;case 28:t=Number(uni.getStorageSync("appUserid")),r=i["id"],n=uni.getStorageSync("statusColumnName"),n=n.replace(/\[/,"").replace(/\]/,"");case 32:if(!r||!t){e.next=55;break}return this.ruleForm.crossuserid=t,this.ruleForm.crossrefid=r,l={page:1,limit:10,crossuserid:t,crossrefid:r},e.next=38,this.$api.list("zuozhenyisheng",l);case 38:if(h=e.sent,!(h.data.total>=n)){e.next=45;break}return this.$utils.msg(uni.getStorageSync("tips")),uni.removeStorageSync("crossCleanType"),e.abrupt("return",!1);case 45:if(!this.ruleForm.id){e.next=50;break}return e.next=48,this.$api.update("zuozhenyisheng",this.ruleForm);case 48:e.next=52;break;case 50:return e.next=52,this.$api.add("zuozhenyisheng",this.ruleForm);case 52:this.$utils.msgBack("提交成功");case 53:e.next=63;break;case 55:if(!this.ruleForm.id){e.next=60;break}return e.next=58,this.$api.update("zuozhenyisheng",this.ruleForm);case 58:e.next=62;break;case 60:return e.next=62,this.$api.add("zuozhenyisheng",this.ruleForm);case 62:this.$utils.msgBack("提交成功");case 63:case"end":return e.stop()}}),e,this)})));function i(){return e.apply(this,arguments)}return i}(),optionsChange:function(e){this.index=e.target.value},bindDateChange:function(e){this.date=e.target.value},getDate:function(e){var i=new Date,t=i.getFullYear(),r=i.getMonth()+1,n=i.getDate();return"start"===e?t-=60:"end"===e&&(t+=2),r=r>9?r:"0"+r,n=n>9?n:"0"+n,"".concat(t,"-").concat(r,"-").concat(n)},toggleTab:function(e){if(this.ro[e])return!1;this.$refs[e].show()}}};i.default=u},"6f10":function(e,i,t){"use strict";t.r(i);var r=t("6db3"),n=t.n(r);for(var a in r)"default"!==a&&function(e){t.d(i,e,(function(){return r[e]}))}(a);i["default"]=n.a},"7c49":function(e,i,t){var r=t("24fb");i=r(!1),i.push([e.i,'@charset "UTF-8";\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.content[data-v-4e99f5f6]{min-height:calc(100vh - 44px);box-sizing:border-box}',""]),e.exports=i},"7ca7":function(e,i,t){"use strict";t.r(i);var r=t("b861"),n=t("6f10");for(var a in n)"default"!==a&&function(e){t.d(i,e,(function(){return n[e]}))}(a);t("0187");var s,o=t("f0c5"),u=Object(o["a"])(n["default"],r["b"],r["c"],!1,null,"4e99f5f6",null,!1,r["a"],s);i["default"]=u.exports},b861:function(e,i,t){"use strict";var r,n=function(){var e=this,i=e.$createElement,t=e._self._c||i;return t("v-uni-view",{staticClass:"content"},[t("v-uni-view",{style:{width:"100%",padding:"0",position:"relative",background:"#fff",height:"100%"}},[t("v-uni-form",{staticClass:"app-update-pv",style:{width:"100%",padding:"24rpx",background:"#fff",display:"block",height:"auto"}},[t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("医生工号")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.yishenggonghao,placeholder:"医生工号",type:"text"},model:{value:e.ruleForm.yishenggonghao,callback:function(i){e.$set(e.ruleForm,"yishenggonghao",i)},expression:"ruleForm.yishenggonghao"}})],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("医生姓名")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.yishengxingming,placeholder:"医生姓名",type:"text"},model:{value:e.ruleForm.yishengxingming,callback:function(i){e.$set(e.ruleForm,"yishengxingming",i)},expression:"ruleForm.yishengxingming"}})],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("性别")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.xingbie,placeholder:"性别",type:"text"},model:{value:e.ruleForm.xingbie,callback:function(i){e.$set(e.ruleForm,"xingbie",i)},expression:"ruleForm.xingbie"}})],1),t("v-uni-view",{staticClass:" select",style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("科室")]),t("v-uni-picker",{style:{width:"100%",flex:"1",height:"auto"},attrs:{disabled:e.ro.keshi,value:e.keshiIndex,range:e.keshiOptions},on:{change:function(i){arguments[0]=i=e.$handleEvent(i),e.keshiChange.apply(void 0,arguments)}}},[t("v-uni-view",{staticClass:"uni-input",style:{width:"100%",lineHeight:"80rpx",fontSize:"28rpx",color:"#D4CF5D"}},[e._v(e._s(e.ruleForm.keshi?e.ruleForm.keshi:"请选择科室"))])],1)],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("医龄")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.yiling,placeholder:"医龄",type:"text"},model:{value:e.ruleForm.yiling,callback:function(i){e.$set(e.ruleForm,"yiling",i)},expression:"ruleForm.yiling"}})],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.zhaopianTap.apply(void 0,arguments)}}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("照片")]),e.ruleForm.zhaopian?t("v-uni-image",{staticClass:"avator",style:{width:"80rpx",borderRadius:"100%",objectFit:"cover",display:"block",height:"80rpx"},attrs:{src:e.baseUrl+e.ruleForm.zhaopian.split(",")[0],mode:"aspectFill"}}):t("v-uni-image",{staticClass:"avator",style:{width:"80rpx",borderRadius:"100%",objectFit:"cover",display:"block",height:"80rpx"},attrs:{src:"../../static/gen/upload.png",mode:"aspectFill"}})],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("职称")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.zhicheng,placeholder:"职称",type:"text"},model:{value:e.ruleForm.zhicheng,callback:function(i){e.$set(e.ruleForm,"zhicheng",i)},expression:"ruleForm.zhicheng"}})],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("擅长")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.shanzhang,placeholder:"擅长",type:"text"},model:{value:e.ruleForm.shanzhang,callback:function(i){e.$set(e.ruleForm,"shanzhang",i)},expression:"ruleForm.shanzhang"}})],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("联系方式")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.lianxifangshi,placeholder:"联系方式",type:"text"},model:{value:e.ruleForm.lianxifangshi,callback:function(i){e.$set(e.ruleForm,"lianxifangshi",i)},expression:"ruleForm.lianxifangshi"}})],1),t("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[t("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("坐诊时间")]),t("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.zuozhenshijian,placeholder:"坐诊时间",type:"text"},model:{value:e.ruleForm.zuozhenshijian,callback:function(i){e.$set(e.ruleForm,"zuozhenshijian",i)},expression:"ruleForm.zuozhenshijian"}})],1),t("v-uni-view",{staticClass:"btn",style:{width:"100%",alignItems:"center",justifyContent:"center",display:"flex",height:"auto"}},[t("v-uni-button",{staticClass:"bg-red",style:{border:"0",padding:"0px",margin:"0",color:"rgb(255, 255, 255)",background:"#D4CF5D",width:"48%",lineHeight:"80rpx",fontSize:"28rpx",height:"80rpx"},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.onSubmitTap.apply(void 0,arguments)}}},[e._v("提交")])],1)],1)],1)],1)},a=[];t.d(i,"b",(function(){return n})),t.d(i,"c",(function(){return a})),t.d(i,"a",(function(){return r}))}}]);