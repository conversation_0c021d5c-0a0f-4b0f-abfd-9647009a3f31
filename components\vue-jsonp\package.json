{"_from": "vue-jsonp", "_id": "vue-jsonp@2.0.0", "_inBundle": false, "_integrity": "sha512-Mzd9GNeuKP5hHFDWZNMWOsCuMILSkA6jo2l4A02wheFz3qqBzH7aSEFTey1BRCZCLizlaf1EqJ5YUtF392KspA==", "_location": "/vue-jsonp", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "vue-jsonp", "name": "vue-jsonp", "escapedName": "vue-jsonp", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/vue-jsonp/-/vue-jsonp-2.0.0.tgz", "_shasum": "3bfac56bb72941a2511c11e1a123b876f03427f7", "_spec": "vue-jsonp", "_where": "C:\\Users\\<USER>\\Downloads\\一搏测试单 (10)\\ssm03fc1\\src\\main\\webapp\\front", "author": {"name": "Lancer<PERSON><PERSON>t", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/LancerComet/vue-jsonp/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A tiny library for handling JSONP request.", "devDependencies": {"@types/expect-puppeteer": "^4.4.3", "@types/jest": "^26.0.14", "@types/jest-environment-puppeteer": "^4.4.0", "@types/puppeteer": "^3.0.2", "jest": "^26.4.2", "jest-puppeteer": "^4.4.0", "puppeteer": "^5.3.1", "rollup": "^2.28.2", "rollup-plugin-cleanup": "^3.2.1", "rollup-plugin-delete": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.27.3", "ts-jest": "^26.4.1", "tslint": "^6.1.3", "typescript": "^4.0.3", "vue": "^2.6.12"}, "files": ["dist/", "index.d.ts", "README.md"], "homepage": "https://github.com/LancerComet/vue-jsonp#readme", "keywords": ["<PERSON><PERSON>", "JSONP"], "license": "MIT", "main": "./dist/index.js", "module": "./dist/index.esm.js", "name": "vue-jsonp", "repository": {"type": "git", "url": "git+https://github.com/LancerComet/vue-jsonp.git"}, "scripts": {"build": "rollup -c", "prepublish": "npm run test", "pretest": "npm run build", "preversion": "npm run test", "test": "jest"}, "version": "2.0.0"}