(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-login-login"],{"03a7":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */uni-page-body[data-v-943af160]{height:100%}.content[data-v-943af160]{height:100%;box-sizing:border-box}',""]),t.exports=e},3285:function(t,e,n){"use strict";n.r(e);var i=n("6263"),a=n.n(i);for(var r in i)"default"!==r&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},"3f43":function(t,e,n){"use strict";var i,a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"content"},[n("v-uni-view",{staticClass:"box",style:{width:"100%",padding:"160rpx 0 0 0",backgroundSize:"100% 100%",position:"relative",backgroundImage:"url(http://codegen.caihongy.cn/20231129/8a45413f19f5441ab00f4650d3f7d43e.png)",height:"100%"}},[n("v-uni-view",{style:{width:"100%",padding:"40rpx 100rpx",display:"block",height:"auto"}},[n("v-uni-image",{style:{width:"160rpx",margin:"0 auto 24rpx auto",borderRadius:"8rpx",display:"none",height:"160rpx"},attrs:{src:"http://codegen.caihongy.cn/20201114/7856ba26477849ea828f481fa2773a95.jpg",mode:"aspectFill"}}),1==t.loginType?n("v-uni-view",{staticClass:"uni-form-item uni-column",style:{width:"100%",margin:"0",flexDirection:"column",display:"flex",height:"auto"}},[n("v-uni-view",{staticClass:"label",style:{width:"100%",lineHeight:"88rpx",fontSize:"28rpx",color:"#000000",textAlign:"left"}},[t._v("账号：")]),n("v-uni-input",{staticClass:"uni-input",style:{border:"0px solid rgb(255, 170, 51)",padding:"20rpx",margin:"0 0 20rpx",color:"rgb(0, 0, 0)",borderRadius:"0",flex:"1",background:"#F6F6F4",fontSize:"28rpx"},attrs:{type:"text",name:"",placeholder:"请输入账号"},model:{value:t.username,callback:function(e){t.username=e},expression:"username"}})],1):t._e(),1==t.loginType?n("v-uni-view",{staticClass:"uni-form-item uni-column",style:{width:"100%",margin:"0",flexDirection:"column",display:"flex",height:"auto"}},[n("v-uni-view",{staticClass:"label",style:{width:"100%",lineHeight:"88rpx",fontSize:"28rpx",color:"#000000",textAlign:"left"}},[t._v("密码：")]),n("v-uni-input",{staticClass:"uni-input",style:{border:"0px solid rgb(255, 170, 51)",padding:"20rpx",margin:"0 0 20rpx",color:"rgb(0, 0, 0)",borderRadius:"0",flex:"1",background:"#F6F6F4",fontSize:"28rpx"},attrs:{type:"password",name:"",placeholder:"请输入密码"},model:{value:t.password,callback:function(e){t.password=e},expression:"password"}})],1):t._e(),t.roleNum>1?n("v-uni-view",{style:{width:"100%",margin:"0 0 24rpx 0",flexWrap:"wrap",display:"flex",height:"auto"}},[n("v-uni-view",{staticClass:"label",style:{width:"100%",lineHeight:"88rpx",fontSize:"28rpx",color:"#000000",textAlign:"left"}},[t._v("用户类型：")]),n("v-uni-picker",{style:{width:"100%",lineHeight:"88rpx",fontSize:"28rpx",color:"#D4CF5D"},attrs:{value:t.index,range:t.options},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.optionsChange.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"uni-picker-type"},[t._v(t._s(t.options[t.index]))])],1)],1):t._e(),1==t.loginType?n("v-uni-button",{staticClass:"btn-submit",style:{border:"0",padding:"0px",margin:"0 0 24rpx 0",color:"rgb(255, 255, 255)",borderRadius:"0",background:"#D4CF5D",width:"100%",lineHeight:"88rpx",fontSize:"40rpx",height:"88rpx"},attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoginTap.apply(void 0,arguments)}}},[t._v("登录")]):t._e(),2==t.loginType?n("v-uni-button",{staticClass:"btn-submit",style:{border:"0",padding:"0px",margin:"0 0 24rpx 0",color:"rgb(255, 255, 255)",borderRadius:"0",background:"#D4CF5D",width:"100%",lineHeight:"88rpx",fontSize:"40rpx",height:"88rpx"},attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onFaceLoginTap.apply(void 0,arguments)}}},[t._v("人脸识别登录")]):t._e(),n("v-uni-view",{staticClass:"links",style:{width:"100%",margin:"0 0 24rpx 0",flexWrap:"wrap",display:"flex",height:"auto"}},[n("v-uni-view",{staticClass:"link-highlight",style:{color:"#999",padding:"8rpx",fontSize:"28rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onRegisterTap("yonghu")}}},[t._v("注册用户")])],1),n("v-uni-view",{staticClass:"idea1",style:{color:"#000000",top:"-80rpx",display:"none",width:"100%",fontSize:"72rpx",position:"absolute",height:"80rpx"}},[t._v("账号登录")]),n("v-uni-view",{staticClass:"idea2",style:{width:"100%",background:"red",display:"none",height:"80rpx"}},[t._v("idea2")]),n("v-uni-view",{staticClass:"idea3",style:{width:"100%",background:"red",display:"none",height:"80rpx"}},[t._v("idea3")])],1)],1)],1)},r=[];n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return i}))},6263:function(t,e,n){"use strict";var i=n("4ea4");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("96cf");var a=i(n("3b8d")),r=i(n("2971")),s={data:function(){return{username:"",password:"",loginType:1,codes:[{num:1,color:"#000",rotate:"10deg",size:"16px"},{num:2,color:"#000",rotate:"10deg",size:"16px"},{num:3,color:"#000",rotate:"10deg",size:"16px"},{num:4,color:"#000",rotate:"10deg",size:"16px"}],options:["请选择登录用户类型"],optionsValues:["","yonghu"],index:0,roleNum:0}},onLoad:function(){var t=["请选择登录用户类型"],e=r.default.list();this.menuList=e;for(var n=0;n<this.menuList.length;n++)"是"==this.menuList[n].hasFrontLogin&&(t.push(this.menuList[n].roleName),this.roleNum++);1==this.roleNum&&(this.index=1),this.options=t,this.styleChange()},onShow:function(){},mounted:function(){},methods:{styleChange:function(){this.$nextTick((function(){}))},onRegisterTap:function(t){uni.setStorageSync("loginTable",t),this.$utils.jump("../register/register")},onLoginTap:function(){var t=(0,a.default)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.username){t.next=3;break}return this.$utils.msg("请输入用户名"),t.abrupt("return");case 3:if(this.password){t.next=6;break}return this.$utils.msg("请输入用户密码"),t.abrupt("return");case 6:if(this.optionsValues[this.index]){t.next=9;break}return this.$utils.msg("请选择登录用户类型"),t.abrupt("return");case 9:this.loginPost();case 10:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),loginPost:function(){var t=(0,a.default)(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.$api.login("".concat(this.optionsValues[this.index]),{username:this.username,password:this.password});case 2:return e=t.sent,uni.removeStorageSync("useridTag"),uni.setStorageSync("appToken",e.token),uni.setStorageSync("nickname",this.username),uni.setStorageSync("nowTable","".concat(this.optionsValues[this.index])),t.next=9,this.$api.session("".concat(this.optionsValues[this.index]));case 9:e=t.sent,e.data.touxiang?uni.setStorageSync("headportrait",e.data.touxiang):e.data.headportrait&&uni.setStorageSync("headportrait",e.data.headportrait),uni.setStorageSync("userSession",JSON.stringify(e.data)),uni.setStorageSync("appUserid",e.data.id),e.data.vip&&uni.setStorageSync("vip",e.data.vip),uni.setStorageSync("appRole","".concat(this.options[this.index])),this.$utils.tab("../index/index");case 16:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),optionsChange:function(t){this.index=t.target.value}}};e.default=s},8483:function(t,e,n){var i=n("03a7");"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("5208ce6c",i,!0,{sourceMap:!1,shadowMode:!1})},a86a:function(t,e,n){"use strict";n.r(e);var i=n("3f43"),a=n("3285");for(var r in a)"default"!==r&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("ed88");var s,o=n("f0c5"),u=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"943af160",null,!1,i["a"],s);e["default"]=u.exports},ed88:function(t,e,n){"use strict";var i=n("8483"),a=n.n(i);a.a}}]);