(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-yaopinleixing-add-or-update"],{"0c80":function(e,t,n){"use strict";var r,i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"content"},[n("v-uni-view",{style:{width:"100%",padding:"0",position:"relative",background:"#fff",height:"100%"}},[n("v-uni-form",{staticClass:"app-update-pv",style:{width:"100%",padding:"24rpx",background:"#fff",display:"block",height:"auto"}},[n("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[n("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("药品类型")]),n("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.yaopinleixing,placeholder:"药品类型",type:"text"},model:{value:e.ruleForm.yaopinleixing,callback:function(t){e.$set(e.ruleForm,"yaopinleixing",t)},expression:"ruleForm.yaopinleixing"}})],1),n("v-uni-view",{staticClass:"btn",style:{width:"100%",alignItems:"center",justifyContent:"center",display:"flex",height:"auto"}},[n("v-uni-button",{staticClass:"bg-red",style:{border:"0",padding:"0px",margin:"0",color:"rgb(255, 255, 255)",background:"#D4CF5D",width:"48%",lineHeight:"80rpx",fontSize:"28rpx",height:"80rpx"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onSubmitTap.apply(void 0,arguments)}}},[e._v("提交")])],1)],1)],1)],1)},a=[];n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return r}))},"3e0e":function(e,t,n){var r=n("5350");"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var i=n("4f06").default;i("8b803e58",r,!0,{sourceMap:!1,shadowMode:!1})},5350:function(e,t,n){var r=n("24fb");t=r(!1),t.push([e.i,'@charset "UTF-8";\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.content[data-v-7690ddba]{min-height:calc(100vh - 44px);box-sizing:border-box}',""]),e.exports=t},"5e97":function(e,t,n){"use strict";var r=n("4ea4");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("a481"),n("f559"),n("ac6a"),n("c5f6"),n("96cf");var i=r(n("3b8d")),a=r(n("e2b1")),s=r(n("064f")),o=r(n("bd56")),u={data:function(){return{cross:"",ruleForm:{yaopinleixing:""},user:{},ro:{yaopinleixing:!1}}},components:{wPicker:a.default,xiaEditor:s.default,multipleSelect:o.default},computed:{baseUrl:function(){return this.$base.url}},onLoad:function(){var e=(0,i.default)(regeneratorRuntime.mark((function e(t){var n,r,i,a,s=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=uni.getStorageSync("nowTable"),e.next=3,this.$api.session(n);case 3:if(r=e.sent,this.user=r.data,this.ruleForm.userid=uni.getStorageSync("appUserid"),t.refid&&(this.ruleForm.refid=Number(t.refid),this.ruleForm.nickname=uni.getStorageSync("nickname")),!t.id){e.next=13;break}return this.ruleForm.id=t.id,e.next=11,this.$api.info("yaopinleixing",this.ruleForm.id);case 11:r=e.sent,this.ruleForm=r.data;case 13:if(this.cross=t.cross,!t.cross){e.next=25;break}i=uni.getStorageSync("crossObj"),e.t0=regeneratorRuntime.keys(i);case 17:if((e.t1=e.t0()).done){e.next=25;break}if(a=e.t1.value,"yaopinleixing"!=a){e.next=23;break}return this.ruleForm.yaopinleixing=i[a],this.ro.yaopinleixing=!0,e.abrupt("continue",17);case 23:e.next=17;break;case 25:this.styleChange(),this.$forceUpdate(),uni.getStorageSync("raffleType")&&null!=uni.getStorageSync("raffleType")&&(uni.removeStorageSync("raffleType"),setTimeout((function(){s.onSubmitTap()}),300));case 28:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),methods:{styleChange:function(){this.$nextTick((function(){}))},getUUID:function(){return(new Date).getTime()},onSubmitTap:function(){var e=(0,i.default)(regeneratorRuntime.mark((function e(){var t,n,r,i,a,s,o,u,c,l;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.ruleForm.yaopinleixing){e.next=3;break}return this.$utils.msg("药品类型不能为空"),e.abrupt("return");case 3:if(!this.cross){e.next=20;break}if(uni.setStorageSync("crossCleanType",!0),a=uni.getStorageSync("statusColumnName"),s=uni.getStorageSync("statusColumnValue"),""==a){e.next=20;break}if(t||(t=uni.getStorageSync("crossObj")),a.startsWith("[")){e.next=16;break}for(o in t)o==a&&(t[o]=s);return u=uni.getStorageSync("crossTable"),e.next=14,this.$api.update("".concat(u),t);case 14:e.next=20;break;case 16:n=Number(uni.getStorageSync("appUserid")),r=t["id"],i=uni.getStorageSync("statusColumnName"),i=i.replace(/\[/,"").replace(/\]/,"");case 20:if(!r||!n){e.next=43;break}return this.ruleForm.crossuserid=n,this.ruleForm.crossrefid=r,c={page:1,limit:10,crossuserid:n,crossrefid:r},e.next=26,this.$api.list("yaopinleixing",c);case 26:if(l=e.sent,!(l.data.total>=i)){e.next=33;break}return this.$utils.msg(uni.getStorageSync("tips")),uni.removeStorageSync("crossCleanType"),e.abrupt("return",!1);case 33:if(!this.ruleForm.id){e.next=38;break}return e.next=36,this.$api.update("yaopinleixing",this.ruleForm);case 36:e.next=40;break;case 38:return e.next=40,this.$api.add("yaopinleixing",this.ruleForm);case 40:this.$utils.msgBack("提交成功");case 41:e.next=51;break;case 43:if(!this.ruleForm.id){e.next=48;break}return e.next=46,this.$api.update("yaopinleixing",this.ruleForm);case 46:e.next=50;break;case 48:return e.next=50,this.$api.add("yaopinleixing",this.ruleForm);case 50:this.$utils.msgBack("提交成功");case 51:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),optionsChange:function(e){this.index=e.target.value},bindDateChange:function(e){this.date=e.target.value},getDate:function(e){var t=new Date,n=t.getFullYear(),r=t.getMonth()+1,i=t.getDate();return"start"===e?n-=60:"end"===e&&(n+=2),r=r>9?r:"0"+r,i=i>9?i:"0"+i,"".concat(n,"-").concat(r,"-").concat(i)},toggleTab:function(e){if(this.ro[e])return!1;this.$refs[e].show()}}};t.default=u},"75b0":function(e,t,n){"use strict";var r=n("3e0e"),i=n.n(r);i.a},c2cb:function(e,t,n){"use strict";n.r(t);var r=n("0c80"),i=n("dfe4");for(var a in i)"default"!==a&&function(e){n.d(t,e,(function(){return i[e]}))}(a);n("75b0");var s,o=n("f0c5"),u=Object(o["a"])(i["default"],r["b"],r["c"],!1,null,"7690ddba",null,!1,r["a"],s);t["default"]=u.exports},dfe4:function(e,t,n){"use strict";n.r(t);var r=n("5e97"),i=n.n(r);for(var a in r)"default"!==a&&function(e){n.d(t,e,(function(){return r[e]}))}(a);t["default"]=i.a}}]);