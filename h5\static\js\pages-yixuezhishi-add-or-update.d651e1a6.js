(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-yixuezhishi-add-or-update"],{"1d0e":function(e,t,i){var r=i("3f32");"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var n=i("4f06").default;n("e6993902",r,!0,{sourceMap:!1,shadowMode:!1})},"22d6":function(e,t,i){"use strict";var r=i("1d0e"),n=i.n(r);n.a},"282c":function(e,t,i){"use strict";i.r(t);var r=i("ac0a"),n=i("d6e0");for(var a in n)"default"!==a&&function(e){i.d(t,e,(function(){return n[e]}))}(a);i("22d6");var o,s=i("f0c5"),u=Object(s["a"])(n["default"],r["b"],r["c"],!1,null,"32c1674a",null,!1,r["a"],o);t["default"]=u.exports},"2e8b":function(e,t,i){"use strict";var r=i("4ea4");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a481"),i("f559"),i("ac6a"),i("28a5"),i("c5f6"),i("96cf");var n=r(i("3b8d")),a=r(i("e2b1")),o=r(i("064f")),s=r(i("bd56")),u={data:function(){return{cross:"",ruleForm:{biaoti:"",neirong:"",tupian:"",shangchuanshijian:"",yishenggonghao:"",yishengxingming:""},user:{},ro:{biaoti:!1,neirong:!1,tupian:!1,shangchuanshijian:!1,yishenggonghao:!1,yishengxingming:!1}}},components:{wPicker:a.default,xiaEditor:o.default,multipleSelect:s.default},computed:{baseUrl:function(){return this.$base.url}},onLoad:function(){var e=(0,n.default)(regeneratorRuntime.mark((function e(t){var i,r,n,a,o=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.ruleForm.shangchuanshijian=this.$utils.getCurDateTime(),i=uni.getStorageSync("nowTable"),e.next=4,this.$api.session(i);case 4:if(r=e.sent,this.user=r.data,this.ruleForm.yishenggonghao=this.user.yishenggonghao,this.ro.yishenggonghao=!0,this.ruleForm.yishengxingming=this.user.yishengxingming,this.ro.yishengxingming=!0,this.ruleForm.userid=uni.getStorageSync("appUserid"),t.refid&&(this.ruleForm.refid=Number(t.refid),this.ruleForm.nickname=uni.getStorageSync("nickname")),!t.id){e.next=18;break}return this.ruleForm.id=t.id,e.next=16,this.$api.info("yixuezhishi",this.ruleForm.id);case 16:r=e.sent,this.ruleForm=r.data;case 18:if(this.cross=t.cross,!t.cross){e.next=50;break}n=uni.getStorageSync("crossObj"),e.t0=regeneratorRuntime.keys(n);case 22:if((e.t1=e.t0()).done){e.next=50;break}if(a=e.t1.value,"biaoti"!=a){e.next=28;break}return this.ruleForm.biaoti=n[a],this.ro.biaoti=!0,e.abrupt("continue",22);case 28:if("neirong"!=a){e.next=32;break}return this.ruleForm.neirong=n[a],this.ro.neirong=!0,e.abrupt("continue",22);case 32:if("tupian"!=a){e.next=36;break}return this.ruleForm.tupian=n[a].split(",")[0],this.ro.tupian=!0,e.abrupt("continue",22);case 36:if("shangchuanshijian"!=a){e.next=40;break}return this.ruleForm.shangchuanshijian=n[a],this.ro.shangchuanshijian=!0,e.abrupt("continue",22);case 40:if("yishenggonghao"!=a){e.next=44;break}return this.ruleForm.yishenggonghao=n[a],this.ro.yishenggonghao=!0,e.abrupt("continue",22);case 44:if("yishengxingming"!=a){e.next=48;break}return this.ruleForm.yishengxingming=n[a],this.ro.yishengxingming=!0,e.abrupt("continue",22);case 48:e.next=22;break;case 50:this.styleChange(),this.$forceUpdate(),uni.getStorageSync("raffleType")&&null!=uni.getStorageSync("raffleType")&&(uni.removeStorageSync("raffleType"),setTimeout((function(){o.onSubmitTap()}),300));case 53:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),methods:{styleChange:function(){this.$nextTick((function(){}))},shangchuanshijianConfirm:function(e){console.log(e),this.ruleForm.shangchuanshijian=e.result,this.$forceUpdate()},tupianTap:function(){var e=this;this.$api.upload((function(t){e.ruleForm.tupian="upload/"+t.file,e.$forceUpdate(),e.$nextTick((function(){e.styleChange()}))}))},getUUID:function(){return(new Date).getTime()},onSubmitTap:function(){var e=(0,n.default)(regeneratorRuntime.mark((function e(){var t,i,r,n,a,o,s,u,l,c;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!this.cross){e.next=17;break}if(uni.setStorageSync("crossCleanType",!0),a=uni.getStorageSync("statusColumnName"),o=uni.getStorageSync("statusColumnValue"),""==a){e.next=17;break}if(t||(t=uni.getStorageSync("crossObj")),a.startsWith("[")){e.next=13;break}for(s in t)s==a&&(t[s]=o);return u=uni.getStorageSync("crossTable"),e.next=11,this.$api.update("".concat(u),t);case 11:e.next=17;break;case 13:i=Number(uni.getStorageSync("appUserid")),r=t["id"],n=uni.getStorageSync("statusColumnName"),n=n.replace(/\[/,"").replace(/\]/,"");case 17:if(!r||!i){e.next=40;break}return this.ruleForm.crossuserid=i,this.ruleForm.crossrefid=r,l={page:1,limit:10,crossuserid:i,crossrefid:r},e.next=23,this.$api.list("yixuezhishi",l);case 23:if(c=e.sent,!(c.data.total>=n)){e.next=30;break}return this.$utils.msg(uni.getStorageSync("tips")),uni.removeStorageSync("crossCleanType"),e.abrupt("return",!1);case 30:if(!this.ruleForm.id){e.next=35;break}return e.next=33,this.$api.update("yixuezhishi",this.ruleForm);case 33:e.next=37;break;case 35:return e.next=37,this.$api.add("yixuezhishi",this.ruleForm);case 37:this.$utils.msgBack("提交成功");case 38:e.next=48;break;case 40:if(!this.ruleForm.id){e.next=45;break}return e.next=43,this.$api.update("yixuezhishi",this.ruleForm);case 43:e.next=47;break;case 45:return e.next=47,this.$api.add("yixuezhishi",this.ruleForm);case 47:this.$utils.msgBack("提交成功");case 48:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),optionsChange:function(e){this.index=e.target.value},bindDateChange:function(e){this.date=e.target.value},getDate:function(e){var t=new Date,i=t.getFullYear(),r=t.getMonth()+1,n=t.getDate();return"start"===e?i-=60:"end"===e&&(i+=2),r=r>9?r:"0"+r,n=n>9?n:"0"+n,"".concat(i,"-").concat(r,"-").concat(n)},toggleTab:function(e){if(this.ro[e])return!1;this.$refs[e].show()}}};t.default=u},"3f32":function(e,t,i){var r=i("24fb");t=r(!1),t.push([e.i,'@charset "UTF-8";\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.content[data-v-32c1674a]{min-height:calc(100vh - 44px);box-sizing:border-box}',""]),e.exports=t},ac0a:function(e,t,i){"use strict";var r={"w-picker":i("e2b1").default},n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"content"},[i("v-uni-view",{style:{width:"100%",padding:"0",position:"relative",background:"#fff",height:"100%"}},[i("v-uni-form",{staticClass:"app-update-pv",style:{width:"100%",padding:"24rpx",background:"#fff",display:"block",height:"auto"}},[i("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("标题")]),i("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.biaoti,placeholder:"标题",type:"text"},model:{value:e.ruleForm.biaoti,callback:function(t){e.$set(e.ruleForm,"biaoti",t)},expression:"ruleForm.biaoti"}})],1),i("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.tupianTap.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("图片")]),e.ruleForm.tupian?i("v-uni-image",{staticClass:"avator",style:{width:"80rpx",borderRadius:"100%",objectFit:"cover",display:"block",height:"80rpx"},attrs:{src:e.baseUrl+e.ruleForm.tupian.split(",")[0],mode:"aspectFill"}}):i("v-uni-image",{staticClass:"avator",style:{width:"80rpx",borderRadius:"100%",objectFit:"cover",display:"block",height:"80rpx"},attrs:{src:"../../static/gen/upload.png",mode:"aspectFill"}})],1),i("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("上传时间")]),i("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.shangchuanshijian,placeholder:"上传时间"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleTab("shangchuanshijian")}},model:{value:e.ruleForm.shangchuanshijian,callback:function(t){e.$set(e.ruleForm,"shangchuanshijian",t)},expression:"ruleForm.shangchuanshijian"}})],1),i("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("医生工号")]),i("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.yishenggonghao,placeholder:"医生工号",type:"text"},model:{value:e.ruleForm.yishenggonghao,callback:function(t){e.$set(e.ruleForm,"yishenggonghao",t)},expression:"ruleForm.yishenggonghao"}})],1),i("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("医生姓名")]),i("v-uni-input",{style:{border:"0",padding:"0px 24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"80rpx"},attrs:{disabled:e.ro.yishengxingming,placeholder:"医生姓名",type:"text"},model:{value:e.ruleForm.yishengxingming,callback:function(t){e.$set(e.ruleForm,"yishengxingming",t)},expression:"ruleForm.yishengxingming"}})],1),i("v-uni-view",{style:{padding:"12rpx 0",margin:"0 0 24rpx 0",borderColor:"#ccc",alignItems:"center",borderWidth:"0 0 2rpx 0",display:"flex",width:"100%",borderStyle:"solid",height:"auto"}},[i("v-uni-view",{staticClass:"title",style:{width:"auto",padding:"0 20rpx",lineHeight:"80rpx",fontSize:"28rpx",color:"#333",textAlign:"right"}},[e._v("内容")]),i("v-uni-textarea",{style:{border:"0",padding:"24rpx",margin:"0px",color:"rgb(0, 0, 0)",borderRadius:"8rpx",flex:"1",background:"rgba(255, 255, 255, 0)",fontSize:"28rpx",height:"300rpx"},attrs:{placeholder:"内容"},model:{value:e.ruleForm.neirong,callback:function(t){e.$set(e.ruleForm,"neirong",t)},expression:"ruleForm.neirong"}})],1),i("v-uni-view",{staticClass:"btn",style:{width:"100%",alignItems:"center",justifyContent:"center",display:"flex",height:"auto"}},[i("v-uni-button",{staticClass:"bg-red",style:{border:"0",padding:"0px",margin:"0",color:"rgb(255, 255, 255)",background:"#D4CF5D",width:"48%",lineHeight:"80rpx",fontSize:"28rpx",height:"80rpx"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onSubmitTap.apply(void 0,arguments)}}},[e._v("提交")])],1)],1),i("w-picker",{ref:"shangchuanshijian",attrs:{mode:"dateTime",step:"1",current:!1,hasSecond:!1,themeColor:"#333333"},on:{confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.shangchuanshijianConfirm.apply(void 0,arguments)}}})],1)],1)},a=[];i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return a})),i.d(t,"a",(function(){return r}))},d6e0:function(e,t,i){"use strict";i.r(t);var r=i("2e8b"),n=i.n(r);for(var a in r)"default"!==a&&function(e){i.d(t,e,(function(){return r[e]}))}(a);t["default"]=n.a}}]);