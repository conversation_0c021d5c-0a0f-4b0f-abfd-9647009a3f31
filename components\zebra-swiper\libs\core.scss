$themeColor: #007aff !default;

:root {
  --swiper-theme-color: #{$themeColor};
}
.swiper {
  margin-left: auto;
  margin-right: auto;
  position: relative;
  overflow: hidden;
  list-style: none;
  padding: 0;
  /* Fix of Webkit flickering */
  z-index: 1;
}
.swiper-vertical > .swiper-wrapper {
  flex-direction: column;
}
.swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  transition-property: transform;
  box-sizing: content-box;
}
.swiper-android .swiper-slide,
.swiper-wrapper {
  transform: translate3d(0px, 0, 0);
}
.swiper-pointer-events {
  touch-action: pan-y;
  &.swiper-vertical {
    touch-action: pan-x;
  }
}
.swiper-slide {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative;
  transition-property: transform;
}
.swiper-slide-invisible-blank {
  visibility: hidden;
}
/* Auto Height */
.swiper-autoheight {
  &,
  .swiper-slide {
    height: auto;
  }

  .swiper-wrapper {
    align-items: flex-start;
    transition-property: transform, height;
  }
}
  .swiper-slide-3d{
	   transform-style: preserve-3d;
  }
/* 3D Effects */
.swiper-3d {
  &,
  &.swiper-css-mode .swiper-wrapper {
    perspective: 1200px;
  }
  .swiper-wrapper,
  .swiper-slide,
  .swiper-slide-shadow,
  .swiper-slide-shadow-left,
  .swiper-slide-shadow-right,
  .swiper-slide-shadow-top,
  .swiper-slide-shadow-bottom,
  .swiper-cube-shadow {
    transform-style: preserve-3d;
  }
  .swiper-slide-shadow,
  .swiper-slide-shadow-left,
  .swiper-slide-shadow-right,
  .swiper-slide-shadow-top,
  .swiper-slide-shadow-bottom {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
  }
  .swiper-slide-shadow {
    background: rgba(0, 0, 0, 0.15);
  }
  .swiper-slide-shadow-left {
    background-image: linear-gradient(to left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  }
  .swiper-slide-shadow-right {
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  }
  .swiper-slide-shadow-top {
    background-image: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  }
  .swiper-slide-shadow-bottom {
    background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  }
}

/* CSS Mode */
.swiper-css-mode {
  > .swiper-wrapper {
    overflow: auto;
    scrollbar-width: none; /* For Firefox */
    -ms-overflow-style: none; /* For Internet Explorer and Edge */
    &::-webkit-scrollbar {
      display: none;
    }
  }
  > .swiper-wrapper > .swiper-slide {
    scroll-snap-align: start start;
  }
}
.swiper-horizontal.swiper-css-mode {
  > .swiper-wrapper {
    scroll-snap-type: x mandatory;
  }
}
.swiper-vertical.swiper-css-mode {
  > .swiper-wrapper {
    scroll-snap-type: y mandatory;
  }
}
.swiper-centered {
  > .swiper-wrapper::before {
    content: '';
    flex-shrink: 0;
    order: 9999;
  }
  &.swiper-horizontal {
    > .swiper-wrapper > .swiper-slide:first-child {
      margin-inline-start: var(--swiper-centered-offset-before);
    }
    > .swiper-wrapper::before {
      height: 100%;
      width: var(--swiper-centered-offset-after);
    }
  }
  &.swiper-vertical {
    > .swiper-wrapper > .swiper-slide:first-child {
      margin-block-start: var(--swiper-centered-offset-before);
    }
    > .swiper-wrapper::before {
      width: 100%;
      height: var(--swiper-centered-offset-after);
    }
  }

  > .swiper-wrapper > .swiper-slide {
    scroll-snap-align: center center;
  }
}

@import "../modules/effect-fade/effect-fade.scss";
@import "../modules/effect-cube/effect-cube.scss";
@import "../modules/effect-coverflow/effect-coverflow.scss";
@import "../modules/effect-flip/effect-flip.scss";
@import "../modules/effect-cards/effect-cards.scss";
@import "../modules/effect-creative/effect-creative.scss";
@import "../modules/effect-panorama/effect-panorama.scss";
@import "../modules/effect-carousel/effect-carousel.scss";
@import "../modules/navigation/navigation.scss";
@import "../modules/pagination/pagination.scss";
@import "../modules/thumbs/thumbs.scss";
@import "../modules/scrollbar/scrollbar.scss";